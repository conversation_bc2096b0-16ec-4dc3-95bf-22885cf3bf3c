[{"C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\admin\\stats\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\admin\\users\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\admin\\users\\[id]\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointment-procedures\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signin\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signout\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signup\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\clinic-settings\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\dashboard\\stats\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\health\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\healthcare-professionals\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\medical-records\\cleanup-drafts\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\medical-records\\route.ts": "15", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patient-attachments\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patient-attachments\\[id]\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\[id]\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\permissions\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\procedures\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\settings\\profile\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\settings\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\test-auth\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\user-permissions\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\user-roles\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\auth\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\admin\\layout.tsx": "28", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\admin\\page.tsx": "29", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\admin\\usuarios\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\agenda\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\configuracoes\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\layout.tsx": "33", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\pacientes\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\pacientes\\[id]\\page.tsx": "35", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx": "36", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\prontuario\\[patientId]\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx": "38", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\page.tsx": "39", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\AppointmentForm.tsx": "40", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\AppSidebar.tsx": "41", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ConfirmDialog.tsx": "42", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\DashboardLayout.tsx": "43", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ErrorBoundary.tsx": "44", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\FullCalendarView.tsx": "45", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\LandingPage.tsx": "46", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\Providers.tsx": "47", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ReactQueryProvider.tsx": "48", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\accordion.tsx": "49", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert-dialog.tsx": "50", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert.tsx": "51", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\aspect-ratio.tsx": "52", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\avatar.tsx": "53", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\badge.tsx": "54", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\breadcrumb.tsx": "55", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\button.tsx": "56", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\calendar.tsx": "57", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\card.tsx": "58", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\carousel.tsx": "59", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\chart.tsx": "60", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\checkbox.tsx": "61", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\collapsible.tsx": "62", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\command.tsx": "63", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\context-menu.tsx": "64", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\datetime-input.tsx": "65", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dialog.tsx": "66", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\drawer.tsx": "67", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dropdown-menu.tsx": "68", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\form.tsx": "69", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\hover-card.tsx": "70", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input-otp.tsx": "71", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input.tsx": "72", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\label.tsx": "73", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\menubar.tsx": "74", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\navigation-menu.tsx": "75", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\pagination.tsx": "76", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\popover.tsx": "77", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\progress.tsx": "78", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\radio-group.tsx": "79", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\resizable.tsx": "80", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\scroll-area.tsx": "81", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\select.tsx": "82", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\separator.tsx": "83", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sheet.tsx": "84", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sidebar.tsx": "85", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\skeleton.tsx": "86", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\slider.tsx": "87", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sonner.tsx": "88", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\switch.tsx": "89", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\table.tsx": "90", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tabs.tsx": "91", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\textarea.tsx": "92", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toast.tsx": "93", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toaster.tsx": "94", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle-group.tsx": "95", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle.tsx": "96", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tooltip.tsx": "97", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\use-toast.ts": "98", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-mobile.tsx": "99", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-toast.ts": "100", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useApiRequest.ts": "101", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAsyncOperation.ts": "102", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAuth.ts": "103", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAuth.tsx": "104", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\usePermissions.ts": "105", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\usePermissions.tsx": "106", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\api-client.ts": "107", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\api-utils.ts": "108", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\date-utils.ts": "109", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\encryption.ts": "110", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\permissions.ts": "111", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\request-throttle.ts": "112", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\client.ts": "113", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\server.ts": "114", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\utils.ts": "115", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\validations.ts": "116", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\middleware.ts": "117", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\types\\supabase.ts": "118", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\api\\appointments.test.ts": "119", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\components\\ui\\calendar.test.tsx": "120", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\hooks\\usePermissions.test.tsx": "121", "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\lib\\validations.test.ts": "122"}, {"size": 1713, "mtime": 1752111176572, "results": "123", "hashOfConfig": "124"}, {"size": 2541, "mtime": 1752105883926, "results": "125", "hashOfConfig": "124"}, {"size": 3083, "mtime": 1752105900626, "results": "126", "hashOfConfig": "124"}, {"size": 1902, "mtime": 1752097857642, "results": "127", "hashOfConfig": "124"}, {"size": 3997, "mtime": 1752258624712, "results": "128", "hashOfConfig": "124"}, {"size": 2610, "mtime": 1752249580078, "results": "129", "hashOfConfig": "124"}, {"size": 779, "mtime": 1751835044056, "results": "130", "hashOfConfig": "124"}, {"size": 543, "mtime": 1751835056838, "results": "131", "hashOfConfig": "124"}, {"size": 928, "mtime": 1751835050748, "results": "132", "hashOfConfig": "124"}, {"size": 3043, "mtime": 1752097911508, "results": "133", "hashOfConfig": "124"}, {"size": 1941, "mtime": 1752113522771, "results": "134", "hashOfConfig": "124"}, {"size": 647, "mtime": 1752106224224, "results": "135", "hashOfConfig": "124"}, {"size": 1497, "mtime": 1752097836661, "results": "136", "hashOfConfig": "124"}, {"size": 1463, "mtime": 1752265480780, "results": "137", "hashOfConfig": "124"}, {"size": 5365, "mtime": 1752255836417, "results": "138", "hashOfConfig": "124"}, {"size": 2660, "mtime": 1752098351566, "results": "139", "hashOfConfig": "124"}, {"size": 2469, "mtime": 1752098365335, "results": "140", "hashOfConfig": "124"}, {"size": 1594, "mtime": 1752100730900, "results": "141", "hashOfConfig": "124"}, {"size": 2085, "mtime": 1751834967142, "results": "142", "hashOfConfig": "124"}, {"size": 1677, "mtime": 1752105914354, "results": "143", "hashOfConfig": "124"}, {"size": 1514, "mtime": 1752097846358, "results": "144", "hashOfConfig": "124"}, {"size": 647, "mtime": 1752100836548, "results": "145", "hashOfConfig": "124"}, {"size": 1010, "mtime": 1752100828607, "results": "146", "hashOfConfig": "124"}, {"size": 1070, "mtime": 1752105775516, "results": "147", "hashOfConfig": "124"}, {"size": 466, "mtime": 1752098743493, "results": "148", "hashOfConfig": "124"}, {"size": 1286, "mtime": 1752100873084, "results": "149", "hashOfConfig": "124"}, {"size": 6844, "mtime": 1751836670752, "results": "150", "hashOfConfig": "124"}, {"size": 742, "mtime": 1752111207044, "results": "151", "hashOfConfig": "124"}, {"size": 7852, "mtime": 1752111161466, "results": "152", "hashOfConfig": "124"}, {"size": 18081, "mtime": 1752105858934, "results": "153", "hashOfConfig": "124"}, {"size": 27855, "mtime": 1752259440539, "results": "154", "hashOfConfig": "124"}, {"size": 26971, "mtime": 1752265038096, "results": "155", "hashOfConfig": "124"}, {"size": 207, "mtime": 1751848739693, "results": "156", "hashOfConfig": "124"}, {"size": 14543, "mtime": 1752200791632, "results": "157", "hashOfConfig": "124"}, {"size": 34057, "mtime": 1752256499161, "results": "158", "hashOfConfig": "124"}, {"size": 11481, "mtime": 1752111366392, "results": "159", "hashOfConfig": "124"}, {"size": 19595, "mtime": 1752259380761, "results": "160", "hashOfConfig": "124"}, {"size": 784, "mtime": 1751837997834, "results": "161", "hashOfConfig": "124"}, {"size": 114, "mtime": 1751836641020, "results": "162", "hashOfConfig": "124"}, {"size": 34614, "mtime": 1752256040598, "results": "163", "hashOfConfig": "124"}, {"size": 3501, "mtime": 1752106007592, "results": "164", "hashOfConfig": "124"}, {"size": 1552, "mtime": 1752256092551, "results": "165", "hashOfConfig": "124"}, {"size": 1467, "mtime": 1751848851423, "results": "166", "hashOfConfig": "124"}, {"size": 3955, "mtime": 1752099010034, "results": "167", "hashOfConfig": "124"}, {"size": 14668, "mtime": 1752258910564, "results": "168", "hashOfConfig": "124"}, {"size": 13449, "mtime": 1751836560647, "results": "169", "hashOfConfig": "124"}, {"size": 738, "mtime": 1752106269028, "results": "170", "hashOfConfig": "124"}, {"size": 494, "mtime": 1751836596110, "results": "171", "hashOfConfig": "124"}, {"size": 2033, "mtime": 1751818663653, "results": "172", "hashOfConfig": "124"}, {"size": 4559, "mtime": 1751818663655, "results": "173", "hashOfConfig": "124"}, {"size": 1643, "mtime": 1751818663655, "results": "174", "hashOfConfig": "124"}, {"size": 145, "mtime": 1751818663657, "results": "175", "hashOfConfig": "124"}, {"size": 1453, "mtime": 1751818663657, "results": "176", "hashOfConfig": "124"}, {"size": 1164, "mtime": 1751818663657, "results": "177", "hashOfConfig": "124"}, {"size": 2816, "mtime": 1751818663659, "results": "178", "hashOfConfig": "124"}, {"size": 2235, "mtime": 1751818663659, "results": "179", "hashOfConfig": "124"}, {"size": 25308, "mtime": 1752196203171, "results": "180", "hashOfConfig": "124"}, {"size": 1956, "mtime": 1751818663659, "results": "181", "hashOfConfig": "124"}, {"size": 6470, "mtime": 1751818663659, "results": "182", "hashOfConfig": "124"}, {"size": 10829, "mtime": 1751818663659, "results": "183", "hashOfConfig": "124"}, {"size": 1084, "mtime": 1751818663665, "results": "184", "hashOfConfig": "124"}, {"size": 324, "mtime": 1751818663665, "results": "185", "hashOfConfig": "124"}, {"size": 5032, "mtime": 1751818663666, "results": "186", "hashOfConfig": "124"}, {"size": 7444, "mtime": 1751818663667, "results": "187", "hashOfConfig": "124"}, {"size": 5426, "mtime": 1752241634888, "results": "188", "hashOfConfig": "124"}, {"size": 3955, "mtime": 1751818663667, "results": "189", "hashOfConfig": "124"}, {"size": 3123, "mtime": 1751818663669, "results": "190", "hashOfConfig": "124"}, {"size": 7493, "mtime": 1751818663669, "results": "191", "hashOfConfig": "124"}, {"size": 4261, "mtime": 1751818663669, "results": "192", "hashOfConfig": "124"}, {"size": 1211, "mtime": 1751818663669, "results": "193", "hashOfConfig": "124"}, {"size": 2223, "mtime": 1751818663672, "results": "194", "hashOfConfig": "124"}, {"size": 813, "mtime": 1751818663673, "results": "195", "hashOfConfig": "124"}, {"size": 734, "mtime": 1751818663674, "results": "196", "hashOfConfig": "124"}, {"size": 8208, "mtime": 1751818663674, "results": "197", "hashOfConfig": "124"}, {"size": 5174, "mtime": 1751818663674, "results": "198", "hashOfConfig": "124"}, {"size": 2868, "mtime": 1751818663674, "results": "199", "hashOfConfig": "124"}, {"size": 1259, "mtime": 1751818663678, "results": "200", "hashOfConfig": "124"}, {"size": 803, "mtime": 1751818663678, "results": "201", "hashOfConfig": "124"}, {"size": 1509, "mtime": 1751818663678, "results": "202", "hashOfConfig": "124"}, {"size": 1752, "mtime": 1751818663678, "results": "203", "hashOfConfig": "124"}, {"size": 1688, "mtime": 1751818663678, "results": "204", "hashOfConfig": "124"}, {"size": 5773, "mtime": 1751818663681, "results": "205", "hashOfConfig": "124"}, {"size": 785, "mtime": 1751818663682, "results": "206", "hashOfConfig": "124"}, {"size": 4381, "mtime": 1751818663683, "results": "207", "hashOfConfig": "124"}, {"size": 24128, "mtime": 1751818663683, "results": "208", "hashOfConfig": "124"}, {"size": 276, "mtime": 1751818663685, "results": "209", "hashOfConfig": "124"}, {"size": 1103, "mtime": 1751818663685, "results": "210", "hashOfConfig": "124"}, {"size": 923, "mtime": 1751818663685, "results": "211", "hashOfConfig": "124"}, {"size": 1166, "mtime": 1751818663688, "results": "212", "hashOfConfig": "124"}, {"size": 2882, "mtime": 1751818663689, "results": "213", "hashOfConfig": "124"}, {"size": 1936, "mtime": 1751818663690, "results": "214", "hashOfConfig": "124"}, {"size": 796, "mtime": 1751818663691, "results": "215", "hashOfConfig": "124"}, {"size": 4972, "mtime": 1751818663693, "results": "216", "hashOfConfig": "124"}, {"size": 805, "mtime": 1751818663694, "results": "217", "hashOfConfig": "124"}, {"size": 1798, "mtime": 1751818663695, "results": "218", "hashOfConfig": "124"}, {"size": 1478, "mtime": 1751818663697, "results": "219", "hashOfConfig": "124"}, {"size": 1173, "mtime": 1751818663698, "results": "220", "hashOfConfig": "124"}, {"size": 85, "mtime": 1751818663699, "results": "221", "hashOfConfig": "124"}, {"size": 584, "mtime": 1751818663699, "results": "222", "hashOfConfig": "124"}, {"size": 4086, "mtime": 1751818663699, "results": "223", "hashOfConfig": "124"}, {"size": 4972, "mtime": 1752111316879, "results": "224", "hashOfConfig": "124"}, {"size": 3186, "mtime": 1752099027538, "results": "225", "hashOfConfig": "124"}, {"size": 3086, "mtime": 1751836462755, "results": "226", "hashOfConfig": "124"}, {"size": 3152, "mtime": 1752109988359, "results": "227", "hashOfConfig": "124"}, {"size": 3903, "mtime": 1752098734143, "results": "228", "hashOfConfig": "124"}, {"size": 4296, "mtime": 1752100995831, "results": "229", "hashOfConfig": "124"}, {"size": 847, "mtime": 1752111264049, "results": "230", "hashOfConfig": "124"}, {"size": 2615, "mtime": 1752100956965, "results": "231", "hashOfConfig": "124"}, {"size": 5171, "mtime": 1752200846882, "results": "232", "hashOfConfig": "124"}, {"size": 4282, "mtime": 1752258443545, "results": "233", "hashOfConfig": "124"}, {"size": 6002, "mtime": 1752098681781, "results": "234", "hashOfConfig": "124"}, {"size": 4595, "mtime": 1752111250308, "results": "235", "hashOfConfig": "124"}, {"size": 271, "mtime": 1751834858396, "results": "236", "hashOfConfig": "124"}, {"size": 849, "mtime": 1751834866579, "results": "237", "hashOfConfig": "124"}, {"size": 166, "mtime": 1751835083516, "results": "238", "hashOfConfig": "124"}, {"size": 12166, "mtime": 1752255984991, "results": "239", "hashOfConfig": "124"}, {"size": 2666, "mtime": 1752106312503, "results": "240", "hashOfConfig": "124"}, {"size": 20110, "mtime": 1752258699543, "results": "241", "hashOfConfig": "124"}, {"size": 7726, "mtime": 1752099596178, "results": "242", "hashOfConfig": "124"}, {"size": 3159, "mtime": 1752099561218, "results": "243", "hashOfConfig": "124"}, {"size": 6125, "mtime": 1752099538828, "results": "244", "hashOfConfig": "124"}, {"size": 5863, "mtime": 1752099509234, "results": "245", "hashOfConfig": "124"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "18sgt8c", {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\admin\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\admin\\users\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\admin\\users\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointment-procedures\\route.ts", ["612"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\route.ts", ["613", "614", "615"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\appointments\\[id]\\route.ts", ["616", "617"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signin\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signout\\route.ts", ["618"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\auth\\signup\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\clinic-settings\\route.ts", ["619"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\dashboard\\stats\\route.ts", ["620"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\health\\route.ts", ["621"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\healthcare-professionals\\route.ts", ["622"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\medical-records\\cleanup-drafts\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\medical-records\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patient-attachments\\route.ts", ["623"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patient-attachments\\[id]\\route.ts", ["624"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\route.ts", ["625", "626"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\patients\\[id]\\route.ts", ["627"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\permissions\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\procedures\\route.ts", ["628"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\settings\\profile\\route.ts", ["629", "630"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\settings\\route.ts", ["631", "632"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\test-auth\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\user-permissions\\route.ts", ["633"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\api\\user-roles\\route.ts", ["634", "635", "636", "637"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\auth\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\admin\\page.tsx", ["638"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\admin\\usuarios\\page.tsx", ["639", "640", "641", "642"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\agenda\\page.tsx", ["643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\configuracoes\\page.tsx", ["656", "657", "658", "659", "660", "661", "662"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\pacientes\\page.tsx", ["663", "664"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\pacientes\\[id]\\page.tsx", ["665", "666", "667", "668"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\page.tsx", ["669", "670"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\dashboard\\prontuario\\[patientId]\\page.tsx", ["671", "672", "673", "674", "675", "676", "677", "678"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\AppointmentForm.tsx", ["679", "680", "681", "682", "683"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\AppSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ConfirmDialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\DashboardLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\FullCalendarView.tsx", ["684", "685"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\LandingPage.tsx", ["686", "687", "688", "689", "690"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\Providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ReactQueryProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\calendar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\chart.tsx", ["691"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\command.tsx", ["692"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\datetime-input.tsx", ["693"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\textarea.tsx", ["694"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\components\\ui\\use-toast.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-mobile.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\use-toast.ts", ["695"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useApiRequest.ts", ["696"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAsyncOperation.ts", ["697", "698", "699", "700", "701"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAuth.ts", ["702", "703"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\useAuth.tsx", ["704", "705"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\usePermissions.ts", ["706"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\hooks\\usePermissions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\api-client.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\api-utils.ts", ["707", "708", "709", "710", "711"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\date-utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\encryption.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\permissions.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\request-throttle.ts", ["712", "713", "714", "715"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\lib\\validations.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\middleware.ts", ["716"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\types\\supabase.ts", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\api\\appointments.test.ts", ["717", "718", "719", "720", "721", "722", "723", "724"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\components\\ui\\calendar.test.tsx", ["725"], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\hooks\\usePermissions.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\next-js\\nectar\\nectar-nextjs\\src\\__tests__\\lib\\validations.test.ts", [], [], {"ruleId": "726", "severity": 2, "message": "727", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 26}, {"ruleId": "726", "severity": 2, "message": "729", "line": 2, "column": 20, "nodeType": null, "messageId": "728", "endLine": 2, "endColumn": 41}, {"ruleId": "726", "severity": 2, "message": "730", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 17}, {"ruleId": "731", "severity": 2, "message": "732", "line": 96, "column": 56, "nodeType": "733", "messageId": "734", "endLine": 96, "endColumn": 59, "suggestions": "735"}, {"ruleId": "726", "severity": 2, "message": "729", "line": 2, "column": 20, "nodeType": null, "messageId": "728", "endLine": 2, "endColumn": 41}, {"ruleId": "726", "severity": 2, "message": "730", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 17}, {"ruleId": "726", "severity": 2, "message": "736", "line": 5, "column": 28, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 35}, {"ruleId": "726", "severity": 2, "message": "737", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 20}, {"ruleId": "731", "severity": 2, "message": "732", "line": 47, "column": 58, "nodeType": "733", "messageId": "734", "endLine": 47, "endColumn": 61, "suggestions": "738"}, {"ruleId": "726", "severity": 2, "message": "736", "line": 4, "column": 27, "nodeType": null, "messageId": "728", "endLine": 4, "endColumn": 34}, {"ruleId": "726", "severity": 2, "message": "739", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 28}, {"ruleId": "726", "severity": 2, "message": "740", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 23}, {"ruleId": "726", "severity": 2, "message": "740", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 23}, {"ruleId": "726", "severity": 2, "message": "729", "line": 2, "column": 20, "nodeType": null, "messageId": "728", "endLine": 2, "endColumn": 41}, {"ruleId": "726", "severity": 2, "message": "741", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 13}, {"ruleId": "726", "severity": 2, "message": "741", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 13}, {"ruleId": "726", "severity": 2, "message": "742", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 15}, {"ruleId": "726", "severity": 2, "message": "743", "line": 5, "column": 35, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 41}, {"ruleId": "726", "severity": 2, "message": "744", "line": 5, "column": 43, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 51}, {"ruleId": "726", "severity": 2, "message": "743", "line": 5, "column": 35, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 41}, {"ruleId": "726", "severity": 2, "message": "744", "line": 5, "column": 43, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 51}, {"ruleId": "726", "severity": 2, "message": "744", "line": 6, "column": 43, "nodeType": null, "messageId": "728", "endLine": 6, "endColumn": 51}, {"ruleId": "726", "severity": 2, "message": "745", "line": 5, "column": 6, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 14}, {"ruleId": "726", "severity": 2, "message": "746", "line": 6, "column": 6, "nodeType": null, "messageId": "728", "endLine": 6, "endColumn": 20}, {"ruleId": "726", "severity": 2, "message": "744", "line": 9, "column": 43, "nodeType": null, "messageId": "728", "endLine": 9, "endColumn": 51}, {"ruleId": "726", "severity": 2, "message": "744", "line": 29, "column": 43, "nodeType": null, "messageId": "728", "endLine": 29, "endColumn": 51}, {"ruleId": "726", "severity": 2, "message": "747", "line": 5, "column": 10, "nodeType": null, "messageId": "728", "endLine": 5, "endColumn": 16}, {"ruleId": "726", "severity": 2, "message": "748", "line": 15, "column": 45, "nodeType": null, "messageId": "728", "endLine": 15, "endColumn": 51}, {"ruleId": "726", "severity": 2, "message": "749", "line": 15, "column": 53, "nodeType": null, "messageId": "728", "endLine": 15, "endColumn": 57}, {"ruleId": "726", "severity": 2, "message": "750", "line": 47, "column": 10, "nodeType": null, "messageId": "728", "endLine": 47, "endColumn": 22}, {"ruleId": "751", "severity": 1, "message": "752", "line": 68, "column": 6, "nodeType": "753", "endLine": 68, "endColumn": 8, "suggestions": "754"}, {"ruleId": "726", "severity": 2, "message": "755", "line": 14, "column": 38, "nodeType": null, "messageId": "728", "endLine": 14, "endColumn": 54}, {"ruleId": "726", "severity": 2, "message": "756", "line": 14, "column": 56, "nodeType": null, "messageId": "728", "endLine": 14, "endColumn": 78}, {"ruleId": "726", "severity": 2, "message": "757", "line": 14, "column": 80, "nodeType": null, "messageId": "728", "endLine": 14, "endColumn": 100}, {"ruleId": "726", "severity": 2, "message": "758", "line": 73, "column": 10, "nodeType": null, "messageId": "728", "endLine": 73, "endColumn": 24}, {"ruleId": "731", "severity": 2, "message": "732", "line": 77, "column": 66, "nodeType": "733", "messageId": "734", "endLine": 77, "endColumn": 69, "suggestions": "759"}, {"ruleId": "751", "severity": 1, "message": "760", "line": 103, "column": 6, "nodeType": "753", "endLine": 103, "endColumn": 8, "suggestions": "761"}, {"ruleId": "751", "severity": 1, "message": "762", "line": 108, "column": 6, "nodeType": "753", "endLine": 108, "endColumn": 20, "suggestions": "763"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 211, "column": 24, "nodeType": "733", "messageId": "734", "endLine": 211, "endColumn": 27, "suggestions": "764"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 282, "column": 48, "nodeType": "733", "messageId": "734", "endLine": 282, "endColumn": 51, "suggestions": "765"}, {"ruleId": "726", "severity": 2, "message": "766", "line": 289, "column": 15, "nodeType": null, "messageId": "728", "endLine": 289, "endColumn": 17}, {"ruleId": "726", "severity": 2, "message": "767", "line": 309, "column": 9, "nodeType": null, "messageId": "728", "endLine": 309, "endColumn": 26}, {"ruleId": "726", "severity": 2, "message": "768", "line": 318, "column": 9, "nodeType": null, "messageId": "728", "endLine": 318, "endColumn": 32}, {"ruleId": "726", "severity": 2, "message": "769", "line": 345, "column": 9, "nodeType": null, "messageId": "728", "endLine": 345, "endColumn": 32}, {"ruleId": "726", "severity": 2, "message": "770", "line": 12, "column": 10, "nodeType": null, "messageId": "728", "endLine": 12, "endColumn": 18}, {"ruleId": "726", "severity": 2, "message": "771", "line": 12, "column": 26, "nodeType": null, "messageId": "728", "endLine": 12, "endColumn": 30}, {"ruleId": "726", "severity": 2, "message": "772", "line": 12, "column": 52, "nodeType": null, "messageId": "728", "endLine": 12, "endColumn": 56}, {"ruleId": "726", "severity": 2, "message": "773", "line": 12, "column": 58, "nodeType": null, "messageId": "728", "endLine": 12, "endColumn": 63}, {"ruleId": "726", "severity": 2, "message": "774", "line": 114, "column": 10, "nodeType": null, "messageId": "728", "endLine": 114, "endColumn": 23}, {"ruleId": "726", "severity": 2, "message": "775", "line": 118, "column": 10, "nodeType": null, "messageId": "728", "endLine": 118, "endColumn": 19}, {"ruleId": "751", "severity": 1, "message": "776", "line": 125, "column": 6, "nodeType": "753", "endLine": 125, "endColumn": 8, "suggestions": "777"}, {"ruleId": "726", "severity": 2, "message": "778", "line": 12, "column": 10, "nodeType": null, "messageId": "728", "endLine": 12, "endColumn": 15}, {"ruleId": "751", "severity": 1, "message": "760", "line": 52, "column": 6, "nodeType": "753", "endLine": 52, "endColumn": 8, "suggestions": "779"}, {"ruleId": "726", "severity": 2, "message": "780", "line": 32, "column": 10, "nodeType": null, "messageId": "728", "endLine": 32, "endColumn": 16}, {"ruleId": "726", "severity": 2, "message": "781", "line": 33, "column": 10, "nodeType": null, "messageId": "728", "endLine": 33, "endColumn": 14}, {"ruleId": "731", "severity": 2, "message": "732", "line": 86, "column": 56, "nodeType": "733", "messageId": "734", "endLine": 86, "endColumn": 59, "suggestions": "782"}, {"ruleId": "751", "severity": 1, "message": "783", "line": 105, "column": 6, "nodeType": "753", "endLine": 105, "endColumn": 17, "suggestions": "784"}, {"ruleId": "726", "severity": 2, "message": "785", "line": 44, "column": 41, "nodeType": null, "messageId": "728", "endLine": 44, "endColumn": 46}, {"ruleId": "751", "severity": 1, "message": "786", "line": 102, "column": 6, "nodeType": "753", "endLine": 102, "endColumn": 32, "suggestions": "787"}, {"ruleId": "726", "severity": 2, "message": "788", "line": 9, "column": 10, "nodeType": null, "messageId": "728", "endLine": 9, "endColumn": 15}, {"ruleId": "726", "severity": 2, "message": "773", "line": 15, "column": 3, "nodeType": null, "messageId": "728", "endLine": 15, "endColumn": 8}, {"ruleId": "726", "severity": 2, "message": "789", "line": 19, "column": 3, "nodeType": null, "messageId": "728", "endLine": 19, "endColumn": 14}, {"ruleId": "726", "severity": 2, "message": "790", "line": 21, "column": 3, "nodeType": null, "messageId": "728", "endLine": 21, "endColumn": 8}, {"ruleId": "726", "severity": 2, "message": "772", "line": 22, "column": 3, "nodeType": null, "messageId": "728", "endLine": 22, "endColumn": 7}, {"ruleId": "726", "severity": 2, "message": "791", "line": 23, "column": 3, "nodeType": null, "messageId": "728", "endLine": 23, "endColumn": 9}, {"ruleId": "751", "severity": 1, "message": "792", "line": 95, "column": 6, "nodeType": "753", "endLine": 95, "endColumn": 32, "suggestions": "793"}, {"ruleId": "751", "severity": 1, "message": "794", "line": 106, "column": 6, "nodeType": "753", "endLine": 106, "endColumn": 44, "suggestions": "795"}, {"ruleId": "726", "severity": 2, "message": "796", "line": 13, "column": 29, "nodeType": null, "messageId": "728", "endLine": 13, "endColumn": 44}, {"ruleId": "726", "severity": 2, "message": "773", "line": 18, "column": 20, "nodeType": null, "messageId": "728", "endLine": 18, "endColumn": 25}, {"ruleId": "726", "severity": 2, "message": "780", "line": 20, "column": 10, "nodeType": null, "messageId": "728", "endLine": 20, "endColumn": 16}, {"ruleId": "726", "severity": 2, "message": "781", "line": 21, "column": 10, "nodeType": null, "messageId": "728", "endLine": 21, "endColumn": 14}, {"ruleId": "726", "severity": 2, "message": "797", "line": 22, "column": 10, "nodeType": null, "messageId": "728", "endLine": 22, "endColumn": 32}, {"ruleId": "726", "severity": 2, "message": "798", "line": 12, "column": 28, "nodeType": null, "messageId": "728", "endLine": 12, "endColumn": 32}, {"ruleId": "731", "severity": 2, "message": "732", "line": 167, "column": 60, "nodeType": "733", "messageId": "734", "endLine": 167, "endColumn": 63, "suggestions": "799"}, {"ruleId": "726", "severity": 2, "message": "800", "line": 3, "column": 10, "nodeType": null, "messageId": "728", "endLine": 3, "endColumn": 18}, {"ruleId": "801", "severity": 2, "message": "802", "line": 209, "column": 23, "nodeType": "803", "messageId": "804", "suggestions": "805"}, {"ruleId": "801", "severity": 2, "message": "802", "line": 209, "column": 93, "nodeType": "803", "messageId": "804", "suggestions": "806"}, {"ruleId": "801", "severity": 2, "message": "802", "line": 224, "column": 23, "nodeType": "803", "messageId": "804", "suggestions": "807"}, {"ruleId": "801", "severity": 2, "message": "802", "line": 224, "column": 95, "nodeType": "803", "messageId": "804", "suggestions": "808"}, {"ruleId": "726", "severity": 2, "message": "809", "line": 70, "column": 7, "nodeType": null, "messageId": "728", "endLine": 70, "endColumn": 8}, {"ruleId": "810", "severity": 2, "message": "811", "line": 24, "column": 11, "nodeType": "812", "messageId": "813", "endLine": 24, "endColumn": 29, "suggestions": "814"}, {"ruleId": "726", "severity": 2, "message": "815", "line": 111, "column": 14, "nodeType": null, "messageId": "728", "endLine": 111, "endColumn": 19}, {"ruleId": "810", "severity": 2, "message": "811", "line": 5, "column": 18, "nodeType": "812", "messageId": "813", "endLine": 5, "endColumn": 31, "suggestions": "816"}, {"ruleId": "726", "severity": 2, "message": "817", "line": 18, "column": 7, "nodeType": null, "messageId": "818", "endLine": 18, "endColumn": 18}, {"ruleId": "731", "severity": 2, "message": "732", "line": 22, "column": 35, "nodeType": "733", "messageId": "734", "endLine": 22, "endColumn": 38, "suggestions": "819"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 5, "column": 23, "nodeType": "733", "messageId": "734", "endLine": 5, "endColumn": 26, "suggestions": "820"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 21, "column": 39, "nodeType": "733", "messageId": "734", "endLine": 21, "endColumn": 42, "suggestions": "821"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 90, "column": 37, "nodeType": "733", "messageId": "734", "endLine": 90, "endColumn": 40, "suggestions": "822"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 99, "column": 39, "nodeType": "733", "messageId": "734", "endLine": 99, "endColumn": 42, "suggestions": "823"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 110, "column": 37, "nodeType": "733", "messageId": "734", "endLine": 110, "endColumn": 40, "suggestions": "824"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 56, "column": 21, "nodeType": "733", "messageId": "734", "endLine": 56, "endColumn": 24, "suggestions": "825"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 93, "column": 21, "nodeType": "733", "messageId": "734", "endLine": 93, "endColumn": 24, "suggestions": "826"}, {"ruleId": "726", "severity": 2, "message": "815", "line": 67, "column": 14, "nodeType": null, "messageId": "728", "endLine": 67, "endColumn": 19}, {"ruleId": "726", "severity": 2, "message": "815", "line": 89, "column": 14, "nodeType": null, "messageId": "728", "endLine": 89, "endColumn": 19}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "827", "line": 112, "column": 12}, {"ruleId": "726", "severity": 2, "message": "828", "line": 3, "column": 30, "nodeType": null, "messageId": "728", "endLine": 3, "endColumn": 34}, {"ruleId": "731", "severity": 2, "message": "732", "line": 5, "column": 34, "nodeType": "733", "messageId": "734", "endLine": 5, "endColumn": 37, "suggestions": "829"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 27, "column": 39, "nodeType": "733", "messageId": "734", "endLine": 27, "endColumn": 42, "suggestions": "830"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 55, "column": 39, "nodeType": "733", "messageId": "734", "endLine": 55, "endColumn": 42, "suggestions": "831"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 86, "column": 39, "nodeType": "733", "messageId": "734", "endLine": 86, "endColumn": 42, "suggestions": "832"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 154, "column": 46, "nodeType": "733", "messageId": "734", "endLine": 154, "endColumn": 49, "suggestions": "833"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 154, "column": 56, "nodeType": "733", "messageId": "734", "endLine": 154, "endColumn": 59, "suggestions": "834"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 169, "column": 46, "nodeType": "733", "messageId": "734", "endLine": 169, "endColumn": 49, "suggestions": "835"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 169, "column": 56, "nodeType": "733", "messageId": "734", "endLine": 169, "endColumn": 59, "suggestions": "836"}, {"ruleId": "726", "severity": 2, "message": "837", "line": 18, "column": 48, "nodeType": null, "messageId": "728", "endLine": 18, "endColumn": 55}, {"ruleId": "731", "severity": 2, "message": "732", "line": 57, "column": 41, "nodeType": "733", "messageId": "734", "endLine": 57, "endColumn": 44, "suggestions": "838"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 73, "column": 24, "nodeType": "733", "messageId": "734", "endLine": 73, "endColumn": 27, "suggestions": "839"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 87, "column": 24, "nodeType": "733", "messageId": "734", "endLine": 87, "endColumn": 27, "suggestions": "840"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 103, "column": 41, "nodeType": "733", "messageId": "734", "endLine": 103, "endColumn": 44, "suggestions": "841"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 137, "column": 42, "nodeType": "733", "messageId": "734", "endLine": 137, "endColumn": 45, "suggestions": "842"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 188, "column": 42, "nodeType": "733", "messageId": "734", "endLine": 188, "endColumn": 45, "suggestions": "843"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 213, "column": 42, "nodeType": "733", "messageId": "734", "endLine": 213, "endColumn": 45, "suggestions": "844"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 263, "column": 42, "nodeType": "733", "messageId": "734", "endLine": 263, "endColumn": 45, "suggestions": "845"}, {"ruleId": "731", "severity": 2, "message": "732", "line": 6, "column": 52, "nodeType": "733", "messageId": "734", "endLine": 6, "endColumn": 55, "suggestions": "846"}, "@typescript-eslint/no-unused-vars", "'AppointmentProcedure' is defined but never used.", "unusedVar", "'withAuthAndPermission' is defined but never used.", "'Appointment' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["847", "848"], "'request' is defined but never used.", "'ClinicSettings' is defined but never used.", ["849", "850"], "'HealthcareProfessional' is defined but never used.", "'PatientAttachment' is defined but never used.", "'Patient' is defined but never used.", "'Procedure' is defined but never used.", "'userId' is defined but never used.", "'supabase' is defined but never used.", "'UserRole' is defined but never used.", "'UserRoleInsert' is defined but never used.", "'Button' is defined but never used.", "'Trash2' is defined but never used.", "'Edit' is defined but never used.", "'selectedUser' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["851"], "'formatDateTimeBR' is defined but never used.", "'getAppointmentStatusBR' is defined but never used.", "'getAppointmentTypeBR' is defined but never used.", "'clinicSettings' is assigned a value but never used.", ["852", "853"], "React Hook useEffect has a missing dependency: 'fetchPatients'. Either include it or remove the dependency array.", ["854"], "React Hook useEffect has a missing dependency: 'fetchAppointments'. Either include it or remove the dependency array.", ["855"], ["856", "857"], ["858", "859"], "'id' is assigned a value but never used.", "'appointmentCounts' is assigned a value but never used.", "'updateAppointmentStatus' is assigned a value but never used.", "'handleDeleteAppointment' is assigned a value but never used.", "'Settings' is defined but never used.", "'Bell' is defined but never used.", "'Mail' is defined but never used.", "'Clock' is defined but never used.", "'notifications' is assigned a value but never used.", "'userRoles' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["860"], "'Badge' is defined but never used.", ["861"], "'format' is defined but never used.", "'ptBR' is defined but never used.", ["862", "863"], "React Hook useEffect has missing dependencies: 'fetchAppointments', 'fetchAttachments', and 'fetchPatientData'. Either include them or remove the dependency array.", ["864"], "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loading'. Either include it or remove the dependency array.", ["865"], "'Input' is defined but never used.", "'AlertCircle' is defined but never used.", "'Phone' is defined but never used.", "'MapPin' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAppointmentData', 'fetchMedicalRecords', and 'fetchPatientData'. Either include them or remove the dependency array.", ["866"], "React Hook useEffect has a missing dependency: 'handleSaveDraft'. Either include it or remove the dependency array.", ["867"], "'CardDescription' is defined but never used.", "'formatDateTimeForInput' is defined but never used.", "'Plus' is defined but never used.", ["868", "869"], "'useState' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["870", "871", "872", "873"], ["874", "875", "876", "877"], ["878", "879", "880", "881"], ["882", "883", "884", "885"], "'_' is defined but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["886"], "'error' is defined but never used.", ["887"], "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", ["888", "889"], ["890", "891"], ["892", "893"], ["894", "895"], ["896", "897"], ["898", "899"], ["900", "901"], ["902", "903"], "Parsing error: Type expected.", "'Role' is defined but never used.", ["904", "905"], ["906", "907"], ["908", "909"], ["910", "911"], ["912", "913"], ["914", "915"], ["916", "917"], ["918", "919"], "'options' is defined but never used.", ["920", "921"], ["922", "923"], ["924", "925"], ["926", "927"], ["928", "929"], ["930", "931"], ["932", "933"], ["934", "935"], ["936", "937"], {"messageId": "938", "fix": "939", "desc": "940"}, {"messageId": "941", "fix": "942", "desc": "943"}, {"messageId": "938", "fix": "944", "desc": "940"}, {"messageId": "941", "fix": "945", "desc": "943"}, {"desc": "946", "fix": "947"}, {"messageId": "938", "fix": "948", "desc": "940"}, {"messageId": "941", "fix": "949", "desc": "943"}, {"desc": "950", "fix": "951"}, {"desc": "952", "fix": "953"}, {"messageId": "938", "fix": "954", "desc": "940"}, {"messageId": "941", "fix": "955", "desc": "943"}, {"messageId": "938", "fix": "956", "desc": "940"}, {"messageId": "941", "fix": "957", "desc": "943"}, {"desc": "958", "fix": "959"}, {"desc": "950", "fix": "960"}, {"messageId": "938", "fix": "961", "desc": "940"}, {"messageId": "941", "fix": "962", "desc": "943"}, {"desc": "963", "fix": "964"}, {"desc": "965", "fix": "966"}, {"desc": "967", "fix": "968"}, {"desc": "969", "fix": "970"}, {"messageId": "938", "fix": "971", "desc": "940"}, {"messageId": "941", "fix": "972", "desc": "943"}, {"messageId": "973", "data": "974", "fix": "975", "desc": "976"}, {"messageId": "973", "data": "977", "fix": "978", "desc": "979"}, {"messageId": "973", "data": "980", "fix": "981", "desc": "982"}, {"messageId": "973", "data": "983", "fix": "984", "desc": "985"}, {"messageId": "973", "data": "986", "fix": "987", "desc": "976"}, {"messageId": "973", "data": "988", "fix": "989", "desc": "979"}, {"messageId": "973", "data": "990", "fix": "991", "desc": "982"}, {"messageId": "973", "data": "992", "fix": "993", "desc": "985"}, {"messageId": "973", "data": "994", "fix": "995", "desc": "976"}, {"messageId": "973", "data": "996", "fix": "997", "desc": "979"}, {"messageId": "973", "data": "998", "fix": "999", "desc": "982"}, {"messageId": "973", "data": "1000", "fix": "1001", "desc": "985"}, {"messageId": "973", "data": "1002", "fix": "1003", "desc": "976"}, {"messageId": "973", "data": "1004", "fix": "1005", "desc": "979"}, {"messageId": "973", "data": "1006", "fix": "1007", "desc": "982"}, {"messageId": "973", "data": "1008", "fix": "1009", "desc": "985"}, {"messageId": "1010", "fix": "1011", "desc": "1012"}, {"messageId": "1010", "fix": "1013", "desc": "1012"}, {"messageId": "938", "fix": "1014", "desc": "940"}, {"messageId": "941", "fix": "1015", "desc": "943"}, {"messageId": "938", "fix": "1016", "desc": "940"}, {"messageId": "941", "fix": "1017", "desc": "943"}, {"messageId": "938", "fix": "1018", "desc": "940"}, {"messageId": "941", "fix": "1019", "desc": "943"}, {"messageId": "938", "fix": "1020", "desc": "940"}, {"messageId": "941", "fix": "1021", "desc": "943"}, {"messageId": "938", "fix": "1022", "desc": "940"}, {"messageId": "941", "fix": "1023", "desc": "943"}, {"messageId": "938", "fix": "1024", "desc": "940"}, {"messageId": "941", "fix": "1025", "desc": "943"}, {"messageId": "938", "fix": "1026", "desc": "940"}, {"messageId": "941", "fix": "1027", "desc": "943"}, {"messageId": "938", "fix": "1028", "desc": "940"}, {"messageId": "941", "fix": "1029", "desc": "943"}, {"messageId": "938", "fix": "1030", "desc": "940"}, {"messageId": "941", "fix": "1031", "desc": "943"}, {"messageId": "938", "fix": "1032", "desc": "940"}, {"messageId": "941", "fix": "1033", "desc": "943"}, {"messageId": "938", "fix": "1034", "desc": "940"}, {"messageId": "941", "fix": "1035", "desc": "943"}, {"messageId": "938", "fix": "1036", "desc": "940"}, {"messageId": "941", "fix": "1037", "desc": "943"}, {"messageId": "938", "fix": "1038", "desc": "940"}, {"messageId": "941", "fix": "1039", "desc": "943"}, {"messageId": "938", "fix": "1040", "desc": "940"}, {"messageId": "941", "fix": "1041", "desc": "943"}, {"messageId": "938", "fix": "1042", "desc": "940"}, {"messageId": "941", "fix": "1043", "desc": "943"}, {"messageId": "938", "fix": "1044", "desc": "940"}, {"messageId": "941", "fix": "1045", "desc": "943"}, {"messageId": "938", "fix": "1046", "desc": "940"}, {"messageId": "941", "fix": "1047", "desc": "943"}, {"messageId": "938", "fix": "1048", "desc": "940"}, {"messageId": "941", "fix": "1049", "desc": "943"}, {"messageId": "938", "fix": "1050", "desc": "940"}, {"messageId": "941", "fix": "1051", "desc": "943"}, {"messageId": "938", "fix": "1052", "desc": "940"}, {"messageId": "941", "fix": "1053", "desc": "943"}, {"messageId": "938", "fix": "1054", "desc": "940"}, {"messageId": "941", "fix": "1055", "desc": "943"}, {"messageId": "938", "fix": "1056", "desc": "940"}, {"messageId": "941", "fix": "1057", "desc": "943"}, {"messageId": "938", "fix": "1058", "desc": "940"}, {"messageId": "941", "fix": "1059", "desc": "943"}, {"messageId": "938", "fix": "1060", "desc": "940"}, {"messageId": "941", "fix": "1061", "desc": "943"}, {"messageId": "938", "fix": "1062", "desc": "940"}, {"messageId": "941", "fix": "1063", "desc": "943"}, "suggestUnknown", {"range": "1064", "text": "1065"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1066", "text": "1067"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1068", "text": "1065"}, {"range": "1069", "text": "1067"}, "Update the dependencies array to be: [fetchData]", {"range": "1070", "text": "1071"}, {"range": "1072", "text": "1065"}, {"range": "1073", "text": "1067"}, "Update the dependencies array to be: [fetchPatients]", {"range": "1074", "text": "1075"}, "Update the dependencies array to be: [fetchAppointments, selectedDate]", {"range": "1076", "text": "1077"}, {"range": "1078", "text": "1065"}, {"range": "1079", "text": "1067"}, {"range": "1080", "text": "1065"}, {"range": "1081", "text": "1067"}, "Update the dependencies array to be: [fetchSettings]", {"range": "1082", "text": "1083"}, {"range": "1084", "text": "1075"}, {"range": "1085", "text": "1065"}, {"range": "1086", "text": "1067"}, "Update the dependencies array to be: [fetchAppointments, fetchAttachments, fetchPatientData, patientId]", {"range": "1087", "text": "1088"}, "Update the dependencies array to be: [user, fetchDashboardData, loading]", {"range": "1089", "text": "1090"}, "Update the dependencies array to be: [patientId, appointmentId, fetchPatientData, fetchAppointmentData, fetchMedicalRecords]", {"range": "1091", "text": "1092"}, "Update the dependencies array to be: [currentRecord, appointmentId, saving, handleSaveDraft]", {"range": "1093", "text": "1094"}, {"range": "1095", "text": "1065"}, {"range": "1096", "text": "1067"}, "replaceWithAlt", {"alt": "1097"}, {"range": "1098", "text": "1099"}, "Replace with `&quot;`.", {"alt": "1100"}, {"range": "1101", "text": "1102"}, "Replace with `&ldquo;`.", {"alt": "1103"}, {"range": "1104", "text": "1105"}, "Replace with `&#34;`.", {"alt": "1106"}, {"range": "1107", "text": "1108"}, "Replace with `&rdquo;`.", {"alt": "1097"}, {"range": "1109", "text": "1110"}, {"alt": "1100"}, {"range": "1111", "text": "1112"}, {"alt": "1103"}, {"range": "1113", "text": "1114"}, {"alt": "1106"}, {"range": "1115", "text": "1116"}, {"alt": "1097"}, {"range": "1117", "text": "1118"}, {"alt": "1100"}, {"range": "1119", "text": "1120"}, {"alt": "1103"}, {"range": "1121", "text": "1122"}, {"alt": "1106"}, {"range": "1123", "text": "1124"}, {"alt": "1097"}, {"range": "1125", "text": "1126"}, {"alt": "1100"}, {"range": "1127", "text": "1128"}, {"alt": "1103"}, {"range": "1129", "text": "1130"}, {"alt": "1106"}, {"range": "1131", "text": "1132"}, "replaceEmptyInterfaceWithSuper", {"range": "1133", "text": "1134"}, "Replace empty interface with a type alias.", {"range": "1135", "text": "1136"}, {"range": "1137", "text": "1065"}, {"range": "1138", "text": "1067"}, {"range": "1139", "text": "1065"}, {"range": "1140", "text": "1067"}, {"range": "1141", "text": "1065"}, {"range": "1142", "text": "1067"}, {"range": "1143", "text": "1065"}, {"range": "1144", "text": "1067"}, {"range": "1145", "text": "1065"}, {"range": "1146", "text": "1067"}, {"range": "1147", "text": "1065"}, {"range": "1148", "text": "1067"}, {"range": "1149", "text": "1065"}, {"range": "1150", "text": "1067"}, {"range": "1151", "text": "1065"}, {"range": "1152", "text": "1067"}, {"range": "1153", "text": "1065"}, {"range": "1154", "text": "1067"}, {"range": "1155", "text": "1065"}, {"range": "1156", "text": "1067"}, {"range": "1157", "text": "1065"}, {"range": "1158", "text": "1067"}, {"range": "1159", "text": "1065"}, {"range": "1160", "text": "1067"}, {"range": "1161", "text": "1065"}, {"range": "1162", "text": "1067"}, {"range": "1163", "text": "1065"}, {"range": "1164", "text": "1067"}, {"range": "1165", "text": "1065"}, {"range": "1166", "text": "1067"}, {"range": "1167", "text": "1065"}, {"range": "1168", "text": "1067"}, {"range": "1169", "text": "1065"}, {"range": "1170", "text": "1067"}, {"range": "1171", "text": "1065"}, {"range": "1172", "text": "1067"}, {"range": "1173", "text": "1065"}, {"range": "1174", "text": "1067"}, {"range": "1175", "text": "1065"}, {"range": "1176", "text": "1067"}, {"range": "1177", "text": "1065"}, {"range": "1178", "text": "1067"}, {"range": "1179", "text": "1065"}, {"range": "1180", "text": "1067"}, {"range": "1181", "text": "1065"}, {"range": "1182", "text": "1067"}, {"range": "1183", "text": "1065"}, {"range": "1184", "text": "1067"}, {"range": "1185", "text": "1065"}, {"range": "1186", "text": "1067"}, [3241, 3244], "unknown", [3241, 3244], "never", [1606, 1609], [1606, 1609], [2080, 2082], "[fetchData]", [2826, 2829], [2826, 2829], [3455, 3457], "[fetchPatients]", [3576, 3590], "[fetchAppointments, selectedDate]", [7332, 7335], [7332, 7335], [9916, 9919], [9916, 9919], [3551, 3553], "[fetchSettings]", [1776, 1778], [2648, 2651], [2648, 2651], [3002, 3013], "[fetchAppointments, fetchAttachments, fetchPatientData, patientId]", [2818, 2844], "[user, fetchDashboardData, loading]", [2600, 2626], "[patientId, appointmentId, fetchPatientData, fetchAppointmentData, fetchMedicalRecords]", [2960, 2998], "[currentRecord, appointmentId, saving, handleSaveDraft]", [5632, 5635], [5632, 5635], "&quot;", [8864, 8979], "\n                      &quot;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", "&ldquo;", [8864, 8979], "\n                      &ldquo;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", "&#34;", [8864, 8979], "\n                      &#34;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", "&rdquo;", [8864, 8979], "\n                      &rdquo;Recuperei 3 horas por dia que gastava organizando agenda e mensagens.\"\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&quot;\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&ldquo;\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&#34;\n                    ", [8864, 8979], "\n                      \"Recuperei 3 horas por dia que gastava organizando agenda e mensagens.&rdquo;\n                    ", [9629, 9746], "\n                      &quot;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      &ldquo;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      &#34;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      &rdquo;Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.\"\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&quot;\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&ldquo;\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&#34;\n                    ", [9629, 9746], "\n                      \"Meu faturamento aumentou 30% em 3 meses com as campanhas automatizadas.&rdquo;\n                    ", [724, 775], "type CommandDialogProps = DialogProps", [77, 164], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [578, 581], [578, 581], [151, 154], [151, 154], [528, 531], [528, 531], [2148, 2151], [2148, 2151], [2371, 2374], [2371, 2374], [2680, 2683], [2680, 2683], [1556, 1559], [1556, 1559], [2494, 2497], [2494, 2497], [204, 207], [204, 207], [575, 578], [575, 578], [1360, 1363], [1360, 1363], [2189, 2192], [2189, 2192], [4001, 4004], [4001, 4004], [4011, 4014], [4011, 4014], [4333, 4336], [4333, 4336], [4343, 4346], [4343, 4346], [1847, 1850], [1847, 1850], [2373, 2376], [2373, 2376], [2828, 2831], [2828, 2831], [3257, 3260], [3257, 3260], [4079, 4082], [4079, 4082], [5520, 5523], [5520, 5523], [6267, 6270], [6267, 6270], [7602, 7605], [7602, 7605], [235, 238], [235, 238]]