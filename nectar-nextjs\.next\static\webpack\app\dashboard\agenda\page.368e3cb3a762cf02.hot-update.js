"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/circle-check-big.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ CircleCheckBig)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M21.801 10A10 10 0 1 1 17 3.335\",\n            key: \"yps3ct\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 11 3 3L22 4\",\n            key: \"1pflzl\"\n        }\n    ]\n];\nconst CircleCheckBig = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"circle-check-big\", __iconNode);\n //# sourceMappingURL=circle-check-big.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-text.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ FileText)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 9H8\",\n            key: \"b1mrlr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 13H8\",\n            key: \"t4e002\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 17H8\",\n            key: \"z1uh3a\"\n        }\n    ]\n];\nconst FileText = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"file-text\", __iconNode);\n //# sourceMappingURL=file-text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    // Fetch initial data that doesn't depend on selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], []);\n    // Fetch date-specific appointments when selected date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            // FullCalendar provides dates in local timezone, use them directly\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.start);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.end);\n        } else if (selectedDate) {\n            // Create appointment for selected date at 9:00 AM\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(startTime);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(endTime);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // For in-progress and completed appointments, navigate to medical record screen\n        if (appointment.status === 'in_progress' || appointment.status === 'completed') {\n            console.log('Navigating to medical record for appointment:', appointment.id);\n            router.push(\"/dashboard/prontuario/\".concat(appointment.patient_id, \"?appointment_id=\").concat(appointment.id));\n            return;\n        }\n        // For other statuses, open edit form with appointment data\n        console.log('Appointment clicked for editing:', appointment);\n        const editData = {\n            id: appointment.id,\n            title: appointment.title,\n            description: appointment.description,\n            patient_id: appointment.patient_id,\n            healthcare_professional_id: appointment.healthcare_professional_id,\n            start_time: appointment.start_time,\n            end_time: appointment.end_time,\n            type: appointment.type,\n            status: appointment.status,\n            price: appointment.price\n        };\n        setAppointmentFormData(editData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const isEditing = data.id;\n            const url = isEditing ? \"/api/appointments/\".concat(data.id) : '/api/appointments';\n            const method = isEditing ? 'PUT' : 'POST';\n            // Remove id from data for API call\n            const { id, ...submitData } = data;\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(submitData)\n            });\n            if (!response.ok) throw new Error(\"Failed to \".concat(isEditing ? 'update' : 'create', \" appointment\"));\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error(\"Error \".concat(data.id ? 'updating' : 'creating', \" appointment:\"), error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancelAppointment = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        if (!confirm('Tem certeza que deseja cancelar esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointment.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'cancelled'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to cancel appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta cancelada com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error canceling appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao cancelar consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStartConsultation = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        if (!confirm(\"Deseja iniciar o atendimento para \".concat(appointment.patient_name, \"?\"))) return;\n        try {\n            // Update appointment status to in_progress\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointment.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'in_progress'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to start consultation');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Atendimento iniciado com sucesso.\"\n            });\n            // Navigate to medical record screen\n            router.push(\"/dashboard/prontuario/\".concat(appointment.patient_id, \"?appointment_id=\").concat(appointment.id));\n        } catch (error) {\n            console.error('Error starting consultation:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao iniciar atendimento.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR,\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    // appointmentCounts={appointmentCounts}\n                                                    // clinicSettings={clinicSettings || undefined}\n                                                    // appointments={allAppointments}\n                                                    className: \"rounded-md border-0 shadow-none w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 504,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatDateBR)(selectedDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 506,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border transition-colors space-y-3 sm:space-y-0 \".concat(appointment.status === 'cancelled' ? 'bg-red-50 border-red-200 hover:bg-red-100' : appointment.status === 'in_progress' ? 'bg-blue-50 border-blue-200 hover:bg-blue-100 cursor-pointer' : appointment.status === 'completed' ? 'bg-green-50 border-green-200 hover:bg-green-100 cursor-pointer' : 'bg-card/50 hover:bg-card cursor-pointer'),\n                                                            onClick: appointment.status !== 'cancelled' ? ()=>handleAppointmentClick(appointment) : undefined,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-1\",\n                                                                                    children: [\n                                                                                        appointment.status === 'in_progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-3 w-3 text-blue-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 543,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        appointment.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 546,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.start_time)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 548,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 541,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.end_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 552,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 557,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 558,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 562,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : appointment.status === 'in_progress' ? 'default' : 'outline',\n                                                                                    className: \"text-xs\",\n                                                                                    children: [\n                                                                                        appointment.status === 'scheduled' && 'Agendado',\n                                                                                        appointment.status === 'confirmed' && 'Confirmado',\n                                                                                        appointment.status === 'completed' && 'Concluído',\n                                                                                        appointment.status === 'cancelled' && 'Cancelado',\n                                                                                        appointment.status === 'in_progress' && 'Em Andamento'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 570,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium whitespace-nowrap\",\n                                                                                    children: [\n                                                                                        \"R$ \",\n                                                                                        appointment.price.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 569,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.status !== 'cancelled' && appointment.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-1\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs\",\n                                                                                    onClick: (e)=>handleStartConsultation(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 602,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"Iniciar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 596,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                                    onClick: (e)=>handleCancelAppointment(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 612,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"Cancelar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 606,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            appointments: allAppointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 628,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 640,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 420,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"xLZJ7pt+Iz6ZUtzTScoCCOXUoEE=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});