"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/AppointmentForm.tsx":
/*!********************************************!*\
  !*** ./src/components/AppointmentForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/datetime-input */ \"(app-pages-browser)/./src/components/ui/datetime-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=addMinutes!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addMinutes.js\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useAsyncOperation */ \"(app-pages-browser)/./src/hooks/useAsyncOperation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Utility functions for Brazilian timezone (UTC-3)\nconst brazilianTimezoneOffset = -3 * 60; // -3 hours in minutes\nconst toBrazilianTime = (date)=>{\n    if (!date) return null;\n    // Se for string, converte para Date\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    // Cria uma nova data ajustada para UTC-3\n    const utcTime = dateObj.getTime() + dateObj.getTimezoneOffset() * 60000;\n    const brazilianTime = new Date(utcTime + brazilianTimezoneOffset * 60000);\n    return brazilianTime;\n};\nconst formatDateTimeForBrazilianInput = (dateTime)=>{\n    if (!dateTime) return '';\n    try {\n        const date = toBrazilianTime(dateTime);\n        if (!date || isNaN(date.getTime())) return '';\n        // Formato para datetime-local: YYYY-MM-DDTHH:mm\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n        return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"T\").concat(hours, \":\").concat(minutes);\n    } catch (error) {\n        console.error('[DATETIME] Error formatting date:', error);\n        return '';\n    }\n};\nconst parseBrazilianDateTime = (dateTimeString)=>{\n    if (!dateTimeString) return null;\n    try {\n        // Se já tem timezone, usa diretamente\n        if (dateTimeString.includes('T') && (dateTimeString.includes('Z') || dateTimeString.includes('-03:00'))) {\n            return new Date(dateTimeString);\n        }\n        // Se é formato datetime-local (YYYY-MM-DDTHH:mm), adiciona timezone brasileiro\n        if (dateTimeString.includes('T')) {\n            return new Date(dateTimeString + ':00-03:00');\n        }\n        // Fallback para outros formatos\n        const date = new Date(dateTimeString);\n        return toBrazilianTime(date);\n    } catch (error) {\n        console.error('[DATETIME] Error parsing date:', error);\n        return null;\n    }\n};\nconst AppointmentForm = (param)=>{\n    let { open, onOpenChange, patients, healthcareProfessionals, procedures, initialData, onSubmit, loading = false } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('scheduling');\n    const [searchProcedure, setSearchProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedProcedures, setSelectedProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Check if we're in editing mode\n    const isEditing = Boolean(initialData === null || initialData === void 0 ? void 0 : initialData.id);\n    const { execute: submitForm, loading: submitting } = (0,_hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission)({\n        successMessage: isEditing ? 'Consulta atualizada com sucesso!' : 'Consulta agendada com sucesso!',\n        errorMessage: isEditing ? 'Erro ao atualizar consulta' : 'Erro ao agendar consulta'\n    });\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_16__.appointmentSchema),\n        defaultValues: {\n            title: '',\n            description: '',\n            patient_id: '',\n            healthcare_professional_id: '',\n            start_time: '',\n            end_time: '',\n            type: 'consultation',\n            status: 'scheduled',\n            notes: '',\n            has_recurrence: false,\n            recurrence_type: 'weekly',\n            recurrence_interval: 1,\n            recurrence_days: [],\n            recurrence_end_type: 'never',\n            recurrence_end_date: '',\n            recurrence_count: 1\n        }\n    });\n    const { control, handleSubmit, watch, setValue, reset, formState: { errors } } = form;\n    const watchedValues = watch();\n    // Initialize form with initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (initialData && open) {\n                console.log('[DATETIME] Initializing form with data:', initialData);\n                const formattedData = {\n                    ...initialData,\n                    start_time: formatDateTimeForBrazilianInput(initialData.start_time),\n                    end_time: formatDateTimeForBrazilianInput(initialData.end_time),\n                    patient_id: initialData.patient_id || '',\n                    healthcare_professional_id: initialData.healthcare_professional_id || ''\n                };\n                console.log('[DATETIME] Formatted data for form:', formattedData);\n                reset(formattedData);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        initialData,\n        open,\n        reset\n    ]);\n    // Auto-generate title when patient and type change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (watchedValues.patient_id && watchedValues.type) {\n                const patient = patients.find({\n                    \"AppointmentForm.useEffect.patient\": (p)=>p.id === watchedValues.patient_id\n                }[\"AppointmentForm.useEffect.patient\"]);\n                if (patient) {\n                    const typeLabel = watchedValues.type === 'consultation' ? 'Consulta' : 'Retorno';\n                    setValue('title', \"\".concat(typeLabel, \" - \").concat(patient.name));\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.patient_id,\n        watchedValues.type,\n        patients,\n        setValue\n    ]);\n    // Auto-calculate end time based on start time (Brazilian timezone)\n    // Only auto-calculate if end_time is not already set (e.g., from FullCalendar selection)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            console.log('[DATETIME] Auto-calculating end time, start_time:', watchedValues.start_time, 'end_time:', watchedValues.end_time);\n            if (watchedValues.start_time && !watchedValues.end_time) {\n                try {\n                    const startTime = parseBrazilianDateTime(watchedValues.start_time);\n                    console.log('[DATETIME] Parsed start time:', startTime, 'Valid:', startTime && !isNaN(startTime.getTime()));\n                    if (startTime && !isNaN(startTime.getTime())) {\n                        const totalDuration = selectedProcedures.reduce({\n                            \"AppointmentForm.useEffect.totalDuration\": (total, proc)=>{\n                                const procedure = procedures.find({\n                                    \"AppointmentForm.useEffect.totalDuration.procedure\": (p)=>p.id === proc.procedure_id\n                                }[\"AppointmentForm.useEffect.totalDuration.procedure\"]);\n                                return total + ((procedure === null || procedure === void 0 ? void 0 : procedure.duration_minutes) || 30) * proc.quantity;\n                            }\n                        }[\"AppointmentForm.useEffect.totalDuration\"], 30); // Default 30 minutes if no procedures\n                        console.log('[DATETIME] Total duration:', totalDuration, 'minutes');\n                        const endTime = (0,_barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__.addMinutes)(startTime, totalDuration);\n                        const formattedEndTime = formatDateTimeForBrazilianInput(endTime);\n                        console.log('[DATETIME] Calculated end time:', endTime, 'Formatted:', formattedEndTime);\n                        setValue('end_time', formattedEndTime);\n                    }\n                } catch (error) {\n                    console.error('[DATETIME] Error calculating end time:', error);\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.start_time,\n        watchedValues.end_time,\n        selectedProcedures,\n        procedures,\n        setValue\n    ]);\n    const filteredProcedures = procedures.filter((procedure)=>procedure.name.toLowerCase().includes(searchProcedure.toLowerCase()) || procedure.description && procedure.description.toLowerCase().includes(searchProcedure.toLowerCase()));\n    const addProcedure = (procedure)=>{\n        const existingIndex = selectedProcedures.findIndex((p)=>p.procedure_id === procedure.id);\n        if (existingIndex >= 0) {\n            // Increase quantity if already exists\n            const updated = [\n                ...selectedProcedures\n            ];\n            updated[existingIndex].quantity += 1;\n            updated[existingIndex].total_price = updated[existingIndex].quantity * updated[existingIndex].unit_price;\n            setSelectedProcedures(updated);\n        } else {\n            // Add new procedure\n            const newProcedure = {\n                procedure_id: procedure.id,\n                procedure_name: procedure.name,\n                quantity: 1,\n                unit_price: procedure.default_price || 0,\n                total_price: procedure.default_price || 0\n            };\n            setSelectedProcedures([\n                ...selectedProcedures,\n                newProcedure\n            ]);\n        }\n    };\n    const updateProcedure = (index, field, value)=>{\n        const updated = [\n            ...selectedProcedures\n        ];\n        updated[index][field] = value;\n        updated[index].total_price = updated[index].quantity * updated[index].unit_price;\n        setSelectedProcedures(updated);\n    };\n    const removeProcedure = (index)=>{\n        setSelectedProcedures(selectedProcedures.filter((_, i)=>i !== index));\n    };\n    const getTotalPrice = ()=>{\n        return selectedProcedures.reduce((total, proc)=>total + proc.total_price, 0);\n    };\n    const onFormSubmit = async (data)=>{\n        console.log('[DATETIME] Form submitted with data:', data);\n        try {\n            // Parse and validate dates in Brazilian timezone\n            const startTime = parseBrazilianDateTime(data.start_time);\n            const endTime = parseBrazilianDateTime(data.end_time);\n            if (!startTime || !endTime) {\n                toast({\n                    title: 'Erro',\n                    description: 'Datas inválidas. Por favor, verifique os horários.',\n                    variant: 'destructive'\n                });\n                return;\n            }\n            // Convert to ISO string with Brazilian timezone\n            const formattedData = {\n                ...data,\n                start_time: startTime.toISOString(),\n                end_time: endTime.toISOString()\n            };\n            console.log('[DATETIME] Formatted data for submission:', formattedData);\n            await submitForm(async ()=>{\n                const appointmentData = {\n                    ...formattedData,\n                    procedures: selectedProcedures,\n                    total_price: getTotalPrice(),\n                    status: formattedData.status || 'scheduled',\n                    ...isEditing && (initialData === null || initialData === void 0 ? void 0 : initialData.id) && {\n                        id: initialData.id\n                    }\n                };\n                console.log('[DATETIME] Final appointment data:', appointmentData);\n                await onSubmit(appointmentData);\n                // Reset form\n                reset();\n                setSelectedProcedures([]);\n                setActiveTab('scheduling');\n                onOpenChange(false);\n            });\n        } catch (error) {\n            console.error('[DATETIME] Error submitting form:', error);\n            toast({\n                title: 'Erro',\n                description: 'Erro ao processar os dados. Por favor, tente novamente.',\n                variant: 'destructive'\n            });\n        }\n    };\n    const toggleRecurrenceDay = (day)=>{\n        const currentDays = watchedValues.recurrence_days || [];\n        const newDays = currentDays.includes(day) ? currentDays.filter((d)=>d !== day) : [\n            ...currentDays,\n            day\n        ];\n        setValue('recurrence_days', newDays);\n    };\n    const weekDays = [\n        {\n            value: 1,\n            label: 'D'\n        },\n        {\n            value: 2,\n            label: 'S'\n        },\n        {\n            value: 3,\n            label: 'T'\n        },\n        {\n            value: 4,\n            label: 'Q'\n        },\n        {\n            value: 5,\n            label: 'Q'\n        },\n        {\n            value: 6,\n            label: 'S'\n        },\n        {\n            value: 7,\n            label: 'S'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[800px] max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: isEditing ? 'Editar Consulta' : 'Agendar Nova Consulta'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                            children: isEditing ? 'Edite os dados da consulta' : 'Preencha os dados para agendar uma nova consulta'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit(onFormSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"scheduling\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Agendamento\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"procedures\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Procedimentos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"scheduling\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"patient\",\n                                                            children: \"Paciente *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"patient_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o paciente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 383,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: patient.id,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                lineNumber: 389,\n                                                                                                columnNumber: 33\n                                                                                            }, void 0),\n                                                                                            patient.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0)\n                                                                                }, patient.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 29\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.patient_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.patient_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"professional\",\n                                                            children: \"Profissional\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"healthcare_professional_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o profissional\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: professional.id,\n                                                                                    children: [\n                                                                                        professional.name,\n                                                                                        professional.specialty && \" - \".concat(professional.specialty)\n                                                                                    ]\n                                                                                }, professional.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 420,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.healthcare_professional_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.healthcare_professional_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"type\",\n                                                            children: \"Tipo de Consulta *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"type\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"consultation\",\n                                                                                    children: \"Consulta\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"follow_up\",\n                                                                                    children: \"Retorno\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 27\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.type.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"T\\xedtulo *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"title\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"title\",\n                                                                    ...field\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.title.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"status\",\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"status\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 493,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"scheduled\",\n                                                                                    children: \"Agendado\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 497,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"confirmed\",\n                                                                                    children: \"Confirmado\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 498,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"in_progress\",\n                                                                                    children: \"Em Andamento\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 499,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"completed\",\n                                                                                    children: \"Conclu\\xeddo\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 500,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"cancelled\",\n                                                                                    children: \"Cancelado\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 501,\n                                                                                    columnNumber: 27\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 496,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.status.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"start_time\",\n                                                            children: \"Data/Hora In\\xedcio *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"start_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"start_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.start_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.start_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"end_time\",\n                                                            children: \"Data/Hora Fim *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"end_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"end_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.end_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.end_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"Descri\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                    name: \"description\",\n                                                    control: control,\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                            id: \"description\",\n                                                            placeholder: \"Descri\\xe7\\xe3o da consulta...\",\n                                                            ...field\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        errors.description.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                name: \"has_recurrence\",\n                                                                control: control,\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n                                                                        id: \"has_recurrence\",\n                                                                        checked: field.value,\n                                                                        onCheckedChange: field.onChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 25\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"has_recurrence\",\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Recorr\\xeancia Personalizada\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                watchedValues.has_recurrence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir a cada:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 611,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_interval\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            type: \"number\",\n                                                                                            min: \"1\",\n                                                                                            ...field,\n                                                                                            onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                            className: \"w-20\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 617,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 613,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_type\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                                            value: field.value,\n                                                                                            onValueChange: field.onChange,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                        lineNumber: 632,\n                                                                                                        columnNumber: 35\n                                                                                                    }, void 0)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 631,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"daily\",\n                                                                                                            children: \"dia(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 635,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"weekly\",\n                                                                                                            children: \"semana(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 636,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"monthly\",\n                                                                                                            children: \"m\\xeas(es)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 637,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 634,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 630,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 626,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 610,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                watchedValues.recurrence_type === 'weekly' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 647,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex gap-1\",\n                                                                            children: weekDays.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: (watchedValues.recurrence_days || []).includes(day.value) ? 'default' : 'outline',\n                                                                                    size: \"sm\",\n                                                                                    className: \"w-8 h-8 p-0\",\n                                                                                    onClick: ()=>toggleRecurrenceDay(day.value),\n                                                                                    children: day.label\n                                                                                }, day.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 650,\n                                                                                    columnNumber: 31\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Termina em:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 667,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                    name: \"recurrence_end_type\",\n                                                                    control: control,\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroup, {\n                                                                            value: field.value,\n                                                                            onValueChange: field.onChange,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"never\",\n                                                                                            id: \"never\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 675,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"never\",\n                                                                                            children: \"Nunca\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 676,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 674,\n                                                                                    columnNumber: 25\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"date\",\n                                                                                            id: \"end_date\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 680,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_date\",\n                                                                                            children: \"Em\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 681,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_end_date\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"date\",\n                                                                                                    ...field,\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'date',\n                                                                                                    className: \"w-40\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 686,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 682,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 679,\n                                                                                    columnNumber: 29\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"count\",\n                                                                                            id: \"end_count\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 697,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_count\",\n                                                                                            children: \"Ap\\xf3s\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 698,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_count\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"number\",\n                                                                                                    min: \"1\",\n                                                                                                    ...field,\n                                                                                                    onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'count',\n                                                                                                    className: \"w-20\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 703,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 699,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            children: \"ocorr\\xeancias\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 713,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 696,\n                                                                                    columnNumber: 29\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 672,\n                                                                            columnNumber: 27\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 666,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"procedures\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 727,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        placeholder: \"Buscar procedimentos...\",\n                                                        value: searchProcedure,\n                                                        onChange: (e)=>setSearchProcedure(e.target.value),\n                                                        className: \"flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 728,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 726,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto\",\n                                                children: filteredProcedures.map((procedure)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                        className: \"cursor-pointer hover:bg-accent/50\",\n                                                        onClick: ()=>addProcedure(procedure),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: procedure.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 742,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            procedure.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                                children: procedure.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 744,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-2\",\n                                                                                children: [\n                                                                                    procedure.default_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"secondary\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            \"R$ \",\n                                                                                            procedure.default_price.toFixed(2)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 748,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    procedure.duration_minutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            procedure.duration_minutes,\n                                                                                            \"min\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 753,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 746,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 741,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 760,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 759,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, procedure.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 736,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            selectedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: \"Procedimentos Selecionados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 771,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 770,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            selectedProcedures.map((proc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 p-3 border rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium\",\n                                                                                children: proc.procedure_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 777,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 776,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Qtd:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 781,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    value: proc.quantity,\n                                                                                    onChange: (e)=>updateProcedure(index, 'quantity', parseInt(e.target.value) || 1),\n                                                                                    className: \"w-16 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 782,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 780,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Pre\\xe7o:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 792,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"0\",\n                                                                                    step: \"0.01\",\n                                                                                    value: proc.unit_price,\n                                                                                    onChange: (e)=>updateProcedure(index, 'unit_price', parseFloat(e.target.value) || 0),\n                                                                                    className: \"w-24 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 793,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 791,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                proc.total_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 803,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>removeProcedure(index),\n                                                                            className: \"h-8 w-8 p-0 text-destructive hover:text-destructive\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 814,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 775,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center pt-3 border-t\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 820,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold\",\n                                                                        children: [\n                                                                            \"R$ \",\n                                                                            getTotalPrice().toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 819,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 724,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    disabled: submitting || loading,\n                                    children: submitting ? isEditing ? 'Salvando...' : 'Agendando...' : isEditing ? 'Salvar Alterações' : 'Agendar Consulta'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 834,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n        lineNumber: 351,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppointmentForm, \"0fK7CMPQBa8UlSZ3bOvox60mpoM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AppointmentForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentForm);\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AppointmentForm.tsx\n"));

/***/ })

});