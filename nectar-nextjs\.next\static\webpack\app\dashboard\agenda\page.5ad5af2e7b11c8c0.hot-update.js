"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/play.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Play)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polygon\",\n        {\n            points: \"6 3 20 12 6 21 6 3\",\n            key: \"1oa8hb\"\n        }\n    ]\n];\nconst Play = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"play\", __iconNode);\n //# sourceMappingURL=play.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcGxheS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFHYSxtQkFBdUI7SUFBQztRQUFDLFNBQVc7UUFBQTtZQUFFLFFBQVEsb0JBQXNCO1lBQUEsSUFBSyxTQUFTO1FBQUEsQ0FBQztLQUFDO0NBQUE7QUFhM0YsV0FBTyxrRUFBaUIsU0FBUSxDQUFVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNpcm92XFxEb2N1bWVudHNcXHNyY1xcaWNvbnNcXHBsYXkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5pbXBvcnQgeyBJY29uTm9kZSB9IGZyb20gJy4uL3R5cGVzJztcblxuZXhwb3J0IGNvbnN0IF9faWNvbk5vZGU6IEljb25Ob2RlID0gW1sncG9seWdvbicsIHsgcG9pbnRzOiAnNiAzIDIwIDEyIDYgMjEgNiAzJywga2V5OiAnMW9hOGhiJyB9XV07XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBQbGF5XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjRzlzZVdkdmJpQndiMmx1ZEhNOUlqWWdNeUF5TUNBeE1pQTJJREl4SURZZ015SWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9wbGF5XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgUGxheSA9IGNyZWF0ZUx1Y2lkZUljb24oJ3BsYXknLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IGRlZmF1bHQgUGxheTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    // Fetch initial data that doesn't depend on selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], []);\n    // Fetch date-specific appointments when selected date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            // FullCalendar provides dates in local timezone, use them directly\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.start);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.end);\n        } else if (selectedDate) {\n            // Create appointment for selected date at 9:00 AM\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(startTime);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(endTime);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // Open edit form with appointment data\n        console.log('Appointment clicked for editing:', appointment);\n        const editData = {\n            id: appointment.id,\n            title: appointment.title,\n            description: appointment.description,\n            patient_id: appointment.patient_id,\n            healthcare_professional_id: appointment.healthcare_professional_id,\n            start_time: appointment.start_time,\n            end_time: appointment.end_time,\n            type: appointment.type,\n            status: appointment.status,\n            price: appointment.price\n        };\n        setAppointmentFormData(editData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const isEditing = data.id;\n            const url = isEditing ? \"/api/appointments/\".concat(data.id) : '/api/appointments';\n            const method = isEditing ? 'PUT' : 'POST';\n            // Remove id from data for API call\n            const { id, ...submitData } = data;\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(submitData)\n            });\n            if (!response.ok) throw new Error(\"Failed to \".concat(isEditing ? 'update' : 'create', \" appointment\"));\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error(\"Error \".concat(data.id ? 'updating' : 'creating', \" appointment:\"), error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancelAppointment = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        if (!confirm('Tem certeza que deseja cancelar esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointment.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'cancelled'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to cancel appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta cancelada com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error canceling appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao cancelar consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStartConsultation = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        if (!confirm(\"Deseja iniciar o atendimento para \".concat(appointment.patient_name, \"?\"))) return;\n        try {\n            // Update appointment status to in_progress\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointment.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'in_progress'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to start consultation');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Atendimento iniciado com sucesso.\"\n            });\n            // Navigate to medical record screen\n            router.push(\"/dashboard/prontuario/\".concat(appointment.patient_id, \"?appointment_id=\").concat(appointment.id));\n        } catch (error) {\n            console.error('Error starting consultation:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao iniciar atendimento.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR,\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    // appointmentCounts={appointmentCounts}\n                                                    // clinicSettings={clinicSettings || undefined}\n                                                    // appointments={allAppointments}\n                                                    className: \"rounded-md border-0 shadow-none w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatDateBR)(selectedDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 514,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border transition-colors space-y-3 sm:space-y-0 \".concat(appointment.status === 'cancelled' ? 'bg-red-50 border-red-200 hover:bg-red-100' : 'bg-card/50 hover:bg-card cursor-pointer'),\n                                                            onClick: appointment.status !== 'cancelled' ? ()=>handleAppointmentClick(appointment) : undefined,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.start_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 530,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.end_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 533,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 529,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 539,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 543,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : appointment.status === 'in_progress' ? 'default' : 'outline',\n                                                                                    className: \"text-xs\",\n                                                                                    children: [\n                                                                                        appointment.status === 'scheduled' && 'Agendado',\n                                                                                        appointment.status === 'confirmed' && 'Confirmado',\n                                                                                        appointment.status === 'completed' && 'Concluído',\n                                                                                        appointment.status === 'cancelled' && 'Cancelado',\n                                                                                        appointment.status === 'in_progress' && 'Em Andamento'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 551,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium whitespace-nowrap\",\n                                                                                    children: [\n                                                                                        \"R$ \",\n                                                                                        appointment.price.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 568,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.status !== 'cancelled' && appointment.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-1\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs\",\n                                                                                    onClick: (e)=>handleStartConsultation(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 583,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"Iniciar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 577,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                                    onClick: (e)=>handleCancelAppointment(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 593,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"Cancelar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 549,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            appointments: allAppointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 609,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 621,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"xLZJ7pt+Iz6ZUtzTScoCCOXUoEE=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});