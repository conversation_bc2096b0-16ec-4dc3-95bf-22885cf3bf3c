import crypto from 'crypto';

/**
 * Medical data encryption utility
 * Uses AES-256-GCM for secure encryption of sensitive medical records
 */

const ALGORITHM = 'aes-256-cbc';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits

/**
 * Get encryption key from environment variable
 * In production, this should be stored securely (e.g., AWS KMS, Azure Key Vault)
 */
function getEncryptionKey(): Buffer {
  const key = process.env.MEDICAL_RECORDS_ENCRYPTION_KEY;
  
  if (!key) {
    throw new Error('MEDICAL_RECORDS_ENCRYPTION_KEY environment variable is required');
  }
  
  // If key is hex string, convert to buffer
  if (key.length === 64) {
    return Buffer.from(key, 'hex');
  }
  
  // If key is base64, convert to buffer
  if (key.length === 44 && key.endsWith('=')) {
    return Buffer.from(key, 'base64');
  }
  
  // Otherwise, hash the key to ensure it's 32 bytes
  return crypto.createHash('sha256').update(key).digest();
}

/**
 * Generate a random encryption key (for initial setup)
 */
export function generateEncryptionKey(): string {
  return crypto.randomBytes(KEY_LENGTH).toString('hex');
}

/**
 * Encrypt sensitive medical data
 */
export function encryptMedicalData(plaintext: string): string {
  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);

    const cipher = crypto.createCipher(ALGORITHM, key);

    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // For CBC mode, we only need IV + encrypted data
    const combined = Buffer.concat([
      iv,
      Buffer.from(encrypted, 'hex')
    ]);

    return combined.toString('base64');
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt medical data');
  }
}

/**
 * Decrypt sensitive medical data
 */
export function decryptMedicalData(encryptedData: string): string {
  try {
    const key = getEncryptionKey();
    const combined = Buffer.from(encryptedData, 'base64');

    // Extract IV and encrypted data (no tag for CBC mode)
    const iv = combined.subarray(0, IV_LENGTH);
    const encrypted = combined.subarray(IV_LENGTH);

    const decipher = crypto.createDecipher(ALGORITHM, key);

    let decrypted = decipher.update(encrypted, undefined, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt medical data');
  }
}

/**
 * Validate if data is encrypted (basic check)
 */
export function isEncrypted(data: string): boolean {
  try {
    // Check if it's a valid base64 string with expected length
    const buffer = Buffer.from(data, 'base64');
    return buffer.length > IV_LENGTH; // Only IV + encrypted data for CBC mode
  } catch {
    return false;
  }
}

/**
 * Safely encrypt medical data with error handling
 */
export function safeEncryptMedicalData(plaintext: string): string {
  if (!plaintext || plaintext.trim() === '') {
    return plaintext;
  }
  
  try {
    return encryptMedicalData(plaintext);
  } catch (error) {
    console.error('Safe encryption failed, storing as plaintext:', error);
    // In production, you might want to fail here instead of storing plaintext
    return plaintext;
  }
}

/**
 * Safely decrypt medical data with error handling
 */
export function safeDecryptMedicalData(encryptedData: string): string {
  if (!encryptedData || encryptedData.trim() === '') {
    return encryptedData;
  }
  
  // If data doesn't look encrypted, return as-is (for backward compatibility)
  if (!isEncrypted(encryptedData)) {
    return encryptedData;
  }
  
  try {
    return decryptMedicalData(encryptedData);
  } catch (error) {
    console.error('Safe decryption failed:', error);
    // Return a placeholder or the encrypted data (depending on your security policy)
    return '[ENCRYPTED DATA - DECRYPTION FAILED]';
  }
}
