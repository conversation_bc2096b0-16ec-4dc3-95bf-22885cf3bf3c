"use client"

import React, { useState, useEffect } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar as CalendarIcon, Clock, Plus, Users, Grid3X3 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatDateBR, formatTimeBR, formatDateTimeBR, getAppointmentStatusBR, getAppointmentTypeBR, toLocalISOString } from '@/lib/date-utils';
import { makeAuthenticatedRequest } from '@/lib/api-client';
import FullCalendarView from '@/components/FullCalendarView';
import AppointmentForm from '@/components/AppointmentForm';
import type { DateSelectArg } from '@fullcalendar/core';

type Patient = {
  id: string;
  name: string;
  email?: string;
  phone?: string;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty: string | null;
  is_active: boolean;
};

type Procedure = {
  id: string;
  name: string;
  description: string | null;
  default_price: number | null;
  duration_minutes: number | null;
};

type Appointment = {
  id: string;
  title: string;
  description: string | null;
  patient_id: string;
  patient_name?: string;
  healthcare_professional_id: string | null;
  healthcare_professional_name?: string;
  start_time: string;
  end_time: string;
  type: string;
  status: string;
  price: number | null;
};

type ClinicSettings = {
  working_hours_start: string;
  working_hours_end: string;
  working_days: number[];
  appointment_duration_minutes: number;
  allow_weekend_appointments: boolean;
};

const AgendaPage = () => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [allAppointments, setAllAppointments] = useState<Appointment[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [healthcareProfessionals, setHealthcareProfessionals] = useState<HealthcareProfessional[]>([]);
  const [procedures, setProcedures] = useState<Procedure[]>([]);
  const [clinicSettings, setClinicSettings] = useState<ClinicSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [appointmentFormOpen, setAppointmentFormOpen] = useState(false);
  const [currentView, setCurrentView] = useState<'calendar' | 'fullcalendar'>('calendar');
  const [appointmentFormData, setAppointmentFormData] = useState<any>(null);
  const { toast } = useToast();

  // Fetch initial data that doesn't depend on selected date
  useEffect(() => {
    fetchAllAppointments();
    fetchPatients();
    fetchHealthcareProfessionals();
    fetchProcedures();
    fetchClinicSettings();
  }, []);

  // Fetch date-specific appointments when selected date changes
  useEffect(() => {
    fetchAppointments();
  }, [selectedDate]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      const response = await makeAuthenticatedRequest(`/api/appointments?date=${dateStr}`);
      if (!response.ok) throw new Error('Failed to fetch appointments');
      const result = await response.json();
      const data = result.data || result;
      setAppointments(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      setAppointments([]);
      toast({
        title: "Erro ao carregar consultas",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchPatients = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/patients');
      if (!response.ok) throw new Error('Failed to fetch patients');
      const result = await response.json();
      console.log('Patients API response:', result);
      const data = result.data || result;
      setPatients(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching patients:', error);
      setPatients([]);
      toast({
        title: "Erro ao carregar pacientes",
        description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',
        variant: "destructive"
      });
    }
  };

  const fetchHealthcareProfessionals = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/healthcare-professionals');
      if (!response.ok) throw new Error('Failed to fetch healthcare professionals');
      const result = await response.json();
      const data = result.data || result;
      setHealthcareProfessionals(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching healthcare professionals:', error);
      setHealthcareProfessionals([]);
    }
  };

  const fetchProcedures = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/procedures');
      if (!response.ok) throw new Error('Failed to fetch procedures');
      const result = await response.json();
      const data = result.data || result;
      setProcedures(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching procedures:', error);
      setProcedures([]);
    }
  };

  const fetchAllAppointments = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/appointments');
      if (!response.ok) throw new Error('Failed to fetch all appointments');
      const result = await response.json();
      const data = result.data || result;
      setAllAppointments(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching all appointments:', error);
      setAllAppointments([]);
    }
  };

  const fetchClinicSettings = async () => {
    try {
      const response = await makeAuthenticatedRequest('/api/clinic-settings');
      if (!response.ok) throw new Error('Failed to fetch clinic settings');
      const result = await response.json();
      const data = result.data || result;
      setClinicSettings(data);
    } catch (error) {
      console.error('Error fetching clinic settings:', error);
      // Set default settings if fetch fails
      setClinicSettings({
        working_hours_start: '08:00',
        working_hours_end: '18:00',
        working_days: [1, 2, 3, 4, 5],
        appointment_duration_minutes: 30,
        allow_weekend_appointments: false
      });
    }
  };

  const handleAppointmentCreate = (selectInfo?: DateSelectArg) => {
    const initialData: any = {};

    if (selectInfo) {
      // FullCalendar provides dates in local timezone, use them directly
      initialData.start_time = toLocalISOString(selectInfo.start);
      initialData.end_time = toLocalISOString(selectInfo.end);
    } else if (selectedDate) {
      // Create appointment for selected date at 9:00 AM
      const startTime = new Date(selectedDate);
      startTime.setHours(9, 0, 0, 0);
      const endTime = new Date(startTime);
      endTime.setMinutes(endTime.getMinutes() + 30);

      initialData.start_time = toLocalISOString(startTime);
      initialData.end_time = toLocalISOString(endTime);
    }

    setAppointmentFormData(initialData);
    setAppointmentFormOpen(true);
  };

  const handleAppointmentClick = (appointment: Appointment) => {
    // Open edit form with appointment data
    console.log('Appointment clicked for editing:', appointment);

    const editData = {
      id: appointment.id,
      title: appointment.title,
      description: appointment.description,
      patient_id: appointment.patient_id,
      healthcare_professional_id: appointment.healthcare_professional_id,
      start_time: appointment.start_time,
      end_time: appointment.end_time,
      type: appointment.type,
      status: appointment.status,
      price: appointment.price,
    };

    setAppointmentFormData(editData);
    setAppointmentFormOpen(true);
  };

  const handleAppointmentUpdate = async (appointmentId: string, newStart: Date, newEnd: Date) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          start_time: newStart.toISOString(),
          end_time: newEnd.toISOString(),
        })
      });

      if (!response.ok) throw new Error('Failed to update appointment');

      // Refresh both daily and all appointments for calendar views
      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      console.error('Error updating appointment:', error);
      throw error;
    }
  };

  const handleAppointmentSubmit = async (data: any) => {
    try {
      const isEditing = data.id;
      const url = isEditing ? `/api/appointments/${data.id}` : '/api/appointments';
      const method = isEditing ? 'PUT' : 'POST';

      // Remove id from data for API call
      const { id, ...submitData } = data;

      const response = await makeAuthenticatedRequest(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(submitData)
      });

      if (!response.ok) throw new Error(`Failed to ${isEditing ? 'update' : 'create'} appointment`);

      // Refresh both daily and all appointments for calendar views
      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      console.error(`Error ${data.id ? 'updating' : 'creating'} appointment:`, error);
      throw error;
    }
  };

  // Calculate appointment counts for calendar indicators
  const appointmentCounts = React.useMemo(() => {
    const counts: Record<string, number> = {};
    appointments.forEach(appointment => {
      const date = new Date(appointment.start_time).toISOString().split('T')[0];
      counts[date] = (counts[date] || 0) + 1;
    });
    return counts;
  }, [appointments]);

  const updateAppointmentStatus = async (appointmentId: string, status: string) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (!response.ok) throw new Error('Failed to update appointment status');

      toast({
        title: "Sucesso!",
        description: "Status da consulta atualizado.",
      });

      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      console.error('Error updating appointment status:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar status da consulta.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteAppointment = async (appointmentId: string) => {
    if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;

    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete appointment');

      toast({
        title: "Sucesso!",
        description: "Consulta excluída com sucesso.",
      });

      fetchAppointments();
      fetchAllAppointments();
    } catch (error) {
      console.error('Error deleting appointment:', error);
      toast({
        title: "Erro",
        description: "Erro ao excluir consulta.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Agenda</h1>
          <p className="text-muted-foreground">Gerencie suas consultas e horários</p>
        </div>

        <div className="flex items-center gap-2">
          <Button onClick={() => handleAppointmentCreate()}>
            <Plus className="mr-2 h-4 w-4" />
            Nova Consulta
          </Button>
        </div>
      </div>

      <Tabs
        value={currentView}
        onValueChange={(value) => {
          // Prevent tab switching during loading to avoid race conditions
          if (loading) {
            toast({
              title: "Aguarde",
              description: "Aguarde o carregamento dos dados antes de trocar de aba.",
              variant: "default",
            });
            return;
          }
          setCurrentView(value as 'calendar' | 'fullcalendar');
        }}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger
            value="calendar"
            className={`flex items-center gap-2 text-xs sm:text-sm ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={loading}
          >
            <CalendarIcon className="h-4 w-4" />
            <span className="hidden xs:inline">Calendário</span>
            <span className="xs:hidden">Cal.</span>
            {loading && <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1"></div>}
          </TabsTrigger>
          <TabsTrigger
            value="fullcalendar"
            className={`flex items-center gap-2 text-xs sm:text-sm ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={loading}
          >
            <Grid3X3 className="h-4 w-4" />
            <span className="hidden xs:inline">Agenda Completa</span>
            <span className="xs:hidden">Agenda</span>
            {loading && <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1"></div>}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-1">
              <CardHeader>
                <CardTitle className="flex items-center text-lg">
                  <CalendarIcon className="mr-2 h-5 w-5 text-primary" />
                  Calendário
                </CardTitle>
              </CardHeader>
              <CardContent >
                <div className="flex justify-center">
                  <Calendar
                    mode="single"
                    locale={ptBR}
                    selected={selectedDate}
                    onSelect={(date) => date && setSelectedDate(date)}
                    // appointmentCounts={appointmentCounts}
                    // clinicSettings={clinicSettings || undefined}
                    // appointments={allAppointments}
                    className="rounded-md border-0 shadow-none w-full"

                  />
                </div>
              </CardContent>
            </Card>

            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-lg">
                    <Clock className="mr-2 h-5 w-5 text-primary" />
                    <span className="hidden sm:inline">Consultas - {formatDateBR(selectedDate)}</span>
                    <span className="sm:hidden">Consultas</span>
                  </CardTitle>
                  <CardDescription>
                    {appointments.length} consulta(s) agendada(s) para este dia
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Clock className="mx-auto h-12 w-12 mb-4 opacity-50 animate-spin" />
                      <p>Carregando consultas...</p>
                    </div>
                  ) : appointments.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
                      <p>Nenhuma consulta agendada para este dia</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {appointments.map((appointment) => (
                        <div
                          key={appointment.id}
                          className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border bg-card/50 hover:bg-card transition-colors cursor-pointer space-y-3 sm:space-y-0"
                          onClick={() => handleAppointmentClick(appointment)}
                        >
                          <div className="flex items-center space-x-4 min-w-0 flex-1">
                            <div className="flex flex-col items-center flex-shrink-0">
                              <span className="text-sm font-medium">
                                {formatTimeBR(appointment.start_time)}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {formatTimeBR(appointment.end_time)}
                              </span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium truncate">{appointment.title}</h3>
                              <p className="text-sm text-muted-foreground truncate">
                                {appointment.patient_name}
                              </p>
                              {appointment.healthcare_professional_name && (
                                <p className="text-xs text-muted-foreground truncate hidden sm:block">
                                  {appointment.healthcare_professional_name}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0">
                            <Badge
                              variant={
                                appointment.status === 'confirmed' ? 'default' :
                                appointment.status === 'completed' ? 'secondary' :
                                appointment.status === 'cancelled' ? 'destructive' :
                                'outline'
                              }
                              className="text-xs"
                            >
                              {appointment.status === 'scheduled' && 'Agendado'}
                              {appointment.status === 'confirmed' && 'Confirmado'}
                              {appointment.status === 'completed' && 'Concluído'}
                              {appointment.status === 'cancelled' && 'Cancelado'}
                              {appointment.status === 'in_progress' && 'Em Andamento'}
                            </Badge>
                            {appointment.price && (
                              <span className="text-sm font-medium whitespace-nowrap">
                                R$ {appointment.price.toFixed(2)}
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="fullcalendar" className="space-y-6 mt-6">
          <FullCalendarView
            appointments={allAppointments}
            healthcareProfessionals={healthcareProfessionals}
            onAppointmentCreate={handleAppointmentCreate}
            onAppointmentClick={handleAppointmentClick}
            onAppointmentUpdate={handleAppointmentUpdate}
            loading={loading}
          />
        </TabsContent>
      </Tabs>

      <AppointmentForm
        open={appointmentFormOpen}
        onOpenChange={setAppointmentFormOpen}
        patients={patients}
        healthcareProfessionals={healthcareProfessionals}
        procedures={procedures}
        initialData={appointmentFormData}
        onSubmit={handleAppointmentSubmit}
        loading={loading}
      />
    </div>
  );
};

export default AgendaPage;
