"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/AppointmentForm.tsx":
/*!********************************************!*\
  !*** ./src/components/AppointmentForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/datetime-input */ \"(app-pages-browser)/./src/components/ui/datetime-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=addMinutes!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addMinutes.js\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useAsyncOperation */ \"(app-pages-browser)/./src/hooks/useAsyncOperation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Utility functions for Brazilian timezone (UTC-3)\nconst brazilianTimezoneOffset = -3 * 60; // -3 hours in minutes\nconst toBrazilianTime = (date)=>{\n    if (!date) return null;\n    // Se for string, converte para Date\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    // Cria uma nova data ajustada para UTC-3\n    const utcTime = dateObj.getTime() + dateObj.getTimezoneOffset() * 60000;\n    const brazilianTime = new Date(utcTime + brazilianTimezoneOffset * 60000);\n    return brazilianTime;\n};\nconst formatDateTimeForBrazilianInput = (dateTime)=>{\n    if (!dateTime) return '';\n    try {\n        const date = toBrazilianTime(dateTime);\n        if (!date || isNaN(date.getTime())) return '';\n        // Formato para datetime-local: YYYY-MM-DDTHH:mm\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n        return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"T\").concat(hours, \":\").concat(minutes);\n    } catch (error) {\n        console.error('[DATETIME] Error formatting date:', error);\n        return '';\n    }\n};\nconst parseBrazilianDateTime = (dateTimeString)=>{\n    if (!dateTimeString) return null;\n    try {\n        // Se já tem timezone, usa diretamente\n        if (dateTimeString.includes('T') && (dateTimeString.includes('Z') || dateTimeString.includes('-03:00'))) {\n            return new Date(dateTimeString);\n        }\n        // Se é formato datetime-local (YYYY-MM-DDTHH:mm), adiciona timezone brasileiro\n        if (dateTimeString.includes('T')) {\n            return new Date(dateTimeString + ':00-03:00');\n        }\n        // Fallback para outros formatos\n        const date = new Date(dateTimeString);\n        return toBrazilianTime(date);\n    } catch (error) {\n        console.error('[DATETIME] Error parsing date:', error);\n        return null;\n    }\n};\nconst AppointmentForm = (param)=>{\n    let { open, onOpenChange, patients, healthcareProfessionals, procedures, initialData, onSubmit, loading = false } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('scheduling');\n    const [searchProcedure, setSearchProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedProcedures, setSelectedProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Check if we're in editing mode\n    const isEditing = Boolean(initialData === null || initialData === void 0 ? void 0 : initialData.id);\n    const { execute: submitForm, loading: submitting } = (0,_hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission)({\n        successMessage: isEditing ? 'Consulta atualizada com sucesso!' : 'Consulta agendada com sucesso!',\n        errorMessage: isEditing ? 'Erro ao atualizar consulta' : 'Erro ao agendar consulta'\n    });\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_16__.appointmentSchema),\n        defaultValues: {\n            title: '',\n            description: '',\n            patient_id: '',\n            healthcare_professional_id: '',\n            start_time: '',\n            end_time: '',\n            type: 'consultation',\n            status: 'scheduled',\n            notes: '',\n            has_recurrence: false,\n            recurrence_type: 'weekly',\n            recurrence_interval: 1,\n            recurrence_days: [],\n            recurrence_end_type: 'never',\n            recurrence_end_date: '',\n            recurrence_count: 1\n        }\n    });\n    const { control, handleSubmit, watch, setValue, reset, formState: { errors } } = form;\n    const watchedValues = watch();\n    // Initialize form with initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (initialData && open) {\n                console.log('[DATETIME] Initializing form with data:', initialData);\n                const formattedData = {\n                    ...initialData,\n                    start_time: formatDateTimeForBrazilianInput(initialData.start_time),\n                    end_time: formatDateTimeForBrazilianInput(initialData.end_time),\n                    patient_id: initialData.patient_id || '',\n                    healthcare_professional_id: initialData.healthcare_professional_id || ''\n                };\n                console.log('[DATETIME] Formatted data for form:', formattedData);\n                reset(formattedData);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        initialData,\n        open,\n        reset\n    ]);\n    // Auto-generate title when patient and type change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (watchedValues.patient_id && watchedValues.type) {\n                const patient = patients.find({\n                    \"AppointmentForm.useEffect.patient\": (p)=>p.id === watchedValues.patient_id\n                }[\"AppointmentForm.useEffect.patient\"]);\n                if (patient) {\n                    const typeLabel = watchedValues.type === 'consultation' ? 'Consulta' : 'Retorno';\n                    setValue('title', \"\".concat(typeLabel, \" - \").concat(patient.name));\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.patient_id,\n        watchedValues.type,\n        patients,\n        setValue\n    ]);\n    // Auto-calculate end time based on start time (Brazilian timezone)\n    // Only auto-calculate if end_time is not already set (e.g., from FullCalendar selection)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            console.log('[DATETIME] Auto-calculating end time, start_time:', watchedValues.start_time, 'end_time:', watchedValues.end_time);\n            if (watchedValues.start_time && !watchedValues.end_time) {\n                try {\n                    const startTime = parseBrazilianDateTime(watchedValues.start_time);\n                    console.log('[DATETIME] Parsed start time:', startTime, 'Valid:', startTime && !isNaN(startTime.getTime()));\n                    if (startTime && !isNaN(startTime.getTime())) {\n                        const totalDuration = selectedProcedures.reduce({\n                            \"AppointmentForm.useEffect.totalDuration\": (total, proc)=>{\n                                const procedure = procedures.find({\n                                    \"AppointmentForm.useEffect.totalDuration.procedure\": (p)=>p.id === proc.procedure_id\n                                }[\"AppointmentForm.useEffect.totalDuration.procedure\"]);\n                                return total + ((procedure === null || procedure === void 0 ? void 0 : procedure.duration_minutes) || 30) * proc.quantity;\n                            }\n                        }[\"AppointmentForm.useEffect.totalDuration\"], 30); // Default 30 minutes if no procedures\n                        console.log('[DATETIME] Total duration:', totalDuration, 'minutes');\n                        const endTime = (0,_barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__.addMinutes)(startTime, totalDuration);\n                        const formattedEndTime = formatDateTimeForBrazilianInput(endTime);\n                        console.log('[DATETIME] Calculated end time:', endTime, 'Formatted:', formattedEndTime);\n                        setValue('end_time', formattedEndTime);\n                    }\n                } catch (error) {\n                    console.error('[DATETIME] Error calculating end time:', error);\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.start_time,\n        watchedValues.end_time,\n        selectedProcedures,\n        procedures,\n        setValue\n    ]);\n    const filteredProcedures = procedures.filter((procedure)=>procedure.name.toLowerCase().includes(searchProcedure.toLowerCase()) || procedure.description && procedure.description.toLowerCase().includes(searchProcedure.toLowerCase()));\n    const addProcedure = (procedure)=>{\n        const existingIndex = selectedProcedures.findIndex((p)=>p.procedure_id === procedure.id);\n        if (existingIndex >= 0) {\n            // Increase quantity if already exists\n            const updated = [\n                ...selectedProcedures\n            ];\n            updated[existingIndex].quantity += 1;\n            updated[existingIndex].total_price = updated[existingIndex].quantity * updated[existingIndex].unit_price;\n            setSelectedProcedures(updated);\n        } else {\n            // Add new procedure\n            const newProcedure = {\n                procedure_id: procedure.id,\n                procedure_name: procedure.name,\n                quantity: 1,\n                unit_price: procedure.default_price || 0,\n                total_price: procedure.default_price || 0\n            };\n            setSelectedProcedures([\n                ...selectedProcedures,\n                newProcedure\n            ]);\n        }\n    };\n    const updateProcedure = (index, field, value)=>{\n        const updated = [\n            ...selectedProcedures\n        ];\n        updated[index][field] = value;\n        updated[index].total_price = updated[index].quantity * updated[index].unit_price;\n        setSelectedProcedures(updated);\n    };\n    const removeProcedure = (index)=>{\n        setSelectedProcedures(selectedProcedures.filter((_, i)=>i !== index));\n    };\n    const getTotalPrice = ()=>{\n        return selectedProcedures.reduce((total, proc)=>total + proc.total_price, 0);\n    };\n    const onFormSubmit = async (data)=>{\n        console.log('[DATETIME] Form submitted with data:', data);\n        try {\n            // Parse and validate dates in Brazilian timezone\n            const startTime = parseBrazilianDateTime(data.start_time);\n            const endTime = parseBrazilianDateTime(data.end_time);\n            if (!startTime || !endTime) {\n                toast({\n                    title: 'Erro',\n                    description: 'Datas inválidas. Por favor, verifique os horários.',\n                    variant: 'destructive'\n                });\n                return;\n            }\n            // Convert to ISO string with Brazilian timezone\n            const formattedData = {\n                ...data,\n                start_time: startTime.toISOString(),\n                end_time: endTime.toISOString()\n            };\n            console.log('[DATETIME] Formatted data for submission:', formattedData);\n            await submitForm(async ()=>{\n                const appointmentData = {\n                    ...formattedData,\n                    procedures: selectedProcedures,\n                    total_price: getTotalPrice(),\n                    status: 'scheduled',\n                    ...isEditing && (initialData === null || initialData === void 0 ? void 0 : initialData.id) && {\n                        id: initialData.id\n                    }\n                };\n                console.log('[DATETIME] Final appointment data:', appointmentData);\n                await onSubmit(appointmentData);\n                // Reset form\n                reset();\n                setSelectedProcedures([]);\n                setActiveTab('scheduling');\n                onOpenChange(false);\n            });\n        } catch (error) {\n            console.error('[DATETIME] Error submitting form:', error);\n            toast({\n                title: 'Erro',\n                description: 'Erro ao processar os dados. Por favor, tente novamente.',\n                variant: 'destructive'\n            });\n        }\n    };\n    const toggleRecurrenceDay = (day)=>{\n        const currentDays = watchedValues.recurrence_days || [];\n        const newDays = currentDays.includes(day) ? currentDays.filter((d)=>d !== day) : [\n            ...currentDays,\n            day\n        ];\n        setValue('recurrence_days', newDays);\n    };\n    const weekDays = [\n        {\n            value: 1,\n            label: 'D'\n        },\n        {\n            value: 2,\n            label: 'S'\n        },\n        {\n            value: 3,\n            label: 'T'\n        },\n        {\n            value: 4,\n            label: 'Q'\n        },\n        {\n            value: 5,\n            label: 'Q'\n        },\n        {\n            value: 6,\n            label: 'S'\n        },\n        {\n            value: 7,\n            label: 'S'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[800px] max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: isEditing ? 'Editar Consulta' : 'Agendar Nova Consulta'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                            children: isEditing ? 'Edite os dados da consulta' : 'Preencha os dados para agendar uma nova consulta'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit(onFormSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"scheduling\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Agendamento\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"procedures\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Procedimentos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"scheduling\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"patient\",\n                                                            children: \"Paciente *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"patient_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o paciente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 383,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: patient.id,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                lineNumber: 389,\n                                                                                                columnNumber: 33\n                                                                                            }, void 0),\n                                                                                            patient.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0)\n                                                                                }, patient.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 29\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.patient_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.patient_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"professional\",\n                                                            children: \"Profissional\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"healthcare_professional_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o profissional\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: professional.id,\n                                                                                    children: [\n                                                                                        professional.name,\n                                                                                        professional.specialty && \" - \".concat(professional.specialty)\n                                                                                    ]\n                                                                                }, professional.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 420,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.healthcare_professional_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.healthcare_professional_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"type\",\n                                                            children: \"Tipo de Consulta *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"type\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"consultation\",\n                                                                                    children: \"Consulta\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"follow_up\",\n                                                                                    children: \"Retorno\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 27\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.type.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"T\\xedtulo *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"title\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"title\",\n                                                                    ...field\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.title.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"start_time\",\n                                                            children: \"Data/Hora In\\xedcio *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"start_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"start_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.start_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.start_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"end_time\",\n                                                            children: \"Data/Hora Fim *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"end_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"end_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.end_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.end_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"Descri\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                    name: \"description\",\n                                                    control: control,\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                            id: \"description\",\n                                                            placeholder: \"Descri\\xe7\\xe3o da consulta...\",\n                                                            ...field\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        errors.description.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                name: \"has_recurrence\",\n                                                                control: control,\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n                                                                        id: \"has_recurrence\",\n                                                                        checked: field.value,\n                                                                        onCheckedChange: field.onChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 25\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"has_recurrence\",\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Recorr\\xeancia Personalizada\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                watchedValues.has_recurrence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir a cada:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_interval\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            type: \"number\",\n                                                                                            min: \"1\",\n                                                                                            ...field,\n                                                                                            onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                            className: \"w-20\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 583,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_type\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                                            value: field.value,\n                                                                                            onValueChange: field.onChange,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                        lineNumber: 598,\n                                                                                                        columnNumber: 35\n                                                                                                    }, void 0)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 597,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"daily\",\n                                                                                                            children: \"dia(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 601,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"weekly\",\n                                                                                                            children: \"semana(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 602,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"monthly\",\n                                                                                                            children: \"m\\xeas(es)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 603,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 600,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 596,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 592,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                watchedValues.recurrence_type === 'weekly' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex gap-1\",\n                                                                            children: weekDays.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: (watchedValues.recurrence_days || []).includes(day.value) ? 'default' : 'outline',\n                                                                                    size: \"sm\",\n                                                                                    className: \"w-8 h-8 p-0\",\n                                                                                    onClick: ()=>toggleRecurrenceDay(day.value),\n                                                                                    children: day.label\n                                                                                }, day.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 616,\n                                                                                    columnNumber: 31\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Termina em:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                    name: \"recurrence_end_type\",\n                                                                    control: control,\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroup, {\n                                                                            value: field.value,\n                                                                            onValueChange: field.onChange,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"never\",\n                                                                                            id: \"never\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 641,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"never\",\n                                                                                            children: \"Nunca\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 640,\n                                                                                    columnNumber: 25\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"date\",\n                                                                                            id: \"end_date\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 646,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_date\",\n                                                                                            children: \"Em\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 647,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_end_date\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"date\",\n                                                                                                    ...field,\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'date',\n                                                                                                    className: \"w-40\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 652,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 648,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 645,\n                                                                                    columnNumber: 29\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"count\",\n                                                                                            id: \"end_count\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 663,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_count\",\n                                                                                            children: \"Ap\\xf3s\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 664,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_count\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"number\",\n                                                                                                    min: \"1\",\n                                                                                                    ...field,\n                                                                                                    onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'count',\n                                                                                                    className: \"w-20\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 669,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 665,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            children: \"ocorr\\xeancias\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 679,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 662,\n                                                                                    columnNumber: 29\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 27\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"procedures\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        placeholder: \"Buscar procedimentos...\",\n                                                        value: searchProcedure,\n                                                        onChange: (e)=>setSearchProcedure(e.target.value),\n                                                        className: \"flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto\",\n                                                children: filteredProcedures.map((procedure)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                        className: \"cursor-pointer hover:bg-accent/50\",\n                                                        onClick: ()=>addProcedure(procedure),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: procedure.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            procedure.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                                children: procedure.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 710,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-2\",\n                                                                                children: [\n                                                                                    procedure.default_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"secondary\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            \"R$ \",\n                                                                                            procedure.default_price.toFixed(2)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 714,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    procedure.duration_minutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            procedure.duration_minutes,\n                                                                                            \"min\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 719,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 712,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 726,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, procedure.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            selectedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: \"Procedimentos Selecionados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            selectedProcedures.map((proc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 p-3 border rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium\",\n                                                                                children: proc.procedure_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 743,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Qtd:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 747,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    value: proc.quantity,\n                                                                                    onChange: (e)=>updateProcedure(index, 'quantity', parseInt(e.target.value) || 1),\n                                                                                    className: \"w-16 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 748,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Pre\\xe7o:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 758,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"0\",\n                                                                                    step: \"0.01\",\n                                                                                    value: proc.unit_price,\n                                                                                    onChange: (e)=>updateProcedure(index, 'unit_price', parseFloat(e.target.value) || 0),\n                                                                                    className: \"w-24 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 759,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 757,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                proc.total_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>removeProcedure(index),\n                                                                            className: \"h-8 w-8 p-0 text-destructive hover:text-destructive\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 780,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center pt-3 border-t\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold\",\n                                                                        children: [\n                                                                            \"R$ \",\n                                                                            getTotalPrice().toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 787,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    disabled: submitting || loading,\n                                    children: submitting ? isEditing ? 'Salvando...' : 'Agendando...' : isEditing ? 'Salvar Alterações' : 'Agendar Consulta'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n        lineNumber: 351,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppointmentForm, \"0fK7CMPQBa8UlSZ3bOvox60mpoM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AppointmentForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentForm);\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0FwcG9pbnRtZW50Rm9ybS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVtRDtBQUNHO0FBQ0E7QUFDcUU7QUFDM0U7QUFDRjtBQUNBO0FBQ007QUFDbUQ7QUFDdkI7QUFDaUI7QUFDbkQ7QUFDTTtBQUNxQjtBQUNWO0FBQ3NDO0FBQ3hEO0FBQ0M7QUFHa0M7QUFDbEI7QUFFOUQsbURBQW1EO0FBQ25ELE1BQU02QywwQkFBMEIsQ0FBQyxJQUFJLElBQUksc0JBQXNCO0FBRS9ELE1BQU1DLGtCQUFrQixDQUFDQztJQUN2QixJQUFJLENBQUNBLE1BQU0sT0FBTztJQUVsQixvQ0FBb0M7SUFDcEMsTUFBTUMsVUFBVSxPQUFPRCxTQUFTLFdBQVcsSUFBSUUsS0FBS0YsUUFBUUE7SUFFNUQseUNBQXlDO0lBQ3pDLE1BQU1HLFVBQVVGLFFBQVFHLE9BQU8sS0FBTUgsUUFBUUksaUJBQWlCLEtBQUs7SUFDbkUsTUFBTUMsZ0JBQWdCLElBQUlKLEtBQUtDLFVBQVdMLDBCQUEwQjtJQUVwRSxPQUFPUTtBQUNUO0FBRUEsTUFBTUMsa0NBQWtDLENBQUNDO0lBQ3ZDLElBQUksQ0FBQ0EsVUFBVSxPQUFPO0lBRXRCLElBQUk7UUFDRixNQUFNUixPQUFPRCxnQkFBZ0JTO1FBQzdCLElBQUksQ0FBQ1IsUUFBUVMsTUFBTVQsS0FBS0ksT0FBTyxLQUFLLE9BQU87UUFFM0MsZ0RBQWdEO1FBQ2hELE1BQU1NLE9BQU9WLEtBQUtXLFdBQVc7UUFDN0IsTUFBTUMsUUFBUUMsT0FBT2IsS0FBS2MsUUFBUSxLQUFLLEdBQUdDLFFBQVEsQ0FBQyxHQUFHO1FBQ3RELE1BQU1DLE1BQU1ILE9BQU9iLEtBQUtpQixPQUFPLElBQUlGLFFBQVEsQ0FBQyxHQUFHO1FBQy9DLE1BQU1HLFFBQVFMLE9BQU9iLEtBQUttQixRQUFRLElBQUlKLFFBQVEsQ0FBQyxHQUFHO1FBQ2xELE1BQU1LLFVBQVVQLE9BQU9iLEtBQUtxQixVQUFVLElBQUlOLFFBQVEsQ0FBQyxHQUFHO1FBRXRELE9BQU8sR0FBV0gsT0FBUkYsTUFBSyxLQUFZTSxPQUFUSixPQUFNLEtBQVVNLE9BQVBGLEtBQUksS0FBWUksT0FBVEYsT0FBTSxLQUFXLE9BQVJFO0lBQzdDLEVBQUUsT0FBT0UsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMscUNBQXFDQTtRQUNuRCxPQUFPO0lBQ1Q7QUFDRjtBQUVBLE1BQU1FLHlCQUF5QixDQUFDQztJQUM5QixJQUFJLENBQUNBLGdCQUFnQixPQUFPO0lBRTVCLElBQUk7UUFDRixzQ0FBc0M7UUFDdEMsSUFBSUEsZUFBZUMsUUFBUSxDQUFDLFFBQVNELENBQUFBLGVBQWVDLFFBQVEsQ0FBQyxRQUFRRCxlQUFlQyxRQUFRLENBQUMsU0FBUSxHQUFJO1lBQ3ZHLE9BQU8sSUFBSXhCLEtBQUt1QjtRQUNsQjtRQUVBLCtFQUErRTtRQUMvRSxJQUFJQSxlQUFlQyxRQUFRLENBQUMsTUFBTTtZQUNoQyxPQUFPLElBQUl4QixLQUFLdUIsaUJBQWlCO1FBQ25DO1FBRUEsZ0NBQWdDO1FBQ2hDLE1BQU16QixPQUFPLElBQUlFLEtBQUt1QjtRQUN0QixPQUFPMUIsZ0JBQWdCQztJQUN6QixFQUFFLE9BQU9zQixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2hELE9BQU87SUFDVDtBQUNGO0FBMkNBLE1BQU1LLGtCQUFrRDtRQUFDLEVBQ3ZEQyxJQUFJLEVBQ0pDLFlBQVksRUFDWkMsUUFBUSxFQUNSQyx1QkFBdUIsRUFDdkJDLFVBQVUsRUFDVkMsV0FBVyxFQUNYQyxRQUFRLEVBQ1JDLFVBQVUsS0FBSyxFQUNoQjs7SUFDQyxNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHMUMsMkRBQVFBO0lBQzFCLE1BQU0sQ0FBQzJDLFdBQVdDLGFBQWEsR0FBR3BGLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3FGLGlCQUFpQkMsbUJBQW1CLEdBQUd0RiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUN1RixvQkFBb0JDLHNCQUFzQixHQUFHeEYsK0NBQVFBLENBQXlCLEVBQUU7SUFFdkYsaUNBQWlDO0lBQ2pDLE1BQU15RixZQUFZQyxRQUFRWCx3QkFBQUEsa0NBQUFBLFlBQWFZLEVBQUU7SUFFekMsTUFBTSxFQUFFQyxTQUFTQyxVQUFVLEVBQUVaLFNBQVNhLFVBQVUsRUFBRSxHQUFHbkQsNEVBQWlCQSxDQUFDO1FBQ3JFb0QsZ0JBQWdCTixZQUFZLHFDQUFxQztRQUNqRU8sY0FBY1AsWUFBWSwrQkFBK0I7SUFDM0Q7SUFFQSxNQUFNUSxPQUFPL0YseURBQU9BLENBQXNCO1FBQ3hDZ0csVUFBVTlGLG9FQUFXQSxDQUFDc0MsZ0VBQWlCQTtRQUN2Q3lELGVBQWU7WUFDYkMsT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFlBQVk7WUFDWkMsNEJBQTRCO1lBQzVCQyxZQUFZO1lBQ1pDLFVBQVU7WUFDVkMsTUFBTTtZQUNOQyxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsZ0JBQWdCO1lBQ2hCQyxpQkFBaUI7WUFDakJDLHFCQUFxQjtZQUNyQkMsaUJBQWlCLEVBQUU7WUFDbkJDLHFCQUFxQjtZQUNyQkMscUJBQXFCO1lBQ3JCQyxrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFFQyxLQUFLLEVBQUVDLFdBQVcsRUFBRUMsTUFBTSxFQUFFLEVBQUUsR0FBR3pCO0lBRWpGLE1BQU0wQixnQkFBZ0JMO0lBRXRCLG9DQUFvQztJQUNwQ3JILGdEQUFTQTtxQ0FBQztZQUNSLElBQUk4RSxlQUFlTCxNQUFNO2dCQUN2QkwsUUFBUXVELEdBQUcsQ0FBQywyQ0FBMkM3QztnQkFFdkQsTUFBTThDLGdCQUFnQjtvQkFDcEIsR0FBRzlDLFdBQVc7b0JBQ2R5QixZQUFZbkQsZ0NBQWdDMEIsWUFBWXlCLFVBQVU7b0JBQ2xFQyxVQUFVcEQsZ0NBQWdDMEIsWUFBWTBCLFFBQVE7b0JBQzlESCxZQUFZdkIsWUFBWXVCLFVBQVUsSUFBSTtvQkFDdENDLDRCQUE0QnhCLFlBQVl3QiwwQkFBMEIsSUFBSTtnQkFDeEU7Z0JBRUFsQyxRQUFRdUQsR0FBRyxDQUFDLHVDQUF1Q0M7Z0JBQ25ETCxNQUFNSztZQUNSO1FBQ0Y7b0NBQUc7UUFBQzlDO1FBQWFMO1FBQU04QztLQUFNO0lBRTdCLG1EQUFtRDtJQUNuRHZILGdEQUFTQTtxQ0FBQztZQUNSLElBQUkwSCxjQUFjckIsVUFBVSxJQUFJcUIsY0FBY2pCLElBQUksRUFBRTtnQkFDbEQsTUFBTW9CLFVBQVVsRCxTQUFTbUQsSUFBSTt5REFBQ0MsQ0FBQUEsSUFBS0EsRUFBRXJDLEVBQUUsS0FBS2dDLGNBQWNyQixVQUFVOztnQkFDcEUsSUFBSXdCLFNBQVM7b0JBQ1gsTUFBTUcsWUFBWU4sY0FBY2pCLElBQUksS0FBSyxpQkFBaUIsYUFBYTtvQkFDdkVhLFNBQVMsU0FBUyxHQUFrQk8sT0FBZkcsV0FBVSxPQUFrQixPQUFiSCxRQUFRSSxJQUFJO2dCQUNsRDtZQUNGO1FBQ0Y7b0NBQUc7UUFBQ1AsY0FBY3JCLFVBQVU7UUFBRXFCLGNBQWNqQixJQUFJO1FBQUU5QjtRQUFVMkM7S0FBUztJQUVyRSxtRUFBbUU7SUFDbkUseUZBQXlGO0lBQ3pGdEgsZ0RBQVNBO3FDQUFDO1lBQ1JvRSxRQUFRdUQsR0FBRyxDQUFDLHFEQUFxREQsY0FBY25CLFVBQVUsRUFBRSxhQUFhbUIsY0FBY2xCLFFBQVE7WUFFOUgsSUFBSWtCLGNBQWNuQixVQUFVLElBQUksQ0FBQ21CLGNBQWNsQixRQUFRLEVBQUU7Z0JBQ3ZELElBQUk7b0JBQ0YsTUFBTTBCLFlBQVk3RCx1QkFBdUJxRCxjQUFjbkIsVUFBVTtvQkFFakVuQyxRQUFRdUQsR0FBRyxDQUFDLGlDQUFpQ08sV0FBVyxVQUFVQSxhQUFhLENBQUM1RSxNQUFNNEUsVUFBVWpGLE9BQU87b0JBRXZHLElBQUlpRixhQUFhLENBQUM1RSxNQUFNNEUsVUFBVWpGLE9BQU8sS0FBSzt3QkFDNUMsTUFBTWtGLGdCQUFnQjdDLG1CQUFtQjhDLE1BQU07dUVBQUMsQ0FBQ0MsT0FBT0M7Z0NBQ3RELE1BQU1DLFlBQVkxRCxXQUFXaUQsSUFBSTt5RkFBQ0MsQ0FBQUEsSUFBS0EsRUFBRXJDLEVBQUUsS0FBSzRDLEtBQUtFLFlBQVk7O2dDQUNqRSxPQUFPSCxRQUFRLENBQUNFLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0UsZ0JBQWdCLEtBQUksRUFBQyxJQUFLSCxLQUFLSSxRQUFROzRCQUNwRTtzRUFBRyxLQUFLLHNDQUFzQzt3QkFFOUN0RSxRQUFRdUQsR0FBRyxDQUFDLDhCQUE4QlEsZUFBZTt3QkFFekQsTUFBTVEsVUFBVW5HLHVGQUFVQSxDQUFDMEYsV0FBV0M7d0JBQ3RDLE1BQU1TLG1CQUFtQnhGLGdDQUFnQ3VGO3dCQUV6RHZFLFFBQVF1RCxHQUFHLENBQUMsbUNBQW1DZ0IsU0FBUyxjQUFjQzt3QkFDdEV0QixTQUFTLFlBQVlzQjtvQkFDdkI7Z0JBQ0YsRUFBRSxPQUFPekUsT0FBTztvQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDBDQUEwQ0E7Z0JBQzFEO1lBQ0Y7UUFDRjtvQ0FBRztRQUFDdUQsY0FBY25CLFVBQVU7UUFBRW1CLGNBQWNsQixRQUFRO1FBQUVsQjtRQUFvQlQ7UUFBWXlDO0tBQVM7SUFFL0YsTUFBTXVCLHFCQUFxQmhFLFdBQVdpRSxNQUFNLENBQUNQLENBQUFBLFlBQzNDQSxVQUFVTixJQUFJLENBQUNjLFdBQVcsR0FBR3hFLFFBQVEsQ0FBQ2EsZ0JBQWdCMkQsV0FBVyxPQUNoRVIsVUFBVW5DLFdBQVcsSUFBSW1DLFVBQVVuQyxXQUFXLENBQUMyQyxXQUFXLEdBQUd4RSxRQUFRLENBQUNhLGdCQUFnQjJELFdBQVc7SUFHcEcsTUFBTUMsZUFBZSxDQUFDVDtRQUNwQixNQUFNVSxnQkFBZ0IzRCxtQkFBbUI0RCxTQUFTLENBQUNuQixDQUFBQSxJQUFLQSxFQUFFUyxZQUFZLEtBQUtELFVBQVU3QyxFQUFFO1FBRXZGLElBQUl1RCxpQkFBaUIsR0FBRztZQUN0QixzQ0FBc0M7WUFDdEMsTUFBTUUsVUFBVTttQkFBSTdEO2FBQW1CO1lBQ3ZDNkQsT0FBTyxDQUFDRixjQUFjLENBQUNQLFFBQVEsSUFBSTtZQUNuQ1MsT0FBTyxDQUFDRixjQUFjLENBQUNHLFdBQVcsR0FBR0QsT0FBTyxDQUFDRixjQUFjLENBQUNQLFFBQVEsR0FBR1MsT0FBTyxDQUFDRixjQUFjLENBQUNJLFVBQVU7WUFDeEc5RCxzQkFBc0I0RDtRQUN4QixPQUFPO1lBQ0wsb0JBQW9CO1lBQ3BCLE1BQU1HLGVBQXFDO2dCQUN6Q2QsY0FBY0QsVUFBVTdDLEVBQUU7Z0JBQzFCNkQsZ0JBQWdCaEIsVUFBVU4sSUFBSTtnQkFDOUJTLFVBQVU7Z0JBQ1ZXLFlBQVlkLFVBQVVpQixhQUFhLElBQUk7Z0JBQ3ZDSixhQUFhYixVQUFVaUIsYUFBYSxJQUFJO1lBQzFDO1lBQ0FqRSxzQkFBc0I7bUJBQUlEO2dCQUFvQmdFO2FBQWE7UUFDN0Q7SUFDRjtJQUVBLE1BQU1HLGtCQUFrQixDQUFDQyxPQUFlQyxPQUFrQ0M7UUFDeEUsTUFBTVQsVUFBVTtlQUFJN0Q7U0FBbUI7UUFDdkM2RCxPQUFPLENBQUNPLE1BQU0sQ0FBQ0MsTUFBTSxHQUFHQztRQUN4QlQsT0FBTyxDQUFDTyxNQUFNLENBQUNOLFdBQVcsR0FBR0QsT0FBTyxDQUFDTyxNQUFNLENBQUNoQixRQUFRLEdBQUdTLE9BQU8sQ0FBQ08sTUFBTSxDQUFDTCxVQUFVO1FBQ2hGOUQsc0JBQXNCNEQ7SUFDeEI7SUFFQSxNQUFNVSxrQkFBa0IsQ0FBQ0g7UUFDdkJuRSxzQkFBc0JELG1CQUFtQndELE1BQU0sQ0FBQyxDQUFDZ0IsR0FBR0MsSUFBTUEsTUFBTUw7SUFDbEU7SUFFQSxNQUFNTSxnQkFBZ0I7UUFDcEIsT0FBTzFFLG1CQUFtQjhDLE1BQU0sQ0FBQyxDQUFDQyxPQUFPQyxPQUFTRCxRQUFRQyxLQUFLYyxXQUFXLEVBQUU7SUFDOUU7SUFFQSxNQUFNYSxlQUFlLE9BQU9DO1FBQzFCOUYsUUFBUXVELEdBQUcsQ0FBQyx3Q0FBd0N1QztRQUVwRCxJQUFJO1lBQ0YsaURBQWlEO1lBQ2pELE1BQU1oQyxZQUFZN0QsdUJBQXVCNkYsS0FBSzNELFVBQVU7WUFDeEQsTUFBTW9DLFVBQVV0RSx1QkFBdUI2RixLQUFLMUQsUUFBUTtZQUVwRCxJQUFJLENBQUMwQixhQUFhLENBQUNTLFNBQVM7Z0JBQzFCMUQsTUFBTTtvQkFDSmtCLE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2IrRCxTQUFTO2dCQUNYO2dCQUNBO1lBQ0Y7WUFFQSxnREFBZ0Q7WUFDaEQsTUFBTXZDLGdCQUFnQjtnQkFDcEIsR0FBR3NDLElBQUk7Z0JBQ1AzRCxZQUFZMkIsVUFBVWtDLFdBQVc7Z0JBQ2pDNUQsVUFBVW1DLFFBQVF5QixXQUFXO1lBQy9CO1lBRUFoRyxRQUFRdUQsR0FBRyxDQUFDLDZDQUE2Q0M7WUFFekQsTUFBTWhDLFdBQVc7Z0JBQ2YsTUFBTXlFLGtCQUFrQjtvQkFDdEIsR0FBR3pDLGFBQWE7b0JBQ2hCL0MsWUFBWVM7b0JBQ1o4RCxhQUFhWTtvQkFDYnRELFFBQVE7b0JBQ1IsR0FBSWxCLGNBQWFWLHdCQUFBQSxrQ0FBQUEsWUFBYVksRUFBRSxLQUFJO3dCQUFFQSxJQUFJWixZQUFZWSxFQUFFO29CQUFDLENBQUM7Z0JBQzVEO2dCQUVBdEIsUUFBUXVELEdBQUcsQ0FBQyxzQ0FBc0MwQztnQkFDbEQsTUFBTXRGLFNBQVNzRjtnQkFFZixhQUFhO2dCQUNiOUM7Z0JBQ0FoQyxzQkFBc0IsRUFBRTtnQkFDeEJKLGFBQWE7Z0JBQ2JULGFBQWE7WUFDZjtRQUNGLEVBQUUsT0FBT1AsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMscUNBQXFDQTtZQUNuRGMsTUFBTTtnQkFDSmtCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2IrRCxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTUcsc0JBQXNCLENBQUN6RztRQUMzQixNQUFNMEcsY0FBYzdDLGNBQWNYLGVBQWUsSUFBSSxFQUFFO1FBQ3ZELE1BQU15RCxVQUFVRCxZQUFZaEcsUUFBUSxDQUFDVixPQUNqQzBHLFlBQVl6QixNQUFNLENBQUMyQixDQUFBQSxJQUFLQSxNQUFNNUcsT0FDOUI7ZUFBSTBHO1lBQWExRztTQUFJO1FBQ3pCeUQsU0FBUyxtQkFBbUJrRDtJQUM5QjtJQUVBLE1BQU1FLFdBQVc7UUFDZjtZQUFFZCxPQUFPO1lBQUdlLE9BQU87UUFBSTtRQUN2QjtZQUFFZixPQUFPO1lBQUdlLE9BQU87UUFBSTtRQUN2QjtZQUFFZixPQUFPO1lBQUdlLE9BQU87UUFBSTtRQUN2QjtZQUFFZixPQUFPO1lBQUdlLE9BQU87UUFBSTtRQUN2QjtZQUFFZixPQUFPO1lBQUdlLE9BQU87UUFBSTtRQUN2QjtZQUFFZixPQUFPO1lBQUdlLE9BQU87UUFBSTtRQUN2QjtZQUFFZixPQUFPO1lBQUdlLE9BQU87UUFBSTtLQUN4QjtJQUVELHFCQUNFLDhEQUFDdksseURBQU1BO1FBQUNxRSxNQUFNQTtRQUFNQyxjQUFjQTtrQkFDaEMsNEVBQUNyRSxnRUFBYUE7WUFBQ3VLLFdBQVU7OzhCQUN2Qiw4REFBQ3BLLCtEQUFZQTs7c0NBQ1gsOERBQUNDLDhEQUFXQTtzQ0FBRStFLFlBQVksb0JBQW9COzs7Ozs7c0NBQzlDLDhEQUFDbEYsb0VBQWlCQTtzQ0FDZmtGLFlBQVksK0JBQStCOzs7Ozs7Ozs7Ozs7OEJBSWhELDhEQUFDUTtvQkFBS2pCLFVBQVVxQyxhQUFhNkM7O3NDQUMzQiw4REFBQzlJLHFEQUFJQTs0QkFBQ3lJLE9BQU8xRTs0QkFBVzJGLGVBQWUxRjs0QkFBY3lGLFdBQVU7OzhDQUM3RCw4REFBQ3ZKLHlEQUFRQTtvQ0FBQ3VKLFdBQVU7O3NEQUNsQiw4REFBQ3RKLDREQUFXQTs0Q0FBQ3NJLE9BQU07NENBQWFnQixXQUFVOzs4REFDeEMsOERBQUM1SSxxSUFBUUE7b0RBQUM0SSxXQUFVOzs7Ozs7Z0RBQVk7Ozs7Ozs7c0RBR2xDLDhEQUFDdEosNERBQVdBOzRDQUFDc0ksT0FBTTs0Q0FBYWdCLFdBQVU7OzhEQUN4Qyw4REFBQzFJLHFJQUFXQTtvREFBQzBJLFdBQVU7Ozs7OztnREFBWTs7Ozs7Ozs7Ozs7Ozs4Q0FLdkMsOERBQUN4Siw0REFBV0E7b0NBQUN3SSxPQUFNO29DQUFhZ0IsV0FBVTs7c0RBQ3hDLDhEQUFDRTs0Q0FBSUYsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ2hLLHVEQUFLQTs0REFBQ21LLFNBQVE7c0VBQVU7Ozs7OztzRUFDekIsOERBQUM3Syx3REFBVUE7NERBQ1QrSCxNQUFLOzREQUNMZCxTQUFTQTs0REFDVDZELFFBQVE7b0VBQUMsRUFBRXJCLEtBQUssRUFBRTtxRkFDaEIsOERBQUM3SSx5REFBTUE7b0VBQUM4SSxPQUFPRCxNQUFNQyxLQUFLO29FQUFFaUIsZUFBZWxCLE1BQU1zQixRQUFROztzRkFDdkQsOERBQUNoSyxnRUFBYUE7c0ZBQ1osNEVBQUNDLDhEQUFXQTtnRkFBQ2dLLGFBQVk7Ozs7Ozs7Ozs7O3NGQUUzQiw4REFBQ25LLGdFQUFhQTtzRkFDWDRELFNBQVN3RyxHQUFHLENBQUN0RCxDQUFBQSx3QkFDWiw4REFBQzdHLDZEQUFVQTtvRkFBa0I0SSxPQUFPL0IsUUFBUW5DLEVBQUU7OEZBQzVDLDRFQUFDb0Y7d0ZBQUlGLFdBQVU7OzBHQUNiLDhEQUFDM0kscUlBQUlBO2dHQUFDMkksV0FBVTs7Ozs7OzRGQUNmL0MsUUFBUUksSUFBSTs7Ozs7OzttRkFIQUosUUFBUW5DLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0RBV3BDK0IsT0FBT3BCLFVBQVUsa0JBQ2hCLDhEQUFDMEI7NERBQUU2QyxXQUFVOzs4RUFDWCw4REFBQ3RJLHFJQUFXQTtvRUFBQ3NJLFdBQVU7Ozs7OztnRUFDdEJuRCxPQUFPcEIsVUFBVSxDQUFDK0UsT0FBTzs7Ozs7Ozs7Ozs7Ozs4REFLaEMsOERBQUNOO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ2hLLHVEQUFLQTs0REFBQ21LLFNBQVE7c0VBQWU7Ozs7OztzRUFDOUIsOERBQUM3Syx3REFBVUE7NERBQ1QrSCxNQUFLOzREQUNMZCxTQUFTQTs0REFDVDZELFFBQVE7b0VBQUMsRUFBRXJCLEtBQUssRUFBRTtxRkFDaEIsOERBQUM3SSx5REFBTUE7b0VBQUM4SSxPQUFPRCxNQUFNQyxLQUFLO29FQUFFaUIsZUFBZWxCLE1BQU1zQixRQUFROztzRkFDdkQsOERBQUNoSyxnRUFBYUE7c0ZBQ1osNEVBQUNDLDhEQUFXQTtnRkFBQ2dLLGFBQVk7Ozs7Ozs7Ozs7O3NGQUUzQiw4REFBQ25LLGdFQUFhQTtzRkFDWDZELHdCQUNFa0UsTUFBTSxDQUFDdUMsQ0FBQUEsT0FBUUEsS0FBS0MsU0FBUyxFQUM3QkgsR0FBRyxDQUFDSSxDQUFBQSw2QkFDSCw4REFBQ3ZLLDZEQUFVQTtvRkFBdUI0SSxPQUFPMkIsYUFBYTdGLEVBQUU7O3dGQUNyRDZGLGFBQWF0RCxJQUFJO3dGQUNqQnNELGFBQWFDLFNBQVMsSUFBSSxNQUE2QixPQUF2QkQsYUFBYUMsU0FBUzs7bUZBRnhDRCxhQUFhN0YsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3REFTM0MrQixPQUFPbkIsMEJBQTBCLGtCQUNoQyw4REFBQ3lCOzREQUFFNkMsV0FBVTs7OEVBQ1gsOERBQUN0SSxxSUFBV0E7b0VBQUNzSSxXQUFVOzs7Ozs7Z0VBQ3RCbkQsT0FBT25CLDBCQUEwQixDQUFDOEUsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFNbEQsOERBQUNOOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ0U7b0RBQUlGLFdBQVU7O3NFQUNiLDhEQUFDaEssdURBQUtBOzREQUFDbUssU0FBUTtzRUFBTzs7Ozs7O3NFQUN0Qiw4REFBQzdLLHdEQUFVQTs0REFDVCtILE1BQUs7NERBQ0xkLFNBQVNBOzREQUNUNkQsUUFBUTtvRUFBQyxFQUFFckIsS0FBSyxFQUFFO3FGQUNoQiw4REFBQzdJLHlEQUFNQTtvRUFBQzhJLE9BQU9ELE1BQU1DLEtBQUs7b0VBQUVpQixlQUFlbEIsTUFBTXNCLFFBQVE7O3NGQUN2RCw4REFBQ2hLLGdFQUFhQTtzRkFDWiw0RUFBQ0MsOERBQVdBOzs7Ozs7Ozs7O3NGQUVkLDhEQUFDSCxnRUFBYUE7OzhGQUNaLDhEQUFDQyw2REFBVUE7b0ZBQUM0SSxPQUFNOzhGQUFlOzs7Ozs7OEZBQ2pDLDhEQUFDNUksNkRBQVVBO29GQUFDNEksT0FBTTs4RkFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3dEQUtyQ25DLE9BQU9oQixJQUFJLGtCQUNWLDhEQUFDc0I7NERBQUU2QyxXQUFVOzs4RUFDWCw4REFBQ3RJLHFJQUFXQTtvRUFBQ3NJLFdBQVU7Ozs7OztnRUFDdEJuRCxPQUFPaEIsSUFBSSxDQUFDMkUsT0FBTzs7Ozs7Ozs7Ozs7Ozs4REFLMUIsOERBQUNOO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ2hLLHVEQUFLQTs0REFBQ21LLFNBQVE7c0VBQVE7Ozs7OztzRUFDdkIsOERBQUM3Syx3REFBVUE7NERBQ1QrSCxNQUFLOzREQUNMZCxTQUFTQTs0REFDVDZELFFBQVE7b0VBQUMsRUFBRXJCLEtBQUssRUFBRTtxRkFDaEIsOERBQUNoSix1REFBS0E7b0VBQ0orRSxJQUFHO29FQUNGLEdBQUdpRSxLQUFLOzs7Ozs7Ozs7Ozs7d0RBSWRsQyxPQUFPdEIsS0FBSyxrQkFDWCw4REFBQzRCOzREQUFFNkMsV0FBVTs7OEVBQ1gsOERBQUN0SSxxSUFBV0E7b0VBQUNzSSxXQUFVOzs7Ozs7Z0VBQ3RCbkQsT0FBT3RCLEtBQUssQ0FBQ2lGLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTTdCLDhEQUFDTjs0Q0FBSUYsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ2hLLHVEQUFLQTs0REFBQ21LLFNBQVE7c0VBQWE7Ozs7OztzRUFDNUIsOERBQUM3Syx3REFBVUE7NERBQ1QrSCxNQUFLOzREQUNMZCxTQUFTQTs0REFDVDZELFFBQVE7b0VBQUMsRUFBRXJCLEtBQUssRUFBRTtxRkFDaEIsOERBQUM1SCx5RUFBYUE7b0VBQ1oyRCxJQUFHO29FQUNIa0UsT0FBT0QsTUFBTUMsS0FBSztvRUFDbEJxQixVQUFVdEIsTUFBTXNCLFFBQVE7b0VBQ3hCQyxhQUFZOzs7Ozs7Ozs7Ozs7d0RBSWpCekQsT0FBT2xCLFVBQVUsa0JBQ2hCLDhEQUFDd0I7NERBQUU2QyxXQUFVOzs4RUFDWCw4REFBQ3RJLHFJQUFXQTtvRUFBQ3NJLFdBQVU7Ozs7OztnRUFDdEJuRCxPQUFPbEIsVUFBVSxDQUFDNkUsT0FBTzs7Ozs7Ozs7Ozs7Ozs4REFLaEMsOERBQUNOO29EQUFJRixXQUFVOztzRUFDYiw4REFBQ2hLLHVEQUFLQTs0REFBQ21LLFNBQVE7c0VBQVc7Ozs7OztzRUFDMUIsOERBQUM3Syx3REFBVUE7NERBQ1QrSCxNQUFLOzREQUNMZCxTQUFTQTs0REFDVDZELFFBQVE7b0VBQUMsRUFBRXJCLEtBQUssRUFBRTtxRkFDaEIsOERBQUM1SCx5RUFBYUE7b0VBQ1oyRCxJQUFHO29FQUNIa0UsT0FBT0QsTUFBTUMsS0FBSztvRUFDbEJxQixVQUFVdEIsTUFBTXNCLFFBQVE7b0VBQ3hCQyxhQUFZOzs7Ozs7Ozs7Ozs7d0RBSWpCekQsT0FBT2pCLFFBQVEsa0JBQ2QsOERBQUN1Qjs0REFBRTZDLFdBQVU7OzhFQUNYLDhEQUFDdEkscUlBQVdBO29FQUFDc0ksV0FBVTs7Ozs7O2dFQUN0Qm5ELE9BQU9qQixRQUFRLENBQUM0RSxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU1oQyw4REFBQ047NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDaEssdURBQUtBO29EQUFDbUssU0FBUTs4REFBYzs7Ozs7OzhEQUM3Qiw4REFBQzdLLHdEQUFVQTtvREFDVCtILE1BQUs7b0RBQ0xkLFNBQVNBO29EQUNUNkQsUUFBUTs0REFBQyxFQUFFckIsS0FBSyxFQUFFOzZFQUNoQiw4REFBQzlJLDZEQUFRQTs0REFDUDZFLElBQUc7NERBQ0h3RixhQUFZOzREQUNYLEdBQUd2QixLQUFLOzs7Ozs7Ozs7Ozs7Z0RBSWRsQyxPQUFPckIsV0FBVyxrQkFDakIsOERBQUMyQjtvREFBRTZDLFdBQVU7O3NFQUNYLDhEQUFDdEkscUlBQVdBOzREQUFDc0ksV0FBVTs7Ozs7O3dEQUN0Qm5ELE9BQU9yQixXQUFXLENBQUNnRixPQUFPOzs7Ozs7Ozs7Ozs7O3NEQU1qQyw4REFBQzdKLHNEQUFJQTs7OERBQ0gsOERBQUNFLDREQUFVQTs4REFDVCw0RUFBQ3FKO3dEQUFJRixXQUFVOzswRUFDYiw4REFBQzFLLHdEQUFVQTtnRUFDVCtILE1BQUs7Z0VBQ0xkLFNBQVNBO2dFQUNUNkQsUUFBUTt3RUFBQyxFQUFFckIsS0FBSyxFQUFFO3lGQUNoQiw4REFBQy9ILDhEQUFRQTt3RUFDUDhELElBQUc7d0VBQ0grRixTQUFTOUIsTUFBTUMsS0FBSzt3RUFDcEI4QixpQkFBaUIvQixNQUFNc0IsUUFBUTs7Ozs7Ozs7Ozs7OzBFQUlyQyw4REFBQ3JLLHVEQUFLQTtnRUFBQ21LLFNBQVE7Z0VBQWlCSCxXQUFVOzBFQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0RBTW5FbEQsY0FBY2QsY0FBYyxrQkFDM0IsOERBQUNwRiw2REFBV0E7b0RBQUNvSixXQUFVOztzRUFDckIsOERBQUNFOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ0U7b0VBQUlGLFdBQVU7O3NGQUNiLDhEQUFDaEssdURBQUtBO3NGQUFDOzs7Ozs7c0ZBQ1AsOERBQUNrSzs0RUFBSUYsV0FBVTs7OEZBQ2IsOERBQUMxSyx3REFBVUE7b0ZBQ1QrSCxNQUFLO29GQUNMZCxTQUFTQTtvRkFDVDZELFFBQVE7NEZBQUMsRUFBRXJCLEtBQUssRUFBRTs2R0FDaEIsOERBQUNoSix1REFBS0E7NEZBQ0o4RixNQUFLOzRGQUNMa0YsS0FBSTs0RkFDSCxHQUFHaEMsS0FBSzs0RkFDVHNCLFVBQVUsQ0FBQ1csSUFBTWpDLE1BQU1zQixRQUFRLENBQUNZLFNBQVNELEVBQUVFLE1BQU0sQ0FBQ2xDLEtBQUssS0FBSzs0RkFDNURnQixXQUFVOzs7Ozs7Ozs7Ozs7OEZBSWhCLDhEQUFDMUssd0RBQVVBO29GQUNUK0gsTUFBSztvRkFDTGQsU0FBU0E7b0ZBQ1Q2RCxRQUFROzRGQUFDLEVBQUVyQixLQUFLLEVBQUU7NkdBQ2hCLDhEQUFDN0kseURBQU1BOzRGQUFDOEksT0FBT0QsTUFBTUMsS0FBSzs0RkFBRWlCLGVBQWVsQixNQUFNc0IsUUFBUTs7OEdBQ3ZELDhEQUFDaEssZ0VBQWFBOzhHQUNaLDRFQUFDQyw4REFBV0E7Ozs7Ozs7Ozs7OEdBRWQsOERBQUNILGdFQUFhQTs7c0hBQ1osOERBQUNDLDZEQUFVQTs0R0FBQzRJLE9BQU07c0hBQVE7Ozs7OztzSEFDMUIsOERBQUM1SSw2REFBVUE7NEdBQUM0SSxPQUFNO3NIQUFTOzs7Ozs7c0hBQzNCLDhEQUFDNUksNkRBQVVBOzRHQUFDNEksT0FBTTtzSEFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dFQVF2Q2xDLGNBQWNiLGVBQWUsS0FBSywwQkFDakMsOERBQUNpRTtvRUFBSUYsV0FBVTs7c0ZBQ2IsOERBQUNoSyx1REFBS0E7c0ZBQUM7Ozs7OztzRkFDUCw4REFBQ2tLOzRFQUFJRixXQUFVO3NGQUNaRixTQUFTUyxHQUFHLENBQUN0SCxDQUFBQSxvQkFDWiw4REFBQ25ELHlEQUFNQTtvRkFFTCtGLE1BQUs7b0ZBQ0wwRCxTQUFTLENBQUN6QyxjQUFjWCxlQUFlLElBQUksRUFBRSxFQUFFeEMsUUFBUSxDQUFDVixJQUFJK0YsS0FBSyxJQUFJLFlBQVk7b0ZBQ2pGbUMsTUFBSztvRkFDTG5CLFdBQVU7b0ZBQ1ZvQixTQUFTLElBQU0xQixvQkFBb0J6RyxJQUFJK0YsS0FBSzs4RkFFM0MvRixJQUFJOEcsS0FBSzttRkFQTDlHLElBQUkrRixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NFQWUxQiw4REFBQ2tCOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ2hLLHVEQUFLQTs4RUFBQzs7Ozs7OzhFQUNQLDhEQUFDVix3REFBVUE7b0VBQ1QrSCxNQUFLO29FQUNMZCxTQUFTQTtvRUFDVDZELFFBQVE7NEVBQUMsRUFBRXJCLEtBQUssRUFBRTs2RkFDaEIsOERBQUM5SCxtRUFBVUE7NEVBQUMrSCxPQUFPRCxNQUFNQyxLQUFLOzRFQUFFaUIsZUFBZWxCLE1BQU1zQixRQUFROzs4RkFFL0QsOERBQUNIO29GQUFJRixXQUFVOztzR0FDYiw4REFBQzlJLHVFQUFjQTs0RkFBQzhILE9BQU07NEZBQVFsRSxJQUFHOzs7Ozs7c0dBQ2pDLDhEQUFDOUUsdURBQUtBOzRGQUFDbUssU0FBUTtzR0FBUTs7Ozs7Ozs7Ozs7OzhGQUdyQiw4REFBQ0Q7b0ZBQUlGLFdBQVU7O3NHQUNiLDhEQUFDOUksdUVBQWNBOzRGQUFDOEgsT0FBTTs0RkFBT2xFLElBQUc7Ozs7OztzR0FDaEMsOERBQUM5RSx1REFBS0E7NEZBQUNtSyxTQUFRO3NHQUFXOzs7Ozs7c0dBQzFCLDhEQUFDN0ssd0RBQVVBOzRGQUNUK0gsTUFBSzs0RkFDTGQsU0FBU0E7NEZBQ1Q2RCxRQUFRO29HQUFDLEVBQUVyQixLQUFLLEVBQUU7cUhBQ2hCLDhEQUFDaEosdURBQUtBO29HQUNKOEYsTUFBSztvR0FDSixHQUFHa0QsS0FBSztvR0FDVHNDLFVBQVV2RSxjQUFjVixtQkFBbUIsS0FBSztvR0FDaEQ0RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEZBTWxCLDhEQUFDRTtvRkFBSUYsV0FBVTs7c0dBQ2IsOERBQUM5SSx1RUFBY0E7NEZBQUM4SCxPQUFNOzRGQUFRbEUsSUFBRzs7Ozs7O3NHQUNqQyw4REFBQzlFLHVEQUFLQTs0RkFBQ21LLFNBQVE7c0dBQVk7Ozs7OztzR0FDM0IsOERBQUM3Syx3REFBVUE7NEZBQ1QrSCxNQUFLOzRGQUNMZCxTQUFTQTs0RkFDVDZELFFBQVE7b0dBQUMsRUFBRXJCLEtBQUssRUFBRTtxSEFDaEIsOERBQUNoSix1REFBS0E7b0dBQ0o4RixNQUFLO29HQUNMa0YsS0FBSTtvR0FDSCxHQUFHaEMsS0FBSztvR0FDVHNCLFVBQVUsQ0FBQ1csSUFBTWpDLE1BQU1zQixRQUFRLENBQUNZLFNBQVNELEVBQUVFLE1BQU0sQ0FBQ2xDLEtBQUssS0FBSztvR0FDNURxQyxVQUFVdkUsY0FBY1YsbUJBQW1CLEtBQUs7b0dBQ2hENEQsV0FBVTs7Ozs7Ozs7Ozs7O3NHQUloQiw4REFBQ2hLLHVEQUFLQTtzR0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVd6Qiw4REFBQ1EsNERBQVdBO29DQUFDd0ksT0FBTTtvQ0FBYWdCLFdBQVU7OENBQ3hDLDRFQUFDRTt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUNFO2dEQUFJRixXQUFVOztrRUFDYiw4REFBQ3ZJLHFJQUFNQTt3REFBQ3VJLFdBQVU7Ozs7OztrRUFDbEIsOERBQUNqSyx1REFBS0E7d0RBQ0p1SyxhQUFZO3dEQUNadEIsT0FBT3hFO3dEQUNQNkYsVUFBVSxDQUFDVyxJQUFNdkcsbUJBQW1CdUcsRUFBRUUsTUFBTSxDQUFDbEMsS0FBSzt3REFDbERnQixXQUFVOzs7Ozs7Ozs7Ozs7MERBSWQsOERBQUNFO2dEQUFJRixXQUFVOzBEQUNaL0IsbUJBQW1Cc0MsR0FBRyxDQUFDNUMsQ0FBQUEsMEJBQ3RCLDhEQUFDaEgsc0RBQUlBO3dEQUFvQnFKLFdBQVU7d0RBQW9Db0IsU0FBUyxJQUFNaEQsYUFBYVQ7a0VBQ2pHLDRFQUFDL0csNkRBQVdBOzREQUFDb0osV0FBVTtzRUFDckIsNEVBQUNFO2dFQUFJRixXQUFVOztrRkFDYiw4REFBQ0U7d0VBQUlGLFdBQVU7OzBGQUNiLDhEQUFDc0I7Z0ZBQUd0QixXQUFVOzBGQUF1QnJDLFVBQVVOLElBQUk7Ozs7Ozs0RUFDbERNLFVBQVVuQyxXQUFXLGtCQUNwQiw4REFBQzJCO2dGQUFFNkMsV0FBVTswRkFBc0NyQyxVQUFVbkMsV0FBVzs7Ozs7OzBGQUUxRSw4REFBQzBFO2dGQUFJRixXQUFVOztvRkFDWnJDLFVBQVVpQixhQUFhLGtCQUN0Qiw4REFBQzdILHdEQUFLQTt3RkFBQ3dJLFNBQVE7d0ZBQVlTLFdBQVU7OzRGQUFVOzRGQUN6Q3JDLFVBQVVpQixhQUFhLENBQUMyQyxPQUFPLENBQUM7Ozs7Ozs7b0ZBR3ZDNUQsVUFBVUUsZ0JBQWdCLGtCQUN6Qiw4REFBQzlHLHdEQUFLQTt3RkFBQ3dJLFNBQVE7d0ZBQVVTLFdBQVU7OzRGQUNoQ3JDLFVBQVVFLGdCQUFnQjs0RkFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrRkFLcEMsOERBQUMvSCx5REFBTUE7d0VBQUNxTCxNQUFLO3dFQUFLNUIsU0FBUTt3RUFBUVMsV0FBVTtrRkFDMUMsNEVBQUN6SSxxSUFBSUE7NEVBQUN5SSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3VEQXRCYnJDLFVBQVU3QyxFQUFFOzs7Ozs7Ozs7OzRDQThCMUJKLG1CQUFtQjhHLE1BQU0sR0FBRyxtQkFDM0IsOERBQUM3SyxzREFBSUE7O2tFQUNILDhEQUFDRSw0REFBVUE7a0VBQ1QsNEVBQUNDLDJEQUFTQTs0REFBQ2tKLFdBQVU7c0VBQVU7Ozs7Ozs7Ozs7O2tFQUVqQyw4REFBQ3BKLDZEQUFXQTt3REFBQ29KLFdBQVU7OzREQUNwQnRGLG1CQUFtQjZGLEdBQUcsQ0FBQyxDQUFDN0MsTUFBTW9CLHNCQUM3Qiw4REFBQ29CO29FQUFnQkYsV0FBVTs7c0ZBQ3pCLDhEQUFDRTs0RUFBSUYsV0FBVTtzRkFDYiw0RUFBQ3NCO2dGQUFHdEIsV0FBVTswRkFBZXRDLEtBQUtpQixjQUFjOzs7Ozs7Ozs7OztzRkFHbEQsOERBQUN1Qjs0RUFBSUYsV0FBVTs7OEZBQ2IsOERBQUNoSyx1REFBS0E7b0ZBQUNnSyxXQUFVOzhGQUFVOzs7Ozs7OEZBQzNCLDhEQUFDakssdURBQUtBO29GQUNKOEYsTUFBSztvRkFDTGtGLEtBQUk7b0ZBQ0ovQixPQUFPdEIsS0FBS0ksUUFBUTtvRkFDcEJ1QyxVQUFVLENBQUNXLElBQU1uQyxnQkFBZ0JDLE9BQU8sWUFBWW1DLFNBQVNELEVBQUVFLE1BQU0sQ0FBQ2xDLEtBQUssS0FBSztvRkFDaEZnQixXQUFVOzs7Ozs7Ozs7Ozs7c0ZBSWQsOERBQUNFOzRFQUFJRixXQUFVOzs4RkFDYiw4REFBQ2hLLHVEQUFLQTtvRkFBQ2dLLFdBQVU7OEZBQVU7Ozs7Ozs4RkFDM0IsOERBQUNqSyx1REFBS0E7b0ZBQ0o4RixNQUFLO29GQUNMa0YsS0FBSTtvRkFDSlUsTUFBSztvRkFDTHpDLE9BQU90QixLQUFLZSxVQUFVO29GQUN0QjRCLFVBQVUsQ0FBQ1csSUFBTW5DLGdCQUFnQkMsT0FBTyxjQUFjNEMsV0FBV1YsRUFBRUUsTUFBTSxDQUFDbEMsS0FBSyxLQUFLO29GQUNwRmdCLFdBQVU7Ozs7Ozs7Ozs7OztzRkFJZCw4REFBQ0U7NEVBQUlGLFdBQVU7O2dGQUFzQjtnRkFDL0J0QyxLQUFLYyxXQUFXLENBQUMrQyxPQUFPLENBQUM7Ozs7Ozs7c0ZBRy9CLDhEQUFDekwseURBQU1BOzRFQUNMK0YsTUFBSzs0RUFDTDBELFNBQVE7NEVBQ1I0QixNQUFLOzRFQUNMQyxTQUFTLElBQU1uQyxnQkFBZ0JIOzRFQUMvQmtCLFdBQVU7c0ZBRVYsNEVBQUN4SSxxSUFBTUE7Z0ZBQUN3SSxXQUFVOzs7Ozs7Ozs7Ozs7bUVBdkNabEI7Ozs7OzBFQTRDWiw4REFBQ29CO2dFQUFJRixXQUFVOztrRkFDYiw4REFBQzJCO3dFQUFLM0IsV0FBVTtrRkFBYzs7Ozs7O2tGQUM5Qiw4REFBQzJCO3dFQUFLM0IsV0FBVTs7NEVBQW9COzRFQUFJWixnQkFBZ0JtQyxPQUFPLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTOUUsOERBQUM1TCwrREFBWUE7NEJBQUNxSyxXQUFVOzs4Q0FDdEIsOERBQUNsSyx5REFBTUE7b0NBQUMrRixNQUFLO29DQUFTMEQsU0FBUTtvQ0FBVTZCLFNBQVMsSUFBTXRILGFBQWE7OENBQVE7Ozs7Ozs4Q0FHNUUsOERBQUNoRSx5REFBTUE7b0NBQUMrRixNQUFLO29DQUFTd0YsVUFBVXBHLGNBQWNiOzhDQUMzQ2EsYUFDSUwsWUFBWSxnQkFBZ0IsaUJBQzVCQSxZQUFZLHNCQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRckQ7R0E1cUJNaEI7O1FBVWNqQyx1REFBUUE7UUFRMkJHLHdFQUFpQkE7UUFLekR6QyxxREFBT0E7OztLQXZCaEJ1RTtBQThxQk4saUVBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2lyb3ZcXERvY3VtZW50c1xcbmV4dC1qc1xcbmVjdGFyXFxuZWN0YXItbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXEFwcG9pbnRtZW50Rm9ybS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VGb3JtLCBDb250cm9sbGVyIH0gZnJvbSAncmVhY3QtaG9vay1mb3JtJztcbmltcG9ydCB7IHpvZFJlc29sdmVyIH0gZnJvbSAnQGhvb2tmb3JtL3Jlc29sdmVycy96b2QnO1xuaW1wb3J0IHsgRGlhbG9nLCBEaWFsb2dDb250ZW50LCBEaWFsb2dEZXNjcmlwdGlvbiwgRGlhbG9nRm9vdGVyLCBEaWFsb2dIZWFkZXIsIERpYWxvZ1RpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RpYWxvZyc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IElucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2lucHV0JztcbmltcG9ydCB7IExhYmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2xhYmVsJztcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RleHRhcmVhJztcbmltcG9ydCB7IFNlbGVjdCwgU2VsZWN0Q29udGVudCwgU2VsZWN0SXRlbSwgU2VsZWN0VHJpZ2dlciwgU2VsZWN0VmFsdWUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2VsZWN0JztcbmltcG9ydCB7IFRhYnMsIFRhYnNDb250ZW50LCBUYWJzTGlzdCwgVGFic1RyaWdnZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGFicyc7XG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCc7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSc7XG5pbXBvcnQgeyBDaGVja2JveCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jaGVja2JveCc7XG5pbXBvcnQgeyBSYWRpb0dyb3VwLCBSYWRpb0dyb3VwSXRlbSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9yYWRpby1ncm91cCc7XG5pbXBvcnQgeyBEYXRlVGltZUlucHV0IH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2RhdGV0aW1lLWlucHV0JztcbmltcG9ydCB7IENhbGVuZGFyLCBDbG9jaywgVXNlciwgU3RldGhvc2NvcGUsIFBsdXMsIFRyYXNoMiwgU2VhcmNoLCBBbGVydENpcmNsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0JztcbmltcG9ydCB7IGZvcm1hdCwgYWRkTWludXRlcyB9IGZyb20gJ2RhdGUtZm5zJztcbmltcG9ydCB7IHB0QlIgfSBmcm9tICdkYXRlLWZucy9sb2NhbGUnO1xuaW1wb3J0IHsgZm9ybWF0RGF0ZVRpbWVGb3JJbnB1dCB9IGZyb20gJ0AvbGliL2RhdGUtdXRpbHMnO1xuaW1wb3J0IHsgYXBwb2ludG1lbnRTY2hlbWEsIHR5cGUgQXBwb2ludG1lbnRGb3JtRGF0YSB9IGZyb20gJ0AvbGliL3ZhbGlkYXRpb25zJztcbmltcG9ydCB7IHVzZUZvcm1TdWJtaXNzaW9uIH0gZnJvbSAnQC9ob29rcy91c2VBc3luY09wZXJhdGlvbic7XG5cbi8vIFV0aWxpdHkgZnVuY3Rpb25zIGZvciBCcmF6aWxpYW4gdGltZXpvbmUgKFVUQy0zKVxuY29uc3QgYnJhemlsaWFuVGltZXpvbmVPZmZzZXQgPSAtMyAqIDYwOyAvLyAtMyBob3VycyBpbiBtaW51dGVzXG5cbmNvbnN0IHRvQnJhemlsaWFuVGltZSA9IChkYXRlKSA9PiB7XG4gIGlmICghZGF0ZSkgcmV0dXJuIG51bGw7XG4gIFxuICAvLyBTZSBmb3Igc3RyaW5nLCBjb252ZXJ0ZSBwYXJhIERhdGVcbiAgY29uc3QgZGF0ZU9iaiA9IHR5cGVvZiBkYXRlID09PSAnc3RyaW5nJyA/IG5ldyBEYXRlKGRhdGUpIDogZGF0ZTtcbiAgXG4gIC8vIENyaWEgdW1hIG5vdmEgZGF0YSBhanVzdGFkYSBwYXJhIFVUQy0zXG4gIGNvbnN0IHV0Y1RpbWUgPSBkYXRlT2JqLmdldFRpbWUoKSArIChkYXRlT2JqLmdldFRpbWV6b25lT2Zmc2V0KCkgKiA2MDAwMCk7XG4gIGNvbnN0IGJyYXppbGlhblRpbWUgPSBuZXcgRGF0ZSh1dGNUaW1lICsgKGJyYXppbGlhblRpbWV6b25lT2Zmc2V0ICogNjAwMDApKTtcbiAgXG4gIHJldHVybiBicmF6aWxpYW5UaW1lO1xufTtcblxuY29uc3QgZm9ybWF0RGF0ZVRpbWVGb3JCcmF6aWxpYW5JbnB1dCA9IChkYXRlVGltZSkgPT4ge1xuICBpZiAoIWRhdGVUaW1lKSByZXR1cm4gJyc7XG4gIFxuICB0cnkge1xuICAgIGNvbnN0IGRhdGUgPSB0b0JyYXppbGlhblRpbWUoZGF0ZVRpbWUpO1xuICAgIGlmICghZGF0ZSB8fCBpc05hTihkYXRlLmdldFRpbWUoKSkpIHJldHVybiAnJztcbiAgICBcbiAgICAvLyBGb3JtYXRvIHBhcmEgZGF0ZXRpbWUtbG9jYWw6IFlZWVktTU0tRERUSEg6bW1cbiAgICBjb25zdCB5ZWFyID0gZGF0ZS5nZXRGdWxsWWVhcigpO1xuICAgIGNvbnN0IG1vbnRoID0gU3RyaW5nKGRhdGUuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJyk7XG4gICAgY29uc3QgZGF5ID0gU3RyaW5nKGRhdGUuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpO1xuICAgIGNvbnN0IGhvdXJzID0gU3RyaW5nKGRhdGUuZ2V0SG91cnMoKSkucGFkU3RhcnQoMiwgJzAnKTtcbiAgICBjb25zdCBtaW51dGVzID0gU3RyaW5nKGRhdGUuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpO1xuICAgIFxuICAgIHJldHVybiBgJHt5ZWFyfS0ke21vbnRofS0ke2RheX1UJHtob3Vyc306JHttaW51dGVzfWA7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignW0RBVEVUSU1FXSBFcnJvciBmb3JtYXR0aW5nIGRhdGU6JywgZXJyb3IpO1xuICAgIHJldHVybiAnJztcbiAgfVxufTtcblxuY29uc3QgcGFyc2VCcmF6aWxpYW5EYXRlVGltZSA9IChkYXRlVGltZVN0cmluZykgPT4ge1xuICBpZiAoIWRhdGVUaW1lU3RyaW5nKSByZXR1cm4gbnVsbDtcbiAgXG4gIHRyeSB7XG4gICAgLy8gU2UgasOhIHRlbSB0aW1lem9uZSwgdXNhIGRpcmV0YW1lbnRlXG4gICAgaWYgKGRhdGVUaW1lU3RyaW5nLmluY2x1ZGVzKCdUJykgJiYgKGRhdGVUaW1lU3RyaW5nLmluY2x1ZGVzKCdaJykgfHwgZGF0ZVRpbWVTdHJpbmcuaW5jbHVkZXMoJy0wMzowMCcpKSkge1xuICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVUaW1lU3RyaW5nKTtcbiAgICB9XG4gICAgXG4gICAgLy8gU2Ugw6kgZm9ybWF0byBkYXRldGltZS1sb2NhbCAoWVlZWS1NTS1ERFRISDptbSksIGFkaWNpb25hIHRpbWV6b25lIGJyYXNpbGVpcm9cbiAgICBpZiAoZGF0ZVRpbWVTdHJpbmcuaW5jbHVkZXMoJ1QnKSkge1xuICAgICAgcmV0dXJuIG5ldyBEYXRlKGRhdGVUaW1lU3RyaW5nICsgJzowMC0wMzowMCcpO1xuICAgIH1cbiAgICBcbiAgICAvLyBGYWxsYmFjayBwYXJhIG91dHJvcyBmb3JtYXRvc1xuICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShkYXRlVGltZVN0cmluZyk7XG4gICAgcmV0dXJuIHRvQnJhemlsaWFuVGltZShkYXRlKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdbREFURVRJTUVdIEVycm9yIHBhcnNpbmcgZGF0ZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn07XG5cbnR5cGUgUGF0aWVudCA9IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBlbWFpbD86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG59O1xuXG50eXBlIEhlYWx0aGNhcmVQcm9mZXNzaW9uYWwgPSB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgc3BlY2lhbHR5OiBzdHJpbmcgfCBudWxsO1xuICBpc19hY3RpdmU6IGJvb2xlYW47XG59O1xuXG50eXBlIFByb2NlZHVyZSA9IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nIHwgbnVsbDtcbiAgZGVmYXVsdF9wcmljZTogbnVtYmVyIHwgbnVsbDtcbiAgZHVyYXRpb25fbWludXRlczogbnVtYmVyIHwgbnVsbDtcbn07XG5cbnR5cGUgQXBwb2ludG1lbnRQcm9jZWR1cmUgPSB7XG4gIHByb2NlZHVyZV9pZDogc3RyaW5nO1xuICBwcm9jZWR1cmVfbmFtZTogc3RyaW5nO1xuICBxdWFudGl0eTogbnVtYmVyO1xuICB1bml0X3ByaWNlOiBudW1iZXI7XG4gIHRvdGFsX3ByaWNlOiBudW1iZXI7XG59O1xuXG5pbnRlcmZhY2UgQXBwb2ludG1lbnRGb3JtUHJvcHMge1xuICBvcGVuOiBib29sZWFuO1xuICBvbk9wZW5DaGFuZ2U6IChvcGVuOiBib29sZWFuKSA9PiB2b2lkO1xuICBwYXRpZW50czogUGF0aWVudFtdO1xuICBoZWFsdGhjYXJlUHJvZmVzc2lvbmFsczogSGVhbHRoY2FyZVByb2Zlc3Npb25hbFtdO1xuICBwcm9jZWR1cmVzOiBQcm9jZWR1cmVbXTtcbiAgaW5pdGlhbERhdGE/OiBQYXJ0aWFsPEFwcG9pbnRtZW50Rm9ybURhdGEgJiB7IGlkPzogc3RyaW5nIH0+O1xuICBvblN1Ym1pdDogKGRhdGE6IEFwcG9pbnRtZW50Rm9ybURhdGEgJiB7IHByb2NlZHVyZXM6IEFwcG9pbnRtZW50UHJvY2VkdXJlW107IGlkPzogc3RyaW5nIH0pID0+IFByb21pc2U8dm9pZD47XG4gIGxvYWRpbmc/OiBib29sZWFuO1xufVxuXG5jb25zdCBBcHBvaW50bWVudEZvcm06IFJlYWN0LkZDPEFwcG9pbnRtZW50Rm9ybVByb3BzPiA9ICh7XG4gIG9wZW4sXG4gIG9uT3BlbkNoYW5nZSxcbiAgcGF0aWVudHMsXG4gIGhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzLFxuICBwcm9jZWR1cmVzLFxuICBpbml0aWFsRGF0YSxcbiAgb25TdWJtaXQsXG4gIGxvYWRpbmcgPSBmYWxzZVxufSkgPT4ge1xuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGUoJ3NjaGVkdWxpbmcnKTtcbiAgY29uc3QgW3NlYXJjaFByb2NlZHVyZSwgc2V0U2VhcmNoUHJvY2VkdXJlXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkUHJvY2VkdXJlcywgc2V0U2VsZWN0ZWRQcm9jZWR1cmVzXSA9IHVzZVN0YXRlPEFwcG9pbnRtZW50UHJvY2VkdXJlW10+KFtdKTtcblxuICAvLyBDaGVjayBpZiB3ZSdyZSBpbiBlZGl0aW5nIG1vZGVcbiAgY29uc3QgaXNFZGl0aW5nID0gQm9vbGVhbihpbml0aWFsRGF0YT8uaWQpO1xuXG4gIGNvbnN0IHsgZXhlY3V0ZTogc3VibWl0Rm9ybSwgbG9hZGluZzogc3VibWl0dGluZyB9ID0gdXNlRm9ybVN1Ym1pc3Npb24oe1xuICAgIHN1Y2Nlc3NNZXNzYWdlOiBpc0VkaXRpbmcgPyAnQ29uc3VsdGEgYXR1YWxpemFkYSBjb20gc3VjZXNzbyEnIDogJ0NvbnN1bHRhIGFnZW5kYWRhIGNvbSBzdWNlc3NvIScsXG4gICAgZXJyb3JNZXNzYWdlOiBpc0VkaXRpbmcgPyAnRXJybyBhbyBhdHVhbGl6YXIgY29uc3VsdGEnIDogJ0Vycm8gYW8gYWdlbmRhciBjb25zdWx0YSdcbiAgfSk7XG5cbiAgY29uc3QgZm9ybSA9IHVzZUZvcm08QXBwb2ludG1lbnRGb3JtRGF0YT4oe1xuICAgIHJlc29sdmVyOiB6b2RSZXNvbHZlcihhcHBvaW50bWVudFNjaGVtYSksXG4gICAgZGVmYXVsdFZhbHVlczoge1xuICAgICAgdGl0bGU6ICcnLFxuICAgICAgZGVzY3JpcHRpb246ICcnLFxuICAgICAgcGF0aWVudF9pZDogJycsXG4gICAgICBoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9pZDogJycsXG4gICAgICBzdGFydF90aW1lOiAnJyxcbiAgICAgIGVuZF90aW1lOiAnJyxcbiAgICAgIHR5cGU6ICdjb25zdWx0YXRpb24nLFxuICAgICAgc3RhdHVzOiAnc2NoZWR1bGVkJyxcbiAgICAgIG5vdGVzOiAnJyxcbiAgICAgIGhhc19yZWN1cnJlbmNlOiBmYWxzZSxcbiAgICAgIHJlY3VycmVuY2VfdHlwZTogJ3dlZWtseScsXG4gICAgICByZWN1cnJlbmNlX2ludGVydmFsOiAxLFxuICAgICAgcmVjdXJyZW5jZV9kYXlzOiBbXSxcbiAgICAgIHJlY3VycmVuY2VfZW5kX3R5cGU6ICduZXZlcicsXG4gICAgICByZWN1cnJlbmNlX2VuZF9kYXRlOiAnJyxcbiAgICAgIHJlY3VycmVuY2VfY291bnQ6IDEsXG4gICAgfVxuICB9KTtcblxuICBjb25zdCB7IGNvbnRyb2wsIGhhbmRsZVN1Ym1pdCwgd2F0Y2gsIHNldFZhbHVlLCByZXNldCwgZm9ybVN0YXRlOiB7IGVycm9ycyB9IH0gPSBmb3JtO1xuXG4gIGNvbnN0IHdhdGNoZWRWYWx1ZXMgPSB3YXRjaCgpO1xuXG4gIC8vIEluaXRpYWxpemUgZm9ybSB3aXRoIGluaXRpYWwgZGF0YVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpbml0aWFsRGF0YSAmJiBvcGVuKSB7XG4gICAgICBjb25zb2xlLmxvZygnW0RBVEVUSU1FXSBJbml0aWFsaXppbmcgZm9ybSB3aXRoIGRhdGE6JywgaW5pdGlhbERhdGEpO1xuICAgICAgXG4gICAgICBjb25zdCBmb3JtYXR0ZWREYXRhID0ge1xuICAgICAgICAuLi5pbml0aWFsRGF0YSxcbiAgICAgICAgc3RhcnRfdGltZTogZm9ybWF0RGF0ZVRpbWVGb3JCcmF6aWxpYW5JbnB1dChpbml0aWFsRGF0YS5zdGFydF90aW1lKSxcbiAgICAgICAgZW5kX3RpbWU6IGZvcm1hdERhdGVUaW1lRm9yQnJhemlsaWFuSW5wdXQoaW5pdGlhbERhdGEuZW5kX3RpbWUpLFxuICAgICAgICBwYXRpZW50X2lkOiBpbml0aWFsRGF0YS5wYXRpZW50X2lkIHx8ICcnLFxuICAgICAgICBoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9pZDogaW5pdGlhbERhdGEuaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfaWQgfHwgJycsXG4gICAgICB9O1xuICAgICAgXG4gICAgICBjb25zb2xlLmxvZygnW0RBVEVUSU1FXSBGb3JtYXR0ZWQgZGF0YSBmb3IgZm9ybTonLCBmb3JtYXR0ZWREYXRhKTtcbiAgICAgIHJlc2V0KGZvcm1hdHRlZERhdGEpO1xuICAgIH1cbiAgfSwgW2luaXRpYWxEYXRhLCBvcGVuLCByZXNldF0pO1xuXG4gIC8vIEF1dG8tZ2VuZXJhdGUgdGl0bGUgd2hlbiBwYXRpZW50IGFuZCB0eXBlIGNoYW5nZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh3YXRjaGVkVmFsdWVzLnBhdGllbnRfaWQgJiYgd2F0Y2hlZFZhbHVlcy50eXBlKSB7XG4gICAgICBjb25zdCBwYXRpZW50ID0gcGF0aWVudHMuZmluZChwID0+IHAuaWQgPT09IHdhdGNoZWRWYWx1ZXMucGF0aWVudF9pZCk7XG4gICAgICBpZiAocGF0aWVudCkge1xuICAgICAgICBjb25zdCB0eXBlTGFiZWwgPSB3YXRjaGVkVmFsdWVzLnR5cGUgPT09ICdjb25zdWx0YXRpb24nID8gJ0NvbnN1bHRhJyA6ICdSZXRvcm5vJztcbiAgICAgICAgc2V0VmFsdWUoJ3RpdGxlJywgYCR7dHlwZUxhYmVsfSAtICR7cGF0aWVudC5uYW1lfWApO1xuICAgICAgfVxuICAgIH1cbiAgfSwgW3dhdGNoZWRWYWx1ZXMucGF0aWVudF9pZCwgd2F0Y2hlZFZhbHVlcy50eXBlLCBwYXRpZW50cywgc2V0VmFsdWVdKTtcblxuICAvLyBBdXRvLWNhbGN1bGF0ZSBlbmQgdGltZSBiYXNlZCBvbiBzdGFydCB0aW1lIChCcmF6aWxpYW4gdGltZXpvbmUpXG4gIC8vIE9ubHkgYXV0by1jYWxjdWxhdGUgaWYgZW5kX3RpbWUgaXMgbm90IGFscmVhZHkgc2V0IChlLmcuLCBmcm9tIEZ1bGxDYWxlbmRhciBzZWxlY3Rpb24pXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ1tEQVRFVElNRV0gQXV0by1jYWxjdWxhdGluZyBlbmQgdGltZSwgc3RhcnRfdGltZTonLCB3YXRjaGVkVmFsdWVzLnN0YXJ0X3RpbWUsICdlbmRfdGltZTonLCB3YXRjaGVkVmFsdWVzLmVuZF90aW1lKTtcblxuICAgIGlmICh3YXRjaGVkVmFsdWVzLnN0YXJ0X3RpbWUgJiYgIXdhdGNoZWRWYWx1ZXMuZW5kX3RpbWUpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IHN0YXJ0VGltZSA9IHBhcnNlQnJhemlsaWFuRGF0ZVRpbWUod2F0Y2hlZFZhbHVlcy5zdGFydF90aW1lKTtcblxuICAgICAgICBjb25zb2xlLmxvZygnW0RBVEVUSU1FXSBQYXJzZWQgc3RhcnQgdGltZTonLCBzdGFydFRpbWUsICdWYWxpZDonLCBzdGFydFRpbWUgJiYgIWlzTmFOKHN0YXJ0VGltZS5nZXRUaW1lKCkpKTtcblxuICAgICAgICBpZiAoc3RhcnRUaW1lICYmICFpc05hTihzdGFydFRpbWUuZ2V0VGltZSgpKSkge1xuICAgICAgICAgIGNvbnN0IHRvdGFsRHVyYXRpb24gPSBzZWxlY3RlZFByb2NlZHVyZXMucmVkdWNlKCh0b3RhbCwgcHJvYykgPT4ge1xuICAgICAgICAgICAgY29uc3QgcHJvY2VkdXJlID0gcHJvY2VkdXJlcy5maW5kKHAgPT4gcC5pZCA9PT0gcHJvYy5wcm9jZWR1cmVfaWQpO1xuICAgICAgICAgICAgcmV0dXJuIHRvdGFsICsgKHByb2NlZHVyZT8uZHVyYXRpb25fbWludXRlcyB8fCAzMCkgKiBwcm9jLnF1YW50aXR5O1xuICAgICAgICAgIH0sIDMwKTsgLy8gRGVmYXVsdCAzMCBtaW51dGVzIGlmIG5vIHByb2NlZHVyZXNcblxuICAgICAgICAgIGNvbnNvbGUubG9nKCdbREFURVRJTUVdIFRvdGFsIGR1cmF0aW9uOicsIHRvdGFsRHVyYXRpb24sICdtaW51dGVzJyk7XG5cbiAgICAgICAgICBjb25zdCBlbmRUaW1lID0gYWRkTWludXRlcyhzdGFydFRpbWUsIHRvdGFsRHVyYXRpb24pO1xuICAgICAgICAgIGNvbnN0IGZvcm1hdHRlZEVuZFRpbWUgPSBmb3JtYXREYXRlVGltZUZvckJyYXppbGlhbklucHV0KGVuZFRpbWUpO1xuXG4gICAgICAgICAgY29uc29sZS5sb2coJ1tEQVRFVElNRV0gQ2FsY3VsYXRlZCBlbmQgdGltZTonLCBlbmRUaW1lLCAnRm9ybWF0dGVkOicsIGZvcm1hdHRlZEVuZFRpbWUpO1xuICAgICAgICAgIHNldFZhbHVlKCdlbmRfdGltZScsIGZvcm1hdHRlZEVuZFRpbWUpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdbREFURVRJTUVdIEVycm9yIGNhbGN1bGF0aW5nIGVuZCB0aW1lOicsIGVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFt3YXRjaGVkVmFsdWVzLnN0YXJ0X3RpbWUsIHdhdGNoZWRWYWx1ZXMuZW5kX3RpbWUsIHNlbGVjdGVkUHJvY2VkdXJlcywgcHJvY2VkdXJlcywgc2V0VmFsdWVdKTtcblxuICBjb25zdCBmaWx0ZXJlZFByb2NlZHVyZXMgPSBwcm9jZWR1cmVzLmZpbHRlcihwcm9jZWR1cmUgPT5cbiAgICBwcm9jZWR1cmUubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFByb2NlZHVyZS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgIChwcm9jZWR1cmUuZGVzY3JpcHRpb24gJiYgcHJvY2VkdXJlLmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoUHJvY2VkdXJlLnRvTG93ZXJDYXNlKCkpKVxuICApO1xuXG4gIGNvbnN0IGFkZFByb2NlZHVyZSA9IChwcm9jZWR1cmU6IFByb2NlZHVyZSkgPT4ge1xuICAgIGNvbnN0IGV4aXN0aW5nSW5kZXggPSBzZWxlY3RlZFByb2NlZHVyZXMuZmluZEluZGV4KHAgPT4gcC5wcm9jZWR1cmVfaWQgPT09IHByb2NlZHVyZS5pZCk7XG4gICAgXG4gICAgaWYgKGV4aXN0aW5nSW5kZXggPj0gMCkge1xuICAgICAgLy8gSW5jcmVhc2UgcXVhbnRpdHkgaWYgYWxyZWFkeSBleGlzdHNcbiAgICAgIGNvbnN0IHVwZGF0ZWQgPSBbLi4uc2VsZWN0ZWRQcm9jZWR1cmVzXTtcbiAgICAgIHVwZGF0ZWRbZXhpc3RpbmdJbmRleF0ucXVhbnRpdHkgKz0gMTtcbiAgICAgIHVwZGF0ZWRbZXhpc3RpbmdJbmRleF0udG90YWxfcHJpY2UgPSB1cGRhdGVkW2V4aXN0aW5nSW5kZXhdLnF1YW50aXR5ICogdXBkYXRlZFtleGlzdGluZ0luZGV4XS51bml0X3ByaWNlO1xuICAgICAgc2V0U2VsZWN0ZWRQcm9jZWR1cmVzKHVwZGF0ZWQpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBBZGQgbmV3IHByb2NlZHVyZVxuICAgICAgY29uc3QgbmV3UHJvY2VkdXJlOiBBcHBvaW50bWVudFByb2NlZHVyZSA9IHtcbiAgICAgICAgcHJvY2VkdXJlX2lkOiBwcm9jZWR1cmUuaWQsXG4gICAgICAgIHByb2NlZHVyZV9uYW1lOiBwcm9jZWR1cmUubmFtZSxcbiAgICAgICAgcXVhbnRpdHk6IDEsXG4gICAgICAgIHVuaXRfcHJpY2U6IHByb2NlZHVyZS5kZWZhdWx0X3ByaWNlIHx8IDAsXG4gICAgICAgIHRvdGFsX3ByaWNlOiBwcm9jZWR1cmUuZGVmYXVsdF9wcmljZSB8fCAwLFxuICAgICAgfTtcbiAgICAgIHNldFNlbGVjdGVkUHJvY2VkdXJlcyhbLi4uc2VsZWN0ZWRQcm9jZWR1cmVzLCBuZXdQcm9jZWR1cmVdKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdXBkYXRlUHJvY2VkdXJlID0gKGluZGV4OiBudW1iZXIsIGZpZWxkOiAncXVhbnRpdHknIHwgJ3VuaXRfcHJpY2UnLCB2YWx1ZTogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlZCA9IFsuLi5zZWxlY3RlZFByb2NlZHVyZXNdO1xuICAgIHVwZGF0ZWRbaW5kZXhdW2ZpZWxkXSA9IHZhbHVlO1xuICAgIHVwZGF0ZWRbaW5kZXhdLnRvdGFsX3ByaWNlID0gdXBkYXRlZFtpbmRleF0ucXVhbnRpdHkgKiB1cGRhdGVkW2luZGV4XS51bml0X3ByaWNlO1xuICAgIHNldFNlbGVjdGVkUHJvY2VkdXJlcyh1cGRhdGVkKTtcbiAgfTtcblxuICBjb25zdCByZW1vdmVQcm9jZWR1cmUgPSAoaW5kZXg6IG51bWJlcikgPT4ge1xuICAgIHNldFNlbGVjdGVkUHJvY2VkdXJlcyhzZWxlY3RlZFByb2NlZHVyZXMuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCkpO1xuICB9O1xuXG4gIGNvbnN0IGdldFRvdGFsUHJpY2UgPSAoKSA9PiB7XG4gICAgcmV0dXJuIHNlbGVjdGVkUHJvY2VkdXJlcy5yZWR1Y2UoKHRvdGFsLCBwcm9jKSA9PiB0b3RhbCArIHByb2MudG90YWxfcHJpY2UsIDApO1xuICB9O1xuXG4gIGNvbnN0IG9uRm9ybVN1Ym1pdCA9IGFzeW5jIChkYXRhOiBBcHBvaW50bWVudEZvcm1EYXRhKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ1tEQVRFVElNRV0gRm9ybSBzdWJtaXR0ZWQgd2l0aCBkYXRhOicsIGRhdGEpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyBQYXJzZSBhbmQgdmFsaWRhdGUgZGF0ZXMgaW4gQnJhemlsaWFuIHRpbWV6b25lXG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBwYXJzZUJyYXppbGlhbkRhdGVUaW1lKGRhdGEuc3RhcnRfdGltZSk7XG4gICAgICBjb25zdCBlbmRUaW1lID0gcGFyc2VCcmF6aWxpYW5EYXRlVGltZShkYXRhLmVuZF90aW1lKTtcbiAgICAgIFxuICAgICAgaWYgKCFzdGFydFRpbWUgfHwgIWVuZFRpbWUpIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiAnRXJybycsXG4gICAgICAgICAgZGVzY3JpcHRpb246ICdEYXRhcyBpbnbDoWxpZGFzLiBQb3IgZmF2b3IsIHZlcmlmaXF1ZSBvcyBob3LDoXJpb3MuJyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIENvbnZlcnQgdG8gSVNPIHN0cmluZyB3aXRoIEJyYXppbGlhbiB0aW1lem9uZVxuICAgICAgY29uc3QgZm9ybWF0dGVkRGF0YSA9IHtcbiAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgc3RhcnRfdGltZTogc3RhcnRUaW1lLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIGVuZF90aW1lOiBlbmRUaW1lLnRvSVNPU3RyaW5nKCksXG4gICAgICB9O1xuICAgICAgXG4gICAgICBjb25zb2xlLmxvZygnW0RBVEVUSU1FXSBGb3JtYXR0ZWQgZGF0YSBmb3Igc3VibWlzc2lvbjonLCBmb3JtYXR0ZWREYXRhKTtcblxuICAgICAgYXdhaXQgc3VibWl0Rm9ybShhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnN0IGFwcG9pbnRtZW50RGF0YSA9IHtcbiAgICAgICAgICAuLi5mb3JtYXR0ZWREYXRhLFxuICAgICAgICAgIHByb2NlZHVyZXM6IHNlbGVjdGVkUHJvY2VkdXJlcyxcbiAgICAgICAgICB0b3RhbF9wcmljZTogZ2V0VG90YWxQcmljZSgpLFxuICAgICAgICAgIHN0YXR1czogJ3NjaGVkdWxlZCcsXG4gICAgICAgICAgLi4uKGlzRWRpdGluZyAmJiBpbml0aWFsRGF0YT8uaWQgJiYgeyBpZDogaW5pdGlhbERhdGEuaWQgfSksXG4gICAgICAgIH07XG5cbiAgICAgICAgY29uc29sZS5sb2coJ1tEQVRFVElNRV0gRmluYWwgYXBwb2ludG1lbnQgZGF0YTonLCBhcHBvaW50bWVudERhdGEpO1xuICAgICAgICBhd2FpdCBvblN1Ym1pdChhcHBvaW50bWVudERhdGEpO1xuXG4gICAgICAgIC8vIFJlc2V0IGZvcm1cbiAgICAgICAgcmVzZXQoKTtcbiAgICAgICAgc2V0U2VsZWN0ZWRQcm9jZWR1cmVzKFtdKTtcbiAgICAgICAgc2V0QWN0aXZlVGFiKCdzY2hlZHVsaW5nJyk7XG4gICAgICAgIG9uT3BlbkNoYW5nZShmYWxzZSk7XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW0RBVEVUSU1FXSBFcnJvciBzdWJtaXR0aW5nIGZvcm06JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogJ0Vycm8nLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0Vycm8gYW8gcHJvY2Vzc2FyIG9zIGRhZG9zLiBQb3IgZmF2b3IsIHRlbnRlIG5vdmFtZW50ZS4nLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlUmVjdXJyZW5jZURheSA9IChkYXk6IG51bWJlcikgPT4ge1xuICAgIGNvbnN0IGN1cnJlbnREYXlzID0gd2F0Y2hlZFZhbHVlcy5yZWN1cnJlbmNlX2RheXMgfHwgW107XG4gICAgY29uc3QgbmV3RGF5cyA9IGN1cnJlbnREYXlzLmluY2x1ZGVzKGRheSlcbiAgICAgID8gY3VycmVudERheXMuZmlsdGVyKGQgPT4gZCAhPT0gZGF5KVxuICAgICAgOiBbLi4uY3VycmVudERheXMsIGRheV07XG4gICAgc2V0VmFsdWUoJ3JlY3VycmVuY2VfZGF5cycsIG5ld0RheXMpO1xuICB9O1xuXG4gIGNvbnN0IHdlZWtEYXlzID0gW1xuICAgIHsgdmFsdWU6IDEsIGxhYmVsOiAnRCcgfSwgLy8gRG9taW5nb1xuICAgIHsgdmFsdWU6IDIsIGxhYmVsOiAnUycgfSwgLy8gU2VndW5kYVxuICAgIHsgdmFsdWU6IDMsIGxhYmVsOiAnVCcgfSwgLy8gVGVyw6dhXG4gICAgeyB2YWx1ZTogNCwgbGFiZWw6ICdRJyB9LCAvLyBRdWFydGFcbiAgICB7IHZhbHVlOiA1LCBsYWJlbDogJ1EnIH0sIC8vIFF1aW50YVxuICAgIHsgdmFsdWU6IDYsIGxhYmVsOiAnUycgfSwgLy8gU2V4dGFcbiAgICB7IHZhbHVlOiA3LCBsYWJlbDogJ1MnIH0sIC8vIFPDoWJhZG9cbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxEaWFsb2cgb3Blbj17b3Blbn0gb25PcGVuQ2hhbmdlPXtvbk9wZW5DaGFuZ2V9PlxuICAgICAgPERpYWxvZ0NvbnRlbnQgY2xhc3NOYW1lPVwic206bWF4LXctWzgwMHB4XSBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgPERpYWxvZ1RpdGxlPntpc0VkaXRpbmcgPyAnRWRpdGFyIENvbnN1bHRhJyA6ICdBZ2VuZGFyIE5vdmEgQ29uc3VsdGEnfTwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgPERpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICAgICAge2lzRWRpdGluZyA/ICdFZGl0ZSBvcyBkYWRvcyBkYSBjb25zdWx0YScgOiAnUHJlZW5jaGEgb3MgZGFkb3MgcGFyYSBhZ2VuZGFyIHVtYSBub3ZhIGNvbnN1bHRhJ31cbiAgICAgICAgICA8L0RpYWxvZ0Rlc2NyaXB0aW9uPlxuICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cblxuICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0KG9uRm9ybVN1Ym1pdCl9PlxuICAgICAgICAgIDxUYWJzIHZhbHVlPXthY3RpdmVUYWJ9IG9uVmFsdWVDaGFuZ2U9e3NldEFjdGl2ZVRhYn0gY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiZ3JpZCB3LWZ1bGwgZ3JpZC1jb2xzLTJcIj5cbiAgICAgICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwic2NoZWR1bGluZ1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgIEFnZW5kYW1lbnRvXG4gICAgICAgICAgICAgIDwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cInByb2NlZHVyZXNcIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxTdGV0aG9zY29wZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICBQcm9jZWRpbWVudG9zXG4gICAgICAgICAgICAgIDwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICA8L1RhYnNMaXN0PlxuXG4gICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJzY2hlZHVsaW5nXCIgY2xhc3NOYW1lPVwic3BhY2UteS00IG10LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicGF0aWVudFwiPlBhY2llbnRlICo8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cInBhdGllbnRfaWRcIlxuICAgICAgICAgICAgICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICAgICAgICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXtmaWVsZC52YWx1ZX0gb25WYWx1ZUNoYW5nZT17ZmllbGQub25DaGFuZ2V9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIlNlbGVjaW9uZSBvIHBhY2llbnRlXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7cGF0aWVudHMubWFwKHBhdGllbnQgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17cGF0aWVudC5pZH0gdmFsdWU9e3BhdGllbnQuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlciBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3BhdGllbnQubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5wYXRpZW50X2lkICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWRlc3RydWN0aXZlIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIHtlcnJvcnMucGF0aWVudF9pZC5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicHJvZmVzc2lvbmFsXCI+UHJvZmlzc2lvbmFsPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9pZFwiXG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgICAgIHJlbmRlcj17KHsgZmllbGQgfSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e2ZpZWxkLnZhbHVlfSBvblZhbHVlQ2hhbmdlPXtmaWVsZC5vbkNoYW5nZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIHBsYWNlaG9sZGVyPVwiU2VsZWNpb25lIG8gcHJvZmlzc2lvbmFsXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aGVhbHRoY2FyZVByb2Zlc3Npb25hbHNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZmlsdGVyKHByb2YgPT4gcHJvZi5pc19hY3RpdmUpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLm1hcChwcm9mZXNzaW9uYWwgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXtwcm9mZXNzaW9uYWwuaWR9IHZhbHVlPXtwcm9mZXNzaW9uYWwuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZmVzc2lvbmFsLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9mZXNzaW9uYWwuc3BlY2lhbHR5ICYmIGAgLSAke3Byb2Zlc3Npb25hbC5zcGVjaWFsdHl9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLmhlYWx0aGNhcmVfcHJvZmVzc2lvbmFsX2lkICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWRlc3RydWN0aXZlIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIHtlcnJvcnMuaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfaWQubWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidHlwZVwiPlRpcG8gZGUgQ29uc3VsdGEgKjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8Q29udHJvbGxlclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwidHlwZVwiXG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgICAgIHJlbmRlcj17KHsgZmllbGQgfSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e2ZpZWxkLnZhbHVlfSBvblZhbHVlQ2hhbmdlPXtmaWVsZC5vbkNoYW5nZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJjb25zdWx0YXRpb25cIj5Db25zdWx0YTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJmb2xsb3dfdXBcIj5SZXRvcm5vPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIHtlcnJvcnMudHlwZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1kZXN0cnVjdGl2ZSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3JzLnR5cGUubWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInRpdGxlXCI+VMOtdHVsbyAqPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICAgICAgICAgIG5hbWU9XCJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgICAgIHJlbmRlcj17KHsgZmllbGQgfSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJ0aXRsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB7Li4uZmllbGR9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLnRpdGxlICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWRlc3RydWN0aXZlIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIHtlcnJvcnMudGl0bGUubWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwic3RhcnRfdGltZVwiPkRhdGEvSG9yYSBJbsOtY2lvICo8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgICAgICAgICAgbmFtZT1cInN0YXJ0X3RpbWVcIlxuICAgICAgICAgICAgICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICAgICAgICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8RGF0ZVRpbWVJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJzdGFydF90aW1lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtmaWVsZC52YWx1ZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtmaWVsZC5vbkNoYW5nZX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiREQvTU0vQUFBQSBISDptbVwiXG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLnN0YXJ0X3RpbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZGVzdHJ1Y3RpdmUgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAge2Vycm9ycy5zdGFydF90aW1lLm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJlbmRfdGltZVwiPkRhdGEvSG9yYSBGaW0gKjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8Q29udHJvbGxlclxuICAgICAgICAgICAgICAgICAgICBuYW1lPVwiZW5kX3RpbWVcIlxuICAgICAgICAgICAgICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICAgICAgICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8RGF0ZVRpbWVJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJlbmRfdGltZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZmllbGQudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17ZmllbGQub25DaGFuZ2V9XG4gICAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkREL01NL0FBQUEgSEg6bW1cIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5lbmRfdGltZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1kZXN0cnVjdGl2ZSBmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICB7ZXJyb3JzLmVuZF90aW1lLm1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJkZXNjcmlwdGlvblwiPkRlc2NyacOnw6NvPC9MYWJlbD5cbiAgICAgICAgICAgICAgICA8Q29udHJvbGxlclxuICAgICAgICAgICAgICAgICAgbmFtZT1cImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgaWQ9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJEZXNjcmnDp8OjbyBkYSBjb25zdWx0YS4uLlwiXG4gICAgICAgICAgICAgICAgICAgICAgey4uLmZpZWxkfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnMuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWRlc3RydWN0aXZlIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAge2Vycm9ycy5kZXNjcmlwdGlvbi5tZXNzYWdlfVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBSZWN1cnJlbmNlIFNlY3Rpb24gKi99XG4gICAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwiaGFzX3JlY3VycmVuY2VcIlxuICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZCB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tib3hcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9XCJoYXNfcmVjdXJyZW5jZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2ZpZWxkLnZhbHVlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9e2ZpZWxkLm9uQ2hhbmdlfVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImhhc19yZWN1cnJlbmNlXCIgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgIFJlY29ycsOqbmNpYSBQZXJzb25hbGl6YWRhXG4gICAgICAgICAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG5cbiAgICAgICAgICAgICAgICB7d2F0Y2hlZFZhbHVlcy5oYXNfcmVjdXJyZW5jZSAmJiAoXG4gICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+UmVwZXRpciBhIGNhZGE6PC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicmVjdXJyZW5jZV9pbnRlcnZhbFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbD17Y29udHJvbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPVwiMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsuLi5maWVsZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBmaWVsZC5vbkNoYW5nZShwYXJzZUludChlLnRhcmdldC52YWx1ZSkgfHwgMSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q29udHJvbGxlclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJyZWN1cnJlbmNlX3R5cGVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZCB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXtmaWVsZC52YWx1ZX0gb25WYWx1ZUNoYW5nZT17ZmllbGQub25DaGFuZ2V9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImRhaWx5XCI+ZGlhKHMpPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwid2Vla2x5XCI+c2VtYW5hKHMpPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIHZhbHVlPVwibW9udGhseVwiPm3DqnMoZXMpPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICB7d2F0Y2hlZFZhbHVlcy5yZWN1cnJlbmNlX3R5cGUgPT09ICd3ZWVrbHknICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbD5SZXBldGlyOjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt3ZWVrRGF5cy5tYXAoZGF5ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtkYXkudmFsdWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXsod2F0Y2hlZFZhbHVlcy5yZWN1cnJlbmNlX2RheXMgfHwgW10pLmluY2x1ZGVzKGRheS52YWx1ZSkgPyAnZGVmYXVsdCcgOiAnb3V0bGluZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctOCBoLTggcC0wXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlUmVjdXJyZW5jZURheShkYXkudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGF5LmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8TGFiZWw+VGVybWluYSBlbTo8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICAgICAgICAgICAgICBuYW1lPVwicmVjdXJyZW5jZV9lbmRfdHlwZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZCB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxSYWRpb0dyb3VwIHZhbHVlPXtmaWVsZC52YWx1ZX0gb25WYWx1ZUNoYW5nZT17ZmllbGQub25DaGFuZ2V9PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8UmFkaW9Hcm91cEl0ZW0gdmFsdWU9XCJuZXZlclwiIGlkPVwibmV2ZXJcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cIm5ldmVyXCI+TnVuY2E8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFJhZGlvR3JvdXBJdGVtIHZhbHVlPVwiZGF0ZVwiIGlkPVwiZW5kX2RhdGVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJlbmRfZGF0ZVwiPkVtPC9MYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDb250cm9sbGVyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9XCJyZWN1cnJlbmNlX2VuZF9kYXRlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udHJvbD17Y29udHJvbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZCB9KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Li4uZmllbGR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17d2F0Y2hlZFZhbHVlcy5yZWN1cnJlbmNlX2VuZF90eXBlICE9PSAnZGF0ZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTQwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UmFkaW9Hcm91cEl0ZW0gdmFsdWU9XCJjb3VudFwiIGlkPVwiZW5kX2NvdW50XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZW5kX2NvdW50XCI+QXDDs3M8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENvbnRyb2xsZXJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmFtZT1cInJlY3VycmVuY2VfY291bnRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sPXtjb250cm9sfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI9eyh7IGZpZWxkIH0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWluPVwiMVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Li4uZmllbGR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGZpZWxkLm9uQ2hhbmdlKHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKSB8fCAxKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXt3YXRjaGVkVmFsdWVzLnJlY3VycmVuY2VfZW5kX3R5cGUgIT09ICdjb3VudCd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTIwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbD5vY29ycsOqbmNpYXM8L0xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L1JhZGlvR3JvdXA+XG4gICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJwcm9jZWR1cmVzXCIgY2xhc3NOYW1lPVwic3BhY2UteS00IG10LTRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkJ1c2NhciBwcm9jZWRpbWVudG9zLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFByb2NlZHVyZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hQcm9jZWR1cmUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTFcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNCBtYXgtaC02MCBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgIHtmaWx0ZXJlZFByb2NlZHVyZXMubWFwKHByb2NlZHVyZSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxDYXJkIGtleT17cHJvY2VkdXJlLmlkfSBjbGFzc05hbWU9XCJjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1hY2NlbnQvNTBcIiBvbkNsaWNrPXsoKSA9PiBhZGRQcm9jZWR1cmUocHJvY2VkdXJlKX0+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1zdGFydFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LXNtXCI+e3Byb2NlZHVyZS5uYW1lfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2NlZHVyZS5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtdC0xXCI+e3Byb2NlZHVyZS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG10LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9jZWR1cmUuZGVmYXVsdF9wcmljZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwic2Vjb25kYXJ5XCIgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFIkIHtwcm9jZWR1cmUuZGVmYXVsdF9wcmljZS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9jZWR1cmUuZHVyYXRpb25fbWludXRlcyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCYWRnZSB2YXJpYW50PVwib3V0bGluZVwiIGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvY2VkdXJlLmR1cmF0aW9uX21pbnV0ZXN9bWluXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwic21cIiB2YXJpYW50PVwiZ2hvc3RcIiBjbGFzc05hbWU9XCJoLTggdy04IHAtMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIHsvKiBTZWxlY3RlZCBQcm9jZWR1cmVzICovfVxuICAgICAgICAgICAgICAgIHtzZWxlY3RlZFByb2NlZHVyZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+UHJvY2VkaW1lbnRvcyBTZWxlY2lvbmFkb3M8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkUHJvY2VkdXJlcy5tYXAoKHByb2MsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHAtMyBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9jLnByb2NlZHVyZV9uYW1lfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+UXRkOjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2MucXVhbnRpdHl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZVByb2NlZHVyZShpbmRleCwgJ3F1YW50aXR5JywgcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIHx8IDEpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0xNiBoLThcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXhzXCI+UHJlw6dvOjwvTGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1pbj1cIjBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMDFcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3Byb2MudW5pdF9wcmljZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlUHJvY2VkdXJlKGluZGV4LCAndW5pdF9wcmljZScsIHBhcnNlRmxvYXQoZS50YXJnZXQudmFsdWUpIHx8IDApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy0yNCBoLThcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgUiQge3Byb2MudG90YWxfcHJpY2UudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJlbW92ZVByb2NlZHVyZShpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHctOCBwLTAgdGV4dC1kZXN0cnVjdGl2ZSBob3Zlcjp0ZXh0LWRlc3RydWN0aXZlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHQtMyBib3JkZXItdFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5Ub3RhbDo8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtYm9sZFwiPlIkIHtnZXRUb3RhbFByaWNlKCkudG9GaXhlZCgyKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L1RhYnNDb250ZW50PlxuICAgICAgICAgIDwvVGFicz5cblxuICAgICAgICAgIDxEaWFsb2dGb290ZXIgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXsoKSA9PiBvbk9wZW5DaGFuZ2UoZmFsc2UpfT5cbiAgICAgICAgICAgICAgQ2FuY2VsYXJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvbiB0eXBlPVwic3VibWl0XCIgZGlzYWJsZWQ9e3N1Ym1pdHRpbmcgfHwgbG9hZGluZ30+XG4gICAgICAgICAgICAgIHtzdWJtaXR0aW5nXG4gICAgICAgICAgICAgICAgPyAoaXNFZGl0aW5nID8gJ1NhbHZhbmRvLi4uJyA6ICdBZ2VuZGFuZG8uLi4nKVxuICAgICAgICAgICAgICAgIDogKGlzRWRpdGluZyA/ICdTYWx2YXIgQWx0ZXJhw6fDtWVzJyA6ICdBZ2VuZGFyIENvbnN1bHRhJylcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9EaWFsb2dGb290ZXI+XG4gICAgICAgIDwvZm9ybT5cbiAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICA8L0RpYWxvZz5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEFwcG9pbnRtZW50Rm9ybTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlRm9ybSIsIkNvbnRyb2xsZXIiLCJ6b2RSZXNvbHZlciIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dEZXNjcmlwdGlvbiIsIkRpYWxvZ0Zvb3RlciIsIkRpYWxvZ0hlYWRlciIsIkRpYWxvZ1RpdGxlIiwiQnV0dG9uIiwiSW5wdXQiLCJMYWJlbCIsIlRleHRhcmVhIiwiU2VsZWN0IiwiU2VsZWN0Q29udGVudCIsIlNlbGVjdEl0ZW0iLCJTZWxlY3RUcmlnZ2VyIiwiU2VsZWN0VmFsdWUiLCJUYWJzIiwiVGFic0NvbnRlbnQiLCJUYWJzTGlzdCIsIlRhYnNUcmlnZ2VyIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJhZGdlIiwiQ2hlY2tib3giLCJSYWRpb0dyb3VwIiwiUmFkaW9Hcm91cEl0ZW0iLCJEYXRlVGltZUlucHV0IiwiQ2FsZW5kYXIiLCJVc2VyIiwiU3RldGhvc2NvcGUiLCJQbHVzIiwiVHJhc2gyIiwiU2VhcmNoIiwiQWxlcnRDaXJjbGUiLCJ1c2VUb2FzdCIsImFkZE1pbnV0ZXMiLCJhcHBvaW50bWVudFNjaGVtYSIsInVzZUZvcm1TdWJtaXNzaW9uIiwiYnJhemlsaWFuVGltZXpvbmVPZmZzZXQiLCJ0b0JyYXppbGlhblRpbWUiLCJkYXRlIiwiZGF0ZU9iaiIsIkRhdGUiLCJ1dGNUaW1lIiwiZ2V0VGltZSIsImdldFRpbWV6b25lT2Zmc2V0IiwiYnJhemlsaWFuVGltZSIsImZvcm1hdERhdGVUaW1lRm9yQnJhemlsaWFuSW5wdXQiLCJkYXRlVGltZSIsImlzTmFOIiwieWVhciIsImdldEZ1bGxZZWFyIiwibW9udGgiLCJTdHJpbmciLCJnZXRNb250aCIsInBhZFN0YXJ0IiwiZGF5IiwiZ2V0RGF0ZSIsImhvdXJzIiwiZ2V0SG91cnMiLCJtaW51dGVzIiwiZ2V0TWludXRlcyIsImVycm9yIiwiY29uc29sZSIsInBhcnNlQnJhemlsaWFuRGF0ZVRpbWUiLCJkYXRlVGltZVN0cmluZyIsImluY2x1ZGVzIiwiQXBwb2ludG1lbnRGb3JtIiwib3BlbiIsIm9uT3BlbkNoYW5nZSIsInBhdGllbnRzIiwiaGVhbHRoY2FyZVByb2Zlc3Npb25hbHMiLCJwcm9jZWR1cmVzIiwiaW5pdGlhbERhdGEiLCJvblN1Ym1pdCIsImxvYWRpbmciLCJ0b2FzdCIsImFjdGl2ZVRhYiIsInNldEFjdGl2ZVRhYiIsInNlYXJjaFByb2NlZHVyZSIsInNldFNlYXJjaFByb2NlZHVyZSIsInNlbGVjdGVkUHJvY2VkdXJlcyIsInNldFNlbGVjdGVkUHJvY2VkdXJlcyIsImlzRWRpdGluZyIsIkJvb2xlYW4iLCJpZCIsImV4ZWN1dGUiLCJzdWJtaXRGb3JtIiwic3VibWl0dGluZyIsInN1Y2Nlc3NNZXNzYWdlIiwiZXJyb3JNZXNzYWdlIiwiZm9ybSIsInJlc29sdmVyIiwiZGVmYXVsdFZhbHVlcyIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJwYXRpZW50X2lkIiwiaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfaWQiLCJzdGFydF90aW1lIiwiZW5kX3RpbWUiLCJ0eXBlIiwic3RhdHVzIiwibm90ZXMiLCJoYXNfcmVjdXJyZW5jZSIsInJlY3VycmVuY2VfdHlwZSIsInJlY3VycmVuY2VfaW50ZXJ2YWwiLCJyZWN1cnJlbmNlX2RheXMiLCJyZWN1cnJlbmNlX2VuZF90eXBlIiwicmVjdXJyZW5jZV9lbmRfZGF0ZSIsInJlY3VycmVuY2VfY291bnQiLCJjb250cm9sIiwiaGFuZGxlU3VibWl0Iiwid2F0Y2giLCJzZXRWYWx1ZSIsInJlc2V0IiwiZm9ybVN0YXRlIiwiZXJyb3JzIiwid2F0Y2hlZFZhbHVlcyIsImxvZyIsImZvcm1hdHRlZERhdGEiLCJwYXRpZW50IiwiZmluZCIsInAiLCJ0eXBlTGFiZWwiLCJuYW1lIiwic3RhcnRUaW1lIiwidG90YWxEdXJhdGlvbiIsInJlZHVjZSIsInRvdGFsIiwicHJvYyIsInByb2NlZHVyZSIsInByb2NlZHVyZV9pZCIsImR1cmF0aW9uX21pbnV0ZXMiLCJxdWFudGl0eSIsImVuZFRpbWUiLCJmb3JtYXR0ZWRFbmRUaW1lIiwiZmlsdGVyZWRQcm9jZWR1cmVzIiwiZmlsdGVyIiwidG9Mb3dlckNhc2UiLCJhZGRQcm9jZWR1cmUiLCJleGlzdGluZ0luZGV4IiwiZmluZEluZGV4IiwidXBkYXRlZCIsInRvdGFsX3ByaWNlIiwidW5pdF9wcmljZSIsIm5ld1Byb2NlZHVyZSIsInByb2NlZHVyZV9uYW1lIiwiZGVmYXVsdF9wcmljZSIsInVwZGF0ZVByb2NlZHVyZSIsImluZGV4IiwiZmllbGQiLCJ2YWx1ZSIsInJlbW92ZVByb2NlZHVyZSIsIl8iLCJpIiwiZ2V0VG90YWxQcmljZSIsIm9uRm9ybVN1Ym1pdCIsImRhdGEiLCJ2YXJpYW50IiwidG9JU09TdHJpbmciLCJhcHBvaW50bWVudERhdGEiLCJ0b2dnbGVSZWN1cnJlbmNlRGF5IiwiY3VycmVudERheXMiLCJuZXdEYXlzIiwiZCIsIndlZWtEYXlzIiwibGFiZWwiLCJjbGFzc05hbWUiLCJvblZhbHVlQ2hhbmdlIiwiZGl2IiwiaHRtbEZvciIsInJlbmRlciIsIm9uQ2hhbmdlIiwicGxhY2Vob2xkZXIiLCJtYXAiLCJtZXNzYWdlIiwicHJvZiIsImlzX2FjdGl2ZSIsInByb2Zlc3Npb25hbCIsInNwZWNpYWx0eSIsImNoZWNrZWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJtaW4iLCJlIiwicGFyc2VJbnQiLCJ0YXJnZXQiLCJzaXplIiwib25DbGljayIsImRpc2FibGVkIiwiaDQiLCJ0b0ZpeGVkIiwibGVuZ3RoIiwic3RlcCIsInBhcnNlRmxvYXQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AppointmentForm.tsx\n"));

/***/ })

});