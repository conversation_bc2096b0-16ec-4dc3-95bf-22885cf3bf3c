import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    const body = await request.json();
    const { appointment_id } = body;

    if (!appointment_id) {
      return NextResponse.json({ error: 'appointment_id is required' }, { status: 400 });
    }

    // Verify the appointment belongs to the user
    const { data: appointment, error: appointmentError } = await supabase
      .from('appointments')
      .select('id, user_id')
      .eq('id', appointment_id)
      .eq('user_id', userId)
      .single();

    if (appointmentError || !appointment) {
      return NextResponse.json({ error: 'Appointment not found' }, { status: 404 });
    }

    // Delete all draft medical records for this appointment
    const { error: deleteError } = await supabase
      .from('medical_records')
      .delete()
      .eq('appointment_id', appointment_id)
      .eq('user_id', userId)
      .eq('is_draft', true);

    if (deleteError) {
      console.error('Error deleting draft records:', deleteError);
      return NextResponse.json({ error: 'Failed to cleanup drafts' }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Draft records cleaned up successfully' 
    });

  } catch (error) {
    console.error('Error in cleanup-drafts API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
