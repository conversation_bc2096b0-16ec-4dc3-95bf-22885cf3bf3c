"use client"

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  ArrowLeft,
  User,
  Calendar,
  Clock,
  FileText,
  Save,
  CheckCircle,
  AlertCircle,
  Stethoscope,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { formatDateBR, formatTimeBR, formatDateTimeBR } from '@/lib/date-utils';
import { makeAuthenticatedRequest } from '@/lib/api-client';

type Patient = {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  birth_date: string | null;
  cpf: string | null;
  address: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
};

type Appointment = {
  id: string;
  title: string;
  description: string | null;
  patient_id: string;
  patient_name?: string;
  healthcare_professional_id: string | null;
  healthcare_professional_name?: string;
  start_time: string;
  end_time: string;
  type: string;
  status: string;
  price: number | null;
};

type MedicalRecord = {
  id: string;
  appointment_id: string;
  patient_id: string;
  notes: string;
  created_at: string;
  updated_at: string | null;
  created_by_name: string;
  is_draft: boolean;
};

const MedicalRecordPage = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  const patientId = params.patientId as string;
  const appointmentId = searchParams.get('appointment_id');
  
  const [patient, setPatient] = useState<Patient | null>(null);
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);
  const [currentRecord, setCurrentRecord] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [autoSaving, setAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isDraft, setIsDraft] = useState(false);

  useEffect(() => {
    if (patientId) {
      fetchPatientData();
      if (appointmentId) {
        fetchAppointmentData();
        fetchMedicalRecords();
      }
    }
  }, [patientId, appointmentId]);

  // Auto-save functionality
  useEffect(() => {
    if (currentRecord.trim() && appointmentId && !saving) {
      const autoSaveTimer = setTimeout(() => {
        handleSaveDraft();
      }, 5000); // Auto-save after 5 seconds of inactivity

      return () => clearTimeout(autoSaveTimer);
    }
  }, [currentRecord, appointmentId, saving]);

  const fetchPatientData = async () => {
    try {
      const response = await makeAuthenticatedRequest(`/api/patients/${patientId}`);
      if (!response.ok) throw new Error('Failed to fetch patient data');
      const result = await response.json();
      setPatient(result.data || result);
    } catch (error) {
      console.error('Error fetching patient:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar dados do paciente.",
        variant: "destructive"
      });
    }
  };

  const fetchAppointmentData = async () => {
    if (!appointmentId) return;
    
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`);
      if (!response.ok) throw new Error('Failed to fetch appointment data');
      const result = await response.json();
      setAppointment(result.data || result);
    } catch (error) {
      console.error('Error fetching appointment:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar dados da consulta.",
        variant: "destructive"
      });
    }
  };

  const fetchMedicalRecords = async () => {
    if (!appointmentId) return;
    
    try {
      setLoading(true);
      const response = await makeAuthenticatedRequest(`/api/medical-records?appointment_id=${appointmentId}`);
      if (!response.ok) throw new Error('Failed to fetch medical records');
      const result = await response.json();
      const records = result.data || result;
      setMedicalRecords(Array.isArray(records) ? records : []);
      
      // Check if there's a draft record
      const draftRecord = records.find((record: MedicalRecord) => record.is_draft);
      if (draftRecord) {
        setCurrentRecord(draftRecord.notes);
        setIsDraft(true);
      }
    } catch (error) {
      console.error('Error fetching medical records:', error);
      setMedicalRecords([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveDraft = async () => {
    if (!currentRecord.trim() || !appointmentId || saving) return;

    try {
      setAutoSaving(true);
      const response = await makeAuthenticatedRequest('/api/medical-records', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          appointment_id: appointmentId,
          patient_id: patientId,
          notes: currentRecord.trim(),
          is_draft: true
        })
      });

      if (!response.ok) throw new Error('Failed to save draft');

      setLastSaved(new Date());
      setIsDraft(true);
      
      toast({
        title: "Rascunho salvo",
        description: "Suas alterações foram salvas automaticamente.",
      });
    } catch (error) {
      console.error('Error saving draft:', error);
    } finally {
      setAutoSaving(false);
    }
  };

  const handleCompleteConsultation = async () => {
    if (!currentRecord.trim() || !appointmentId) {
      toast({
        title: "Erro",
        description: "Por favor, adicione suas observações antes de finalizar.",
        variant: "destructive"
      });
      return;
    }

    try {
      setSaving(true);
      
      // Save final medical record
      const recordResponse = await makeAuthenticatedRequest('/api/medical-records', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          appointment_id: appointmentId,
          patient_id: patientId,
          notes: currentRecord.trim(),
          is_draft: false
        })
      });

      if (!recordResponse.ok) throw new Error('Failed to save medical record');

      // Update appointment status to completed
      const appointmentResponse = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'completed' })
      });

      if (!appointmentResponse.ok) throw new Error('Failed to update appointment status');

      toast({
        title: "Sucesso!",
        description: "Atendimento finalizado com sucesso.",
      });

      // Navigate back to agenda
      router.push('/dashboard/agenda');
    } catch (error) {
      console.error('Error completing consultation:', error);
      toast({
        title: "Erro",
        description: "Erro ao finalizar atendimento.",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const calculateAge = (birthDate: string | null): string => {
    if (!birthDate) return 'Não informado';
    
    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return `${age} anos`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Carregando prontuário...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Voltar
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Prontuário</h1>
          <p className="text-muted-foreground">
            {patient?.name && `Histórico médico de ${patient.name}`}
          </p>
        </div>
      </div>

      {/* Patient Information */}
      {patient && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Informações do Paciente
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label className="text-sm font-medium">Nome</Label>
                <p className="text-sm">{patient.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Email</Label>
                <p className="text-sm">{patient.email || 'Não informado'}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Telefone</Label>
                <p className="text-sm">{patient.phone || 'Não informado'}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Data de Nascimento</Label>
                <p className="text-sm">
                  {patient.birth_date ? formatDateBR(patient.birth_date) : 'Não informado'}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Idade</Label>
                <p className="text-sm">{calculateAge(patient.birth_date)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">CPF</Label>
                <p className="text-sm">{patient.cpf || 'Não informado'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Appointment Information */}
      {appointment && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Informações da Consulta
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label className="text-sm font-medium">Título</Label>
                <p className="text-sm">{appointment.title}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Data</Label>
                <p className="text-sm">{formatDateBR(appointment.start_time)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium">Horário</Label>
                <p className="text-sm">
                  {formatTimeBR(appointment.start_time)} - {formatTimeBR(appointment.end_time)}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Status</Label>
                <Badge
                  variant={
                    appointment.status === 'completed' ? 'secondary' :
                    appointment.status === 'in_progress' ? 'default' :
                    appointment.status === 'cancelled' ? 'destructive' :
                    'outline'
                  }
                >
                  {appointment.status === 'scheduled' && 'Agendado'}
                  {appointment.status === 'confirmed' && 'Confirmado'}
                  {appointment.status === 'completed' && 'Concluído'}
                  {appointment.status === 'cancelled' && 'Cancelado'}
                  {appointment.status === 'in_progress' && 'Em Andamento'}
                </Badge>
              </div>
              {appointment.healthcare_professional_name && (
                <div className="md:col-span-2">
                  <Label className="text-sm font-medium">Profissional</Label>
                  <p className="text-sm">{appointment.healthcare_professional_name}</p>
                </div>
              )}
              {appointment.description && (
                <div className="md:col-span-2">
                  <Label className="text-sm font-medium">Descrição</Label>
                  <p className="text-sm">{appointment.description}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* New Medical Record Entry */}
      {appointmentId && appointment?.status !== 'completed' && (
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Nova Anotação
                </CardTitle>
                <CardDescription>
                  Adicione uma nova entrada ao prontuário do paciente
                </CardDescription>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                {autoSaving && (
                  <div className="flex items-center gap-1">
                    <div className="animate-spin rounded-full h-3 w-3 border-b border-primary"></div>
                    <span>Salvando...</span>
                  </div>
                )}
                {lastSaved && !autoSaving && (
                  <div className="flex items-center gap-1">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>Salvo às {formatTimeBR(lastSaved.toISOString())}</span>
                  </div>
                )}
                {isDraft && (
                  <Badge variant="outline" className="text-xs">
                    Rascunho
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="medical-notes">Observações Médicas</Label>
              <Textarea
                id="medical-notes"
                placeholder="Digite suas observações sobre a consulta, diagnóstico, tratamento recomendado, etc..."
                value={currentRecord}
                onChange={(e) => setCurrentRecord(e.target.value)}
                rows={8}
                className="min-h-[200px]"
              />
              <p className="text-xs text-muted-foreground">
                As anotações são automaticamente criptografadas e salvas como rascunho a cada 5 segundos.
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handleSaveDraft}
                disabled={!currentRecord.trim() || autoSaving}
                variant="outline"
              >
                <Save className="h-4 w-4 mr-2" />
                Salvar Rascunho
              </Button>

              <Button
                onClick={handleCompleteConsultation}
                disabled={!currentRecord.trim() || saving}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                {saving ? 'Finalizando...' : 'Finalizar Atendimento'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Medical Records History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Stethoscope className="h-5 w-5" />
            Histórico de Anotações
          </CardTitle>
          <CardDescription>
            {appointmentId
              ? 'Registros médicos desta consulta'
              : 'Histórico completo de registros médicos do paciente'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {medicalRecords.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
              <p>Nenhum registro médico encontrado</p>
            </div>
          ) : (
            <div className="space-y-4">
              {medicalRecords
                .filter(record => !record.is_draft) // Only show finalized records
                .map((record) => (
                <div key={record.id} className="border-l-4 border-primary pl-4 py-3 bg-muted/30 rounded-r-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        {formatDateTimeBR(record.created_at)}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {record.created_by_name}
                      </Badge>
                    </div>
                    {record.updated_at && record.updated_at !== record.created_at && (
                      <span className="text-xs text-muted-foreground">
                        Editado em {formatDateTimeBR(record.updated_at)}
                      </span>
                    )}
                  </div>
                  <div className="prose prose-sm max-w-none">
                    <p className="text-sm whitespace-pre-wrap leading-relaxed">
                      {record.notes}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MedicalRecordPage;
