"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxjaXJvdlxcRG9jdW1lbnRzXFxuZXh0LWpzXFxuZWN0YXJcXG5lY3Rhci1uZXh0anNcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcYXBpXFxuYXZpZ2F0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gJy4uL2NsaWVudC9jb21wb25lbnRzL25hdmlnYXRpb24nO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1uYXZpZ2F0aW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    // Fetch initial data that doesn't depend on selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], []);\n    // Fetch date-specific appointments when selected date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            // FullCalendar provides dates in local timezone, use them directly\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.start);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.end);\n        } else if (selectedDate) {\n            // Create appointment for selected date at 9:00 AM\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(startTime);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(endTime);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // Open edit form with appointment data\n        console.log('Appointment clicked for editing:', appointment);\n        const editData = {\n            id: appointment.id,\n            title: appointment.title,\n            description: appointment.description,\n            patient_id: appointment.patient_id,\n            healthcare_professional_id: appointment.healthcare_professional_id,\n            start_time: appointment.start_time,\n            end_time: appointment.end_time,\n            type: appointment.type,\n            status: appointment.status,\n            price: appointment.price\n        };\n        setAppointmentFormData(editData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const isEditing = data.id;\n            const url = isEditing ? \"/api/appointments/\".concat(data.id) : '/api/appointments';\n            const method = isEditing ? 'PUT' : 'POST';\n            // Remove id from data for API call\n            const { id, ...submitData } = data;\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(submitData)\n            });\n            if (!response.ok) throw new Error(\"Failed to \".concat(isEditing ? 'update' : 'create', \" appointment\"));\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error(\"Error \".concat(data.id ? 'updating' : 'creating', \" appointment:\"), error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR,\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    // appointmentCounts={appointmentCounts}\n                                                    // clinicSettings={clinicSettings || undefined}\n                                                    // appointments={allAppointments}\n                                                    className: \"rounded-md border-0 shadow-none w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatDateBR)(selectedDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border bg-card/50 hover:bg-card transition-colors cursor-pointer space-y-3 sm:space-y-0\",\n                                                            onClick: ()=>handleAppointmentClick(appointment),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.start_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 463,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.end_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 466,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 462,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 471,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 472,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 476,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 461,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && 'Agendado',\n                                                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                                                appointment.status === 'completed' && 'Concluído',\n                                                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium whitespace-nowrap\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                appointment.price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 499,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            appointments: allAppointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 526,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 350,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"xLZJ7pt+Iz6ZUtzTScoCCOXUoEE=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});