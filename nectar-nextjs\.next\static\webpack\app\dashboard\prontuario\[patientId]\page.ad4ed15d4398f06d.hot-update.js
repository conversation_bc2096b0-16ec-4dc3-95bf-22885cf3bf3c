"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/prontuario/[patientId]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/prontuario/[patientId]/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/dashboard/prontuario/[patientId]/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MedicalRecordPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const patientId = params.patientId;\n    const appointmentId = searchParams.get('appointment_id');\n    const [patient, setPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [appointment, setAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecords, setMedicalRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentRecord, setCurrentRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoSaving, setAutoSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDraft, setIsDraft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicalRecordPage.useEffect\": ()=>{\n            if (patientId) {\n                fetchPatientData();\n                if (appointmentId) {\n                    fetchAppointmentData();\n                    fetchMedicalRecords();\n                }\n            }\n        }\n    }[\"MedicalRecordPage.useEffect\"], [\n        patientId,\n        appointmentId\n    ]);\n    // Auto-save functionality - 2 minutes of inactivity\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicalRecordPage.useEffect\": ()=>{\n            if (currentRecord.trim() && appointmentId && !saving) {\n                const autoSaveTimer = setTimeout({\n                    \"MedicalRecordPage.useEffect.autoSaveTimer\": ()=>{\n                        handleSaveDraft();\n                    }\n                }[\"MedicalRecordPage.useEffect.autoSaveTimer\"], 120000); // Auto-save after 2 minutes of inactivity\n                return ({\n                    \"MedicalRecordPage.useEffect\": ()=>clearTimeout(autoSaveTimer)\n                })[\"MedicalRecordPage.useEffect\"];\n            }\n        }\n    }[\"MedicalRecordPage.useEffect\"], [\n        currentRecord,\n        appointmentId,\n        saving\n    ]);\n    const fetchPatientData = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch patient data');\n            const result = await response.json();\n            setPatient(result.data || result);\n        } catch (error) {\n            console.error('Error fetching patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados do paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchAppointmentData = async ()=>{\n        if (!appointmentId) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch appointment data');\n            const result = await response.json();\n            setAppointment(result.data || result);\n        } catch (error) {\n            console.error('Error fetching appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchMedicalRecords = async ()=>{\n        if (!appointmentId) return;\n        try {\n            setLoading(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/medical-records?appointment_id=\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch medical records');\n            const result = await response.json();\n            const records = result.data || result;\n            setMedicalRecords(Array.isArray(records) ? records : []);\n            // Check if there's a draft record\n            const draftRecord = records.find((record)=>record.is_draft);\n            if (draftRecord) {\n                setCurrentRecord(draftRecord.notes);\n                setIsDraft(true);\n            }\n        } catch (error) {\n            console.error('Error fetching medical records:', error);\n            setMedicalRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveDraft = async ()=>{\n        if (!currentRecord.trim() || !appointmentId || saving) return;\n        try {\n            setAutoSaving(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId,\n                    patient_id: patientId,\n                    notes: currentRecord.trim(),\n                    is_draft: true\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.message || 'Failed to save draft');\n            }\n            setLastSaved(new Date());\n            setIsDraft(true);\n            toast({\n                title: \"Rascunho salvo\",\n                description: \"Suas alterações foram salvas automaticamente.\"\n            });\n        } catch (error) {\n            console.error('Error saving draft:', error);\n            toast({\n                title: \"Erro ao salvar rascunho\",\n                description: \"Não foi possível salvar o rascunho. Suas alterações podem ser perdidas.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setAutoSaving(false);\n        }\n    };\n    const cleanupDrafts = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/medical-records/cleanup-drafts\", {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId\n                })\n            });\n            if (!response.ok) {\n                console.warn('Failed to cleanup drafts, but continuing with completion');\n            }\n        } catch (error) {\n            console.warn('Error cleaning up drafts:', error);\n        // Don't throw error here - draft cleanup failure shouldn't prevent completion\n        }\n    };\n    const handleCompleteConsultation = async ()=>{\n        if (!currentRecord.trim() || !appointmentId) {\n            toast({\n                title: \"Erro\",\n                description: \"Por favor, adicione suas observações antes de finalizar.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            // Save final medical record\n            const recordResponse = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId,\n                    patient_id: patientId,\n                    notes: currentRecord.trim(),\n                    is_draft: false\n                })\n            });\n            if (!recordResponse.ok) throw new Error('Failed to save medical record');\n            // Update appointment status to completed\n            const appointmentResponse = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'completed'\n                })\n            });\n            if (!appointmentResponse.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Atendimento finalizado com sucesso.\"\n            });\n            // Navigate back to agenda\n            router.push('/dashboard/agenda');\n        } catch (error) {\n            console.error('Error completing consultation:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao finalizar atendimento.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const calculateAge = (birthDate)=>{\n        if (!birthDate) return 'Não informado';\n        const birth = new Date(birthDate);\n        const today = new Date();\n        let age = today.getFullYear() - birth.getFullYear();\n        const monthDiff = today.getMonth() - birth.getMonth();\n        if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birth.getDate()) {\n            age--;\n        }\n        return \"\".concat(age, \" anos\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-muted-foreground\",\n                        children: \"Carregando prontu\\xe1rio...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n            lineNumber: 296,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Voltar\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Prontu\\xe1rio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: (patient === null || patient === void 0 ? void 0 : patient.name) && \"Hist\\xf3rico m\\xe9dico de \".concat(patient.name)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, undefined),\n            patient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Informa\\xe7\\xf5es do Paciente\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Nome\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.email || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Telefone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.phone || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Data de Nascimento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.birth_date ? (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateBR)(patient.birth_date) : 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Idade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: calculateAge(patient.birth_date)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"CPF\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.cpf || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 9\n            }, undefined),\n            appointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Informa\\xe7\\xf5es da Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"T\\xedtulo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateBR)(appointment.start_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Hor\\xe1rio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.start_time),\n                                                \" - \",\n                                                (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.end_time)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: appointment.status === 'completed' ? 'secondary' : appointment.status === 'in_progress' ? 'default' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                            children: [\n                                                appointment.status === 'scheduled' && 'Agendado',\n                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                appointment.status === 'completed' && 'Concluído',\n                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, undefined),\n                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profissional\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.healthcare_professional_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, undefined),\n                                appointment.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 376,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 369,\n                columnNumber: 9\n            }, undefined),\n            appointmentId && (appointment === null || appointment === void 0 ? void 0 : appointment.status) !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Nova Anota\\xe7\\xe3o\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Adicione uma nova entrada ao prontu\\xe1rio do paciente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                    children: [\n                                        autoSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-3 w-3 border-b border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        lastSaved && !autoSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-3 w-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Salvo \\xe0s \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(lastSaved.toISOString())\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isDraft && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: \"Rascunho\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        htmlFor: \"medical-notes\",\n                                        children: \"Observa\\xe7\\xf5es M\\xe9dicas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        id: \"medical-notes\",\n                                        placeholder: \"Digite suas observa\\xe7\\xf5es sobre a consulta, diagn\\xf3stico, tratamento recomendado, etc...\",\n                                        value: currentRecord,\n                                        onChange: (e)=>setCurrentRecord(e.target.value),\n                                        rows: 8,\n                                        className: \"min-h-[200px]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"As anota\\xe7\\xf5es s\\xe3o automaticamente criptografadas e salvas como rascunho a cada 5 segundos.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleSaveDraft,\n                                        disabled: !currentRecord.trim() || autoSaving,\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Salvar Rascunho\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleCompleteConsultation,\n                                        disabled: !currentRecord.trim() || saving,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            saving ? 'Finalizando...' : 'Finalizar Atendimento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 428,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Hist\\xf3rico de Anota\\xe7\\xf5es\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: appointmentId ? 'Registros médicos desta consulta' : 'Histórico completo de registros médicos do paciente'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: medicalRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Nenhum registro m\\xe9dico encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: medicalRecords.filter((record)=>!record.is_draft) // Only show finalized records\n                            .map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-l-4 border-primary pl-4 py-3 bg-muted/30 rounded-r-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateTimeBR)(record.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: record.created_by_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                record.updated_at && record.updated_at !== record.created_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Editado em \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateTimeBR)(record.updated_at)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-sm max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                                children: record.notes\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, record.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 514,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 501,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n        lineNumber: 306,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MedicalRecordPage, \"Gd55zBCyJLy2D5fiJMb/bjlVCS4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = MedicalRecordPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MedicalRecordPage);\nvar _c;\n$RefreshReg$(_c, \"MedicalRecordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/prontuario/[patientId]/page.tsx\n"));

/***/ })

});