"use client"

import React, { useRef, useEffect, useState, useCallback } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { EventInput, DateSelectArg, EventClickArg, EventDropArg } from '@fullcalendar/core';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Filter, Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

type Appointment = {
  id: string;
  title: string;
  description: string | null;
  patient_id: string;
  patient_name?: string;
  healthcare_professional_id: string | null;
  healthcare_professional_name?: string;
  start_time: string;
  end_time: string;
  type: string;
  status: string;
  price: number | null;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty: string | null;
  is_active: boolean;
};

interface FullCalendarViewProps {
  appointments: Appointment[];
  healthcareProfessionals: HealthcareProfessional[];
  onAppointmentCreate: (selectInfo: DateSelectArg) => void;
  onAppointmentClick: (appointment: Appointment) => void;
  onAppointmentUpdate: (appointmentId: string, newStart: Date, newEnd: Date) => Promise<void>;
  loading?: boolean;
}

// Move getStatusColor outside the component to avoid initialization issues
const getStatusColor = (status: string): string => {
  const colors = {
    'scheduled': '#3b82f6', // blue
    'confirmed': '#10b981', // green
    'in_progress': '#f59e0b', // amber
    'completed': '#6b7280', // gray
    'cancelled': '#ef4444', // red
  };
  return colors[status as keyof typeof colors] || '#6b7280';
};

const FullCalendarView: React.FC<FullCalendarViewProps> = ({
  appointments,
  healthcareProfessionals,
  onAppointmentCreate,
  onAppointmentClick,
  onAppointmentUpdate,
  loading = false
}) => {
  const calendarRef = useRef<FullCalendar>(null);
  const [selectedProfessional, setSelectedProfessional] = useState<string>('all');
  const [currentView, setCurrentView] = useState<string>('timeGridWeek');
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const { toast } = useToast();

  // Initialize component after data is loaded
  useEffect(() => {
    if (!loading && appointments && healthcareProfessionals) {
      setIsInitialized(true);
    }
  }, [loading, appointments, healthcareProfessionals]);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      // Auto-switch to day view on mobile
      if (window.innerWidth < 768 && currentView === 'timeGridWeek') {
        setCurrentView('timeGridDay');
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [currentView]);

  // Filter appointments by selected healthcare professional
  const filteredAppointments = React.useMemo(() => {
    if (selectedProfessional === 'all') {
      return appointments;
    }
    return appointments.filter(apt => apt.healthcare_professional_id === selectedProfessional);
  }, [appointments, selectedProfessional]);

  // Convert appointments to FullCalendar events
  const events: EventInput[] = React.useMemo(() => {
    return filteredAppointments.map(appointment => ({
      id: appointment.id,
      title: appointment.title,
      start: appointment.start_time,
      end: appointment.end_time,
      backgroundColor: getStatusColor(appointment.status),
      borderColor: getStatusColor(appointment.status),
      textColor: '#ffffff',
      extendedProps: {
        appointment,
        description: appointment.description,
        patientName: appointment.patient_name,
        professionalName: appointment.healthcare_professional_name,
        status: appointment.status,
        type: appointment.type,
        price: appointment.price
      }
    }));
  }, [filteredAppointments]);

  const handleDateSelect = useCallback((selectInfo: DateSelectArg) => {
    if (onAppointmentCreate) {
      onAppointmentCreate(selectInfo);
    }
  }, [onAppointmentCreate]);

  const handleEventClick = useCallback((clickInfo: EventClickArg) => {
    const appointment = clickInfo.event.extendedProps.appointment as Appointment;
    if (onAppointmentClick && appointment) {
      onAppointmentClick(appointment);
    }
  }, [onAppointmentClick]);

  const handleEventDrop = useCallback(async (dropInfo: EventDropArg) => {
    try {
      const appointmentId = dropInfo.event.id;
      const newStart = dropInfo.event.start;
      const newEnd = dropInfo.event.end;

      if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {
        dropInfo.revert();
        return;
      }

      await onAppointmentUpdate(appointmentId, newStart, newEnd);

      toast({
        title: "Sucesso!",
        description: "Consulta reagendada com sucesso.",
      });
    } catch (error) {
      console.error('Error updating appointment:', error);
      dropInfo.revert();
      toast({
        title: "Erro",
        description: "Erro ao reagendar consulta.",
        variant: "destructive"
      });
    }
  }, [onAppointmentUpdate, toast]);

  const handleViewChange = (view: string) => {
    setCurrentView(view);
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.changeView(view);
    }
  };

  const goToToday = () => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.today();
    }
  };

  const navigateCalendar = (direction: 'prev' | 'next') => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      if (direction === 'prev') {
        calendarApi.prev();
      } else {
        calendarApi.next();
      }
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="space-y-4">
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5 text-primary" />
            Agenda Completa
          </CardTitle>

          {/* Mobile-first responsive controls */}
          <div className="space-y-3 sm:space-y-0 sm:flex sm:flex-wrap sm:justify-between sm:items-center sm:gap-4">
            {/* Healthcare Professional Filter */}
            <div className="flex items-center gap-2 min-w-0 flex-1 sm:flex-initial">
              <Filter className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <Select value={selectedProfessional} onValueChange={setSelectedProfessional}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="Filtrar por profissional" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os profissionais</SelectItem>
                  {healthcareProfessionals
                    .filter(prof => prof.is_active)
                    .map(professional => (
                      <SelectItem key={professional.id} value={professional.id}>
                        {professional.name}
                        {professional.specialty && ` - ${professional.specialty}`}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col xs:flex-row gap-2 xs:gap-4">
              {/* View Controls */}
              <div className="flex items-center gap-1">
                <Button
                  variant={currentView === 'dayGridMonth' ? 'default' : 'outline'}
                  size={isMobile ? 'sm' : 'sm'}
                  onClick={() => handleViewChange('dayGridMonth')}
                  className="flex-1 xs:flex-initial"
                >
                  {isMobile ? 'M' : 'Mês'}
                </Button>
                {!isMobile && (
                  <Button
                    variant={currentView === 'timeGridWeek' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleViewChange('timeGridWeek')}
                  >
                    Semana
                  </Button>
                )}
                <Button
                  variant={currentView === 'timeGridDay' ? 'default' : 'outline'}
                  size={isMobile ? 'sm' : 'sm'}
                  onClick={() => handleViewChange('timeGridDay')}
                  className="flex-1 xs:flex-initial"
                >
                  {isMobile ? 'D' : 'Dia'}
                </Button>
              </div>

              {/* Navigation Controls */}
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateCalendar('prev')}
                  className="flex-1 xs:flex-initial"
                >
                  ‹
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToToday}
                  className="flex-1 xs:flex-initial"
                >
                  Hoje
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigateCalendar('next')}
                  className="flex-1 xs:flex-initial"
                >
                  ›
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="fullcalendar-container">
          {!isInitialized || loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-muted-foreground">Carregando agenda...</p>
              </div>
            </div>
          ) : (
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView={isMobile ? "timeGridDay" : "timeGridWeek"}
              headerToolbar={false} // We're using custom header
              height="auto"
              contentHeight={isMobile ? 400 : "auto"}
              events={events || []}
              selectable={true}
              selectMirror={true}
              editable={!isMobile} // Disable drag on mobile for better UX
              droppable={!isMobile}
              eventResizableFromStart={!isMobile}
              select={handleDateSelect}
              eventClick={handleEventClick}
              eventDrop={handleEventDrop}
              slotMinTime="00:00:00"
              slotMaxTime="24:00:00"
              slotDuration="00:30:00"
              slotLabelInterval={isMobile ? "02:00:00" : "01:00:00"}
              allDaySlot={false}
              nowIndicator={true}
              businessHours={{
                daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
                startTime: '08:00',
                endTime: '18:00',
              }}
              eventDisplay="block"
              dayMaxEvents={isMobile ? 3 : true}
              moreLinkClick="popover"
              // Mobile-specific settings
              aspectRatio={isMobile ? 1.2 : 1.35}
              handleWindowResize={true}
              stickyHeaderDates={!isMobile}
              // Locale configuration - Fixed to use string format
              locale="pt-br"
              buttonText={{
                today: 'Hoje',
                month: 'Mês',
                week: 'Semana',
                day: 'Dia',
                list: 'Lista'
              }}
              weekText="Sm"
              allDayText="Todo o dia"
              moreLinkText="mais"
              noEventsText="Não há eventos para mostrar"
              firstDay={0} // Sunday = 0
              eventContent={(eventInfo) => {
                const title = eventInfo.event.title || '';
                const patientName = eventInfo.event.extendedProps?.patientName;
                const professionalName = eventInfo.event.extendedProps?.professionalName;
                const startTime = eventInfo.event.start;

                return (
                  <div className={`p-1 ${isMobile ? 'text-xs' : 'text-xs'}`}>
                    <div className="font-medium truncate text-xs sm:text-sm">
                      {isMobile && title.length > 20 ? title.substring(0, 20) + '...' : title}
                    </div>
                    {patientName && !isMobile && (
                      <div className="text-xs opacity-90 truncate">
                        {patientName}
                      </div>
                    )}
                    {professionalName && !isMobile && (
                      <div className="text-xs opacity-75 truncate">
                        {professionalName}
                      </div>
                    )}
                    {isMobile && startTime && (
                      <div className="text-xs opacity-75 truncate">
                        {format(startTime, 'HH:mm')}
                      </div>
                    )}
                  </div>
                );
              }}
            />
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default FullCalendarView;