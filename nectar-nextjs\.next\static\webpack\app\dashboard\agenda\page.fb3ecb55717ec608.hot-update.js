"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/AppointmentForm.tsx":
/*!********************************************!*\
  !*** ./src/components/AppointmentForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/datetime-input */ \"(app-pages-browser)/./src/components/ui/datetime-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=addMinutes!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addMinutes.js\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useAsyncOperation */ \"(app-pages-browser)/./src/hooks/useAsyncOperation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Utility functions for Brazilian timezone (UTC-3)\nconst brazilianTimezoneOffset = -3 * 60; // -3 hours in minutes\nconst toBrazilianTime = (date)=>{\n    if (!date) return null;\n    // Se for string, converte para Date\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    // Cria uma nova data ajustada para UTC-3\n    const utcTime = dateObj.getTime() + dateObj.getTimezoneOffset() * 60000;\n    const brazilianTime = new Date(utcTime + brazilianTimezoneOffset * 60000);\n    return brazilianTime;\n};\nconst formatDateTimeForBrazilianInput = (dateTime)=>{\n    if (!dateTime) return '';\n    try {\n        const date = toBrazilianTime(dateTime);\n        if (!date || isNaN(date.getTime())) return '';\n        // Formato para datetime-local: YYYY-MM-DDTHH:mm\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n        return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"T\").concat(hours, \":\").concat(minutes);\n    } catch (error) {\n        console.error('[DATETIME] Error formatting date:', error);\n        return '';\n    }\n};\nconst parseBrazilianDateTime = (dateTimeString)=>{\n    if (!dateTimeString) return null;\n    try {\n        // Se já tem timezone, usa diretamente\n        if (dateTimeString.includes('T') && (dateTimeString.includes('Z') || dateTimeString.includes('-03:00'))) {\n            return new Date(dateTimeString);\n        }\n        // Se é formato datetime-local (YYYY-MM-DDTHH:mm), adiciona timezone brasileiro\n        if (dateTimeString.includes('T')) {\n            return new Date(dateTimeString + ':00-03:00');\n        }\n        // Fallback para outros formatos\n        const date = new Date(dateTimeString);\n        return toBrazilianTime(date);\n    } catch (error) {\n        console.error('[DATETIME] Error parsing date:', error);\n        return null;\n    }\n};\nconst AppointmentForm = (param)=>{\n    let { open, onOpenChange, patients, healthcareProfessionals, procedures, initialData, onSubmit, loading = false } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('scheduling');\n    const [searchProcedure, setSearchProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedProcedures, setSelectedProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { execute: submitForm, loading: submitting } = (0,_hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission)({\n        successMessage: 'Consulta agendada com sucesso!',\n        errorMessage: 'Erro ao agendar consulta'\n    });\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_16__.appointmentSchema),\n        defaultValues: {\n            title: '',\n            description: '',\n            patient_id: '',\n            healthcare_professional_id: '',\n            start_time: '',\n            end_time: '',\n            type: 'consultation',\n            notes: '',\n            has_recurrence: false,\n            recurrence_type: 'weekly',\n            recurrence_interval: 1,\n            recurrence_days: [],\n            recurrence_end_type: 'never',\n            recurrence_end_date: '',\n            recurrence_count: 1\n        }\n    });\n    const { control, handleSubmit, watch, setValue, reset, formState: { errors } } = form;\n    const watchedValues = watch();\n    // Initialize form with initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (initialData && open) {\n                console.log('[DATETIME] Initializing form with data:', initialData);\n                const formattedData = {\n                    ...initialData,\n                    start_time: formatDateTimeForBrazilianInput(initialData.start_time),\n                    end_time: formatDateTimeForBrazilianInput(initialData.end_time),\n                    patient_id: initialData.patient_id || '',\n                    healthcare_professional_id: initialData.healthcare_professional_id || ''\n                };\n                console.log('[DATETIME] Formatted data for form:', formattedData);\n                reset(formattedData);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        initialData,\n        open,\n        reset\n    ]);\n    // Auto-generate title when patient and type change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (watchedValues.patient_id && watchedValues.type) {\n                const patient = patients.find({\n                    \"AppointmentForm.useEffect.patient\": (p)=>p.id === watchedValues.patient_id\n                }[\"AppointmentForm.useEffect.patient\"]);\n                if (patient) {\n                    const typeLabel = watchedValues.type === 'consultation' ? 'Consulta' : 'Retorno';\n                    setValue('title', \"\".concat(typeLabel, \" - \").concat(patient.name));\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.patient_id,\n        watchedValues.type,\n        patients,\n        setValue\n    ]);\n    // Auto-calculate end time based on start time (Brazilian timezone)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            console.log('[DATETIME] Auto-calculating end time, start_time:', watchedValues.start_time);\n            if (watchedValues.start_time) {\n                try {\n                    const startTime = parseBrazilianDateTime(watchedValues.start_time);\n                    console.log('[DATETIME] Parsed start time:', startTime, 'Valid:', startTime && !isNaN(startTime.getTime()));\n                    if (startTime && !isNaN(startTime.getTime())) {\n                        const totalDuration = selectedProcedures.reduce({\n                            \"AppointmentForm.useEffect.totalDuration\": (total, proc)=>{\n                                const procedure = procedures.find({\n                                    \"AppointmentForm.useEffect.totalDuration.procedure\": (p)=>p.id === proc.procedure_id\n                                }[\"AppointmentForm.useEffect.totalDuration.procedure\"]);\n                                return total + ((procedure === null || procedure === void 0 ? void 0 : procedure.duration_minutes) || 30) * proc.quantity;\n                            }\n                        }[\"AppointmentForm.useEffect.totalDuration\"], 30); // Default 30 minutes if no procedures\n                        console.log('[DATETIME] Total duration:', totalDuration, 'minutes');\n                        const endTime = (0,_barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__.addMinutes)(startTime, totalDuration);\n                        const formattedEndTime = formatDateTimeForBrazilianInput(endTime);\n                        console.log('[DATETIME] Calculated end time:', endTime, 'Formatted:', formattedEndTime);\n                        setValue('end_time', formattedEndTime);\n                    }\n                } catch (error) {\n                    console.error('[DATETIME] Error calculating end time:', error);\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.start_time,\n        selectedProcedures,\n        procedures,\n        setValue\n    ]);\n    const filteredProcedures = procedures.filter((procedure)=>procedure.name.toLowerCase().includes(searchProcedure.toLowerCase()) || procedure.description && procedure.description.toLowerCase().includes(searchProcedure.toLowerCase()));\n    const addProcedure = (procedure)=>{\n        const existingIndex = selectedProcedures.findIndex((p)=>p.procedure_id === procedure.id);\n        if (existingIndex >= 0) {\n            // Increase quantity if already exists\n            const updated = [\n                ...selectedProcedures\n            ];\n            updated[existingIndex].quantity += 1;\n            updated[existingIndex].total_price = updated[existingIndex].quantity * updated[existingIndex].unit_price;\n            setSelectedProcedures(updated);\n        } else {\n            // Add new procedure\n            const newProcedure = {\n                procedure_id: procedure.id,\n                procedure_name: procedure.name,\n                quantity: 1,\n                unit_price: procedure.default_price || 0,\n                total_price: procedure.default_price || 0\n            };\n            setSelectedProcedures([\n                ...selectedProcedures,\n                newProcedure\n            ]);\n        }\n    };\n    const updateProcedure = (index, field, value)=>{\n        const updated = [\n            ...selectedProcedures\n        ];\n        updated[index][field] = value;\n        updated[index].total_price = updated[index].quantity * updated[index].unit_price;\n        setSelectedProcedures(updated);\n    };\n    const removeProcedure = (index)=>{\n        setSelectedProcedures(selectedProcedures.filter((_, i)=>i !== index));\n    };\n    const getTotalPrice = ()=>{\n        return selectedProcedures.reduce((total, proc)=>total + proc.total_price, 0);\n    };\n    const onFormSubmit = async (data)=>{\n        console.log('[DATETIME] Form submitted with data:', data);\n        try {\n            // Parse and validate dates in Brazilian timezone\n            const startTime = parseBrazilianDateTime(data.start_time);\n            const endTime = parseBrazilianDateTime(data.end_time);\n            if (!startTime || !endTime) {\n                toast({\n                    title: 'Erro',\n                    description: 'Datas inválidas. Por favor, verifique os horários.',\n                    variant: 'destructive'\n                });\n                return;\n            }\n            // Convert to ISO string with Brazilian timezone\n            const formattedData = {\n                ...data,\n                start_time: startTime.toISOString(),\n                end_time: endTime.toISOString()\n            };\n            console.log('[DATETIME] Formatted data for submission:', formattedData);\n            await submitForm(async ()=>{\n                const appointmentData = {\n                    ...formattedData,\n                    procedures: selectedProcedures,\n                    total_price: getTotalPrice(),\n                    status: 'scheduled'\n                };\n                console.log('[DATETIME] Final appointment data:', appointmentData);\n                await onSubmit(appointmentData);\n                // Reset form\n                reset();\n                setSelectedProcedures([]);\n                setActiveTab('scheduling');\n                onOpenChange(false);\n            });\n        } catch (error) {\n            console.error('[DATETIME] Error submitting form:', error);\n            toast({\n                title: 'Erro',\n                description: 'Erro ao processar os dados. Por favor, tente novamente.',\n                variant: 'destructive'\n            });\n        }\n    };\n    const toggleRecurrenceDay = (day)=>{\n        const currentDays = watchedValues.recurrence_days || [];\n        const newDays = currentDays.includes(day) ? currentDays.filter((d)=>d !== day) : [\n            ...currentDays,\n            day\n        ];\n        setValue('recurrence_days', newDays);\n    };\n    const weekDays = [\n        {\n            value: 1,\n            label: 'D'\n        },\n        {\n            value: 2,\n            label: 'S'\n        },\n        {\n            value: 3,\n            label: 'T'\n        },\n        {\n            value: 4,\n            label: 'Q'\n        },\n        {\n            value: 5,\n            label: 'Q'\n        },\n        {\n            value: 6,\n            label: 'S'\n        },\n        {\n            value: 7,\n            label: 'S'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[800px] max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: \"Agendar Nova Consulta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                            children: \"Preencha os dados para agendar uma nova consulta\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 349,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 347,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit(onFormSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"scheduling\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Agendamento\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"procedures\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Procedimentos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"scheduling\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"patient\",\n                                                            children: \"Paciente *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"patient_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o paciente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 377,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 376,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: patient.id,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                lineNumber: 383,\n                                                                                                columnNumber: 33\n                                                                                            }, void 0),\n                                                                                            patient.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 382,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0)\n                                                                                }, patient.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 381,\n                                                                                    columnNumber: 29\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 375,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.patient_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.patient_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"professional\",\n                                                            children: \"Profissional\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"healthcare_professional_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o profissional\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 408,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: professional.id,\n                                                                                    children: [\n                                                                                        professional.name,\n                                                                                        professional.specialty && \" - \".concat(professional.specialty)\n                                                                                    ]\n                                                                                }, professional.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 414,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 402,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.healthcare_professional_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.healthcare_professional_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"type\",\n                                                            children: \"Tipo de Consulta *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"type\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 441,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"consultation\",\n                                                                                    children: \"Consulta\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 444,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"follow_up\",\n                                                                                    children: \"Retorno\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 27\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.type.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"T\\xedtulo *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"title\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"title\",\n                                                                    ...field\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.title.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"start_time\",\n                                                            children: \"Data/Hora In\\xedcio *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"start_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"start_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.start_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.start_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"end_time\",\n                                                            children: \"Data/Hora Fim *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"end_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"end_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.end_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.end_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 517,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"Descri\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                    name: \"description\",\n                                                    control: control,\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                            id: \"description\",\n                                                            placeholder: \"Descri\\xe7\\xe3o da consulta...\",\n                                                            ...field\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        errors.description.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                name: \"has_recurrence\",\n                                                                control: control,\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n                                                                        id: \"has_recurrence\",\n                                                                        checked: field.value,\n                                                                        onCheckedChange: field.onChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 25\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"has_recurrence\",\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Recorr\\xeancia Personalizada\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                watchedValues.has_recurrence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir a cada:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 571,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_interval\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            type: \"number\",\n                                                                                            min: \"1\",\n                                                                                            ...field,\n                                                                                            onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                            className: \"w-20\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 577,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 573,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_type\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                                            value: field.value,\n                                                                                            onValueChange: field.onChange,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                        lineNumber: 592,\n                                                                                                        columnNumber: 35\n                                                                                                    }, void 0)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 591,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"daily\",\n                                                                                                            children: \"dia(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 595,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"weekly\",\n                                                                                                            children: \"semana(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 596,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"monthly\",\n                                                                                                            children: \"m\\xeas(es)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 597,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 594,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 590,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 586,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 570,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                watchedValues.recurrence_type === 'weekly' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex gap-1\",\n                                                                            children: weekDays.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: (watchedValues.recurrence_days || []).includes(day.value) ? 'default' : 'outline',\n                                                                                    size: \"sm\",\n                                                                                    className: \"w-8 h-8 p-0\",\n                                                                                    onClick: ()=>toggleRecurrenceDay(day.value),\n                                                                                    children: day.label\n                                                                                }, day.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 610,\n                                                                                    columnNumber: 31\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Termina em:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 627,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                    name: \"recurrence_end_type\",\n                                                                    control: control,\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroup, {\n                                                                            value: field.value,\n                                                                            onValueChange: field.onChange,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"never\",\n                                                                                            id: \"never\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 635,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"never\",\n                                                                                            children: \"Nunca\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 636,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 634,\n                                                                                    columnNumber: 25\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"date\",\n                                                                                            id: \"end_date\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 640,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_date\",\n                                                                                            children: \"Em\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 641,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_end_date\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"date\",\n                                                                                                    ...field,\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'date',\n                                                                                                    className: \"w-40\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 646,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 639,\n                                                                                    columnNumber: 29\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"count\",\n                                                                                            id: \"end_count\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 657,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_count\",\n                                                                                            children: \"Ap\\xf3s\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 658,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_count\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"number\",\n                                                                                                    min: \"1\",\n                                                                                                    ...field,\n                                                                                                    onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'count',\n                                                                                                    className: \"w-20\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 663,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 659,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            children: \"ocorr\\xeancias\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 673,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 29\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 632,\n                                                                            columnNumber: 27\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 628,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 626,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 547,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"procedures\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 687,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        placeholder: \"Buscar procedimentos...\",\n                                                        value: searchProcedure,\n                                                        onChange: (e)=>setSearchProcedure(e.target.value),\n                                                        className: \"flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto\",\n                                                children: filteredProcedures.map((procedure)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                        className: \"cursor-pointer hover:bg-accent/50\",\n                                                        onClick: ()=>addProcedure(procedure),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: procedure.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 702,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            procedure.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                                children: procedure.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 704,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-2\",\n                                                                                children: [\n                                                                                    procedure.default_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"secondary\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            \"R$ \",\n                                                                                            procedure.default_price.toFixed(2)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 708,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    procedure.duration_minutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            procedure.duration_minutes,\n                                                                                            \"min\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 713,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 706,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 720,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 719,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 700,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 699,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, procedure.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            selectedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: \"Procedimentos Selecionados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            selectedProcedures.map((proc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 p-3 border rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium\",\n                                                                                children: proc.procedure_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 737,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Qtd:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    value: proc.quantity,\n                                                                                    onChange: (e)=>updateProcedure(index, 'quantity', parseInt(e.target.value) || 1),\n                                                                                    className: \"w-16 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 742,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Pre\\xe7o:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 752,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"0\",\n                                                                                    step: \"0.01\",\n                                                                                    value: proc.unit_price,\n                                                                                    onChange: (e)=>updateProcedure(index, 'unit_price', parseFloat(e.target.value) || 0),\n                                                                                    className: \"w-24 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 753,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 751,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                proc.total_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 763,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>removeProcedure(index),\n                                                                            className: \"h-8 w-8 p-0 text-destructive hover:text-destructive\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 774,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 767,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center pt-3 border-t\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 780,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold\",\n                                                                        children: [\n                                                                            \"R$ \",\n                                                                            getTotalPrice().toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 781,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                        lineNumber: 685,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 791,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    disabled: submitting || loading,\n                                    children: submitting ? 'Agendando...' : 'Agendar Consulta'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 790,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n            lineNumber: 346,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n        lineNumber: 345,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppointmentForm, \"0fK7CMPQBa8UlSZ3bOvox60mpoM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AppointmentForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentForm);\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AppointmentForm.tsx\n"));

/***/ })

});