"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/prontuario/[patientId]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/prontuario/[patientId]/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/dashboard/prontuario/[patientId]/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MedicalRecordPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const patientId = params.patientId;\n    const appointmentId = searchParams.get('appointment_id');\n    const [patient, setPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [appointment, setAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecords, setMedicalRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentRecord, setCurrentRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoSaving, setAutoSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDraft, setIsDraft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicalRecordPage.useEffect\": ()=>{\n            if (patientId) {\n                fetchPatientData();\n                if (appointmentId) {\n                    fetchAppointmentData();\n                    fetchMedicalRecords();\n                }\n            }\n        }\n    }[\"MedicalRecordPage.useEffect\"], [\n        patientId,\n        appointmentId\n    ]);\n    // Auto-save functionality - 2 minutes of inactivity\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicalRecordPage.useEffect\": ()=>{\n            if (currentRecord.trim() && appointmentId && !saving) {\n                const autoSaveTimer = setTimeout({\n                    \"MedicalRecordPage.useEffect.autoSaveTimer\": ()=>{\n                        handleSaveDraft();\n                    }\n                }[\"MedicalRecordPage.useEffect.autoSaveTimer\"], 120000); // Auto-save after 2 minutes of inactivity\n                return ({\n                    \"MedicalRecordPage.useEffect\": ()=>clearTimeout(autoSaveTimer)\n                })[\"MedicalRecordPage.useEffect\"];\n            }\n        }\n    }[\"MedicalRecordPage.useEffect\"], [\n        currentRecord,\n        appointmentId,\n        saving\n    ]);\n    const fetchPatientData = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch patient data');\n            const result = await response.json();\n            setPatient(result.data || result);\n        } catch (error) {\n            console.error('Error fetching patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados do paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchAppointmentData = async ()=>{\n        if (!appointmentId) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch appointment data');\n            const result = await response.json();\n            setAppointment(result.data || result);\n        } catch (error) {\n            console.error('Error fetching appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchMedicalRecords = async ()=>{\n        if (!appointmentId) return;\n        try {\n            setLoading(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/medical-records?appointment_id=\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch medical records');\n            const result = await response.json();\n            const records = result.data || result;\n            setMedicalRecords(Array.isArray(records) ? records : []);\n            // Check if there's a draft record\n            const draftRecord = records.find((record)=>record.is_draft);\n            if (draftRecord) {\n                setCurrentRecord(draftRecord.notes);\n                setIsDraft(true);\n            }\n        } catch (error) {\n            console.error('Error fetching medical records:', error);\n            setMedicalRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveDraft = async ()=>{\n        if (!currentRecord.trim() || !appointmentId || saving) return;\n        try {\n            setAutoSaving(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId,\n                    patient_id: patientId,\n                    notes: currentRecord.trim(),\n                    is_draft: true\n                })\n            });\n            if (!response.ok) throw new Error('Failed to save draft');\n            setLastSaved(new Date());\n            setIsDraft(true);\n            toast({\n                title: \"Rascunho salvo\",\n                description: \"Suas alterações foram salvas automaticamente.\"\n            });\n        } catch (error) {\n            console.error('Error saving draft:', error);\n        } finally{\n            setAutoSaving(false);\n        }\n    };\n    const handleCompleteConsultation = async ()=>{\n        if (!currentRecord.trim() || !appointmentId) {\n            toast({\n                title: \"Erro\",\n                description: \"Por favor, adicione suas observações antes de finalizar.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            // Save final medical record\n            const recordResponse = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId,\n                    patient_id: patientId,\n                    notes: currentRecord.trim(),\n                    is_draft: false\n                })\n            });\n            if (!recordResponse.ok) throw new Error('Failed to save medical record');\n            // Update appointment status to completed\n            const appointmentResponse = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'completed'\n                })\n            });\n            if (!appointmentResponse.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Atendimento finalizado com sucesso.\"\n            });\n            // Navigate back to agenda\n            router.push('/dashboard/agenda');\n        } catch (error) {\n            console.error('Error completing consultation:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao finalizar atendimento.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const calculateAge = (birthDate)=>{\n        if (!birthDate) return 'Não informado';\n        const birth = new Date(birthDate);\n        const today = new Date();\n        let age = today.getFullYear() - birth.getFullYear();\n        const monthDiff = today.getMonth() - birth.getMonth();\n        if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birth.getDate()) {\n            age--;\n        }\n        return \"\".concat(age, \" anos\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-muted-foreground\",\n                        children: \"Carregando prontu\\xe1rio...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 272,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Voltar\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Prontu\\xe1rio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: (patient === null || patient === void 0 ? void 0 : patient.name) && \"Hist\\xf3rico m\\xe9dico de \".concat(patient.name)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, undefined),\n            patient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Informa\\xe7\\xf5es do Paciente\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Nome\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.email || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Telefone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.phone || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Data de Nascimento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.birth_date ? (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateBR)(patient.birth_date) : 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Idade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: calculateAge(patient.birth_date)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"CPF\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.cpf || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, undefined),\n            appointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Informa\\xe7\\xf5es da Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"T\\xedtulo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateBR)(appointment.start_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Hor\\xe1rio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.start_time),\n                                                \" - \",\n                                                (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.end_time)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: appointment.status === 'completed' ? 'secondary' : appointment.status === 'in_progress' ? 'default' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                            children: [\n                                                appointment.status === 'scheduled' && 'Agendado',\n                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                appointment.status === 'completed' && 'Concluído',\n                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, undefined),\n                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profissional\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.healthcare_professional_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 17\n                                }, undefined),\n                                appointment.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 344,\n                columnNumber: 9\n            }, undefined),\n            appointmentId && (appointment === null || appointment === void 0 ? void 0 : appointment.status) !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Nova Anota\\xe7\\xe3o\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Adicione uma nova entrada ao prontu\\xe1rio do paciente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                    children: [\n                                        autoSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-3 w-3 border-b border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        lastSaved && !autoSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-3 w-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Salvo \\xe0s \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(lastSaved.toISOString())\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isDraft && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: \"Rascunho\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        htmlFor: \"medical-notes\",\n                                        children: \"Observa\\xe7\\xf5es M\\xe9dicas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        id: \"medical-notes\",\n                                        placeholder: \"Digite suas observa\\xe7\\xf5es sobre a consulta, diagn\\xf3stico, tratamento recomendado, etc...\",\n                                        value: currentRecord,\n                                        onChange: (e)=>setCurrentRecord(e.target.value),\n                                        rows: 8,\n                                        className: \"min-h-[200px]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"As anota\\xe7\\xf5es s\\xe3o automaticamente criptografadas e salvas como rascunho a cada 5 segundos.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleSaveDraft,\n                                        disabled: !currentRecord.trim() || autoSaving,\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Salvar Rascunho\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleCompleteConsultation,\n                                        disabled: !currentRecord.trim() || saving,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            saving ? 'Finalizando...' : 'Finalizar Atendimento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Hist\\xf3rico de Anota\\xe7\\xf5es\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: appointmentId ? 'Registros médicos desta consulta' : 'Histórico completo de registros médicos do paciente'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: medicalRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Nenhum registro m\\xe9dico encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: medicalRecords.filter((record)=>!record.is_draft) // Only show finalized records\n                            .map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-l-4 border-primary pl-4 py-3 bg-muted/30 rounded-r-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateTimeBR)(record.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: record.created_by_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                record.updated_at && record.updated_at !== record.created_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Editado em \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateTimeBR)(record.updated_at)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-sm max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                                children: record.notes\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, record.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MedicalRecordPage, \"Gd55zBCyJLy2D5fiJMb/bjlVCS4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = MedicalRecordPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MedicalRecordPage);\nvar _c;\n$RefreshReg$(_c, \"MedicalRecordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/prontuario/[patientId]/page.tsx\n"));

/***/ })

});