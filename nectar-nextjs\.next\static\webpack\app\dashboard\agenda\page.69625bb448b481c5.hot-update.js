"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Confirmation dialog states\n    const [confirmDialog, setConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        title: '',\n        description: '',\n        onConfirm: {\n            \"AgendaPage.useState\": ()=>{}\n        }[\"AgendaPage.useState\"],\n        variant: 'default'\n    });\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter)();\n    // Fetch initial data that doesn't depend on selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], []);\n    // Fetch date-specific appointments when selected date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_13__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            // FullCalendar provides dates in local timezone, use them directly\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.start);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(selectInfo.end);\n        } else if (selectedDate) {\n            // Create appointment for selected date at 9:00 AM\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(startTime);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.toLocalISOString)(endTime);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // For in-progress and completed appointments, navigate to medical record screen\n        if (appointment.status === 'in_progress' || appointment.status === 'completed') {\n            console.log('Navigating to medical record for appointment:', appointment.id);\n            router.push(\"/dashboard/prontuario/\".concat(appointment.patient_id, \"?appointment_id=\").concat(appointment.id));\n            return;\n        }\n        // For other statuses, open edit form with appointment data\n        console.log('Appointment clicked for editing:', appointment);\n        const editData = {\n            id: appointment.id,\n            title: appointment.title,\n            description: appointment.description,\n            patient_id: appointment.patient_id,\n            healthcare_professional_id: appointment.healthcare_professional_id,\n            start_time: appointment.start_time,\n            end_time: appointment.end_time,\n            type: appointment.type,\n            status: appointment.status,\n            price: appointment.price\n        };\n        setAppointmentFormData(editData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const isEditing = data.id;\n            const url = isEditing ? \"/api/appointments/\".concat(data.id) : '/api/appointments';\n            const method = isEditing ? 'PUT' : 'POST';\n            // Remove id from data for API call\n            const { id, ...submitData } = data;\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(submitData)\n            });\n            if (!response.ok) throw new Error(\"Failed to \".concat(isEditing ? 'update' : 'create', \" appointment\"));\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error(\"Error \".concat(data.id ? 'updating' : 'creating', \" appointment:\"), error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancelAppointment = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        setConfirmDialog({\n            open: true,\n            title: 'Cancelar Consulta',\n            description: \"Tem certeza que deseja cancelar a consulta de \".concat(appointment.patient_name, \"?\"),\n            variant: 'destructive',\n            onConfirm: ()=>performCancelAppointment(appointment.id)\n        });\n    };\n    const performCancelAppointment = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'cancelled'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to cancel appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta cancelada com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error canceling appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao cancelar consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStartConsultation = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        setConfirmDialog({\n            open: true,\n            title: 'Iniciar Atendimento',\n            description: \"Deseja iniciar o atendimento para \".concat(appointment.patient_name, \"?\"),\n            variant: 'default',\n            onConfirm: ()=>performStartConsultation(appointment)\n        });\n    };\n    const performStartConsultation = async (appointment)=>{\n        try {\n            // Update appointment status to in_progress\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointment.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'in_progress'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to start consultation');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Atendimento iniciado com sucesso.\"\n            });\n            // Navigate to medical record screen\n            router.push(\"/dashboard/prontuario/\".concat(appointment.patient_id, \"?appointment_id=\").concat(appointment.id));\n        } catch (error) {\n            console.error('Error starting consultation:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao iniciar atendimento.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 454,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 484,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 510,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_17__.ptBR,\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    // appointmentCounts={appointmentCounts}\n                                                    // clinicSettings={clinicSettings || undefined}\n                                                    // appointments={allAppointments}\n                                                    className: \"rounded-md border-0 shadow-none w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatDateBR)(selectedDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 535,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 548,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border transition-colors space-y-3 sm:space-y-0 \".concat(appointment.status === 'cancelled' ? 'bg-red-50 border-red-200 hover:bg-red-100' : appointment.status === 'in_progress' ? 'bg-blue-50 border-blue-200 hover:bg-blue-100 cursor-pointer' : appointment.status === 'completed' ? 'bg-green-50 border-green-200 hover:bg-green-100 cursor-pointer' : 'bg-card/50 hover:bg-card cursor-pointer'),\n                                                            onClick: appointment.status !== 'cancelled' ? ()=>handleAppointmentClick(appointment) : undefined,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-1\",\n                                                                                    children: [\n                                                                                        appointment.status === 'in_progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                            className: \"h-3 w-3 text-blue-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 576,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        appointment.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 579,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.start_time)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 581,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 574,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.end_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 585,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 590,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 591,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 595,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                (appointment.status === 'in_progress' || appointment.status === 'completed') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                                                    children: appointment.status === 'in_progress' ? 'Clique para continuar o atendimento' : 'Clique para ver o prontuário'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : appointment.status === 'in_progress' ? 'default' : 'outline',\n                                                                                    className: \"text-xs\",\n                                                                                    children: [\n                                                                                        appointment.status === 'scheduled' && 'Agendado',\n                                                                                        appointment.status === 'confirmed' && 'Confirmado',\n                                                                                        appointment.status === 'completed' && 'Concluído',\n                                                                                        appointment.status === 'cancelled' && 'Cancelado',\n                                                                                        appointment.status === 'in_progress' && 'Em Andamento'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 611,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium whitespace-nowrap\",\n                                                                                    children: [\n                                                                                        \"R$ \",\n                                                                                        appointment.price.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 628,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 610,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.status !== 'cancelled' && appointment.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-1\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs\",\n                                                                                    onClick: (e)=>handleStartConsultation(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 643,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"Iniciar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 637,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                                    onClick: (e)=>handleCancelAppointment(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 653,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"Cancelar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 647,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 635,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            appointments: allAppointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 670,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 453,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"4/yEZimNaegVRrl3D+KfCx0g7HA=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_12__.useRouter\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2FnZW5kYS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0M7QUFDNkM7QUFDakQ7QUFDRjtBQUNrQztBQUNxQztBQUV4RTtBQUNYO0FBQ0s7QUFDeUc7QUFDcEY7QUFDQztBQUNGO0FBRWY7QUErQzVDLE1BQU1rQyxhQUFhOztJQUNqQixNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHbkMsK0NBQVFBLENBQU8sSUFBSW9DO0lBQzNELE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUd0QywrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUNsRSxNQUFNLENBQUN1QyxpQkFBaUJDLG1CQUFtQixHQUFHeEMsK0NBQVFBLENBQWdCLEVBQUU7SUFDeEUsTUFBTSxDQUFDeUMsVUFBVUMsWUFBWSxHQUFHMUMsK0NBQVFBLENBQVksRUFBRTtJQUN0RCxNQUFNLENBQUMyQyx5QkFBeUJDLDJCQUEyQixHQUFHNUMsK0NBQVFBLENBQTJCLEVBQUU7SUFDbkcsTUFBTSxDQUFDNkMsWUFBWUMsY0FBYyxHQUFHOUMsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUMrQyxnQkFBZ0JDLGtCQUFrQixHQUFHaEQsK0NBQVFBLENBQXdCO0lBQzVFLE1BQU0sQ0FBQ2lELFNBQVNDLFdBQVcsR0FBR2xELCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21ELHFCQUFxQkMsdUJBQXVCLEdBQUdwRCwrQ0FBUUEsQ0FBQztJQUMvRCxNQUFNLENBQUNxRCxhQUFhQyxlQUFlLEdBQUd0RCwrQ0FBUUEsQ0FBOEI7SUFDNUUsTUFBTSxDQUFDdUQscUJBQXFCQyx1QkFBdUIsR0FBR3hELCtDQUFRQSxDQUFNO0lBRXBFLDZCQUE2QjtJQUM3QixNQUFNLENBQUN5RCxlQUFlQyxpQkFBaUIsR0FBRzFELCtDQUFRQSxDQU0vQztRQUNEMkQsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsU0FBUzttQ0FBRSxLQUFPOztRQUNsQkMsU0FBUztJQUNYO0lBQ0EsTUFBTSxFQUFFQyxLQUFLLEVBQUUsR0FBR3pDLDBEQUFRQTtJQUMxQixNQUFNMEMsU0FBU2pDLDJEQUFTQTtJQUV4QiwwREFBMEQ7SUFDMUQvQixnREFBU0E7Z0NBQUM7WUFDUmlFO1lBQ0FDO1lBQ0FDO1lBQ0FDO1lBQ0FDO1FBQ0Y7K0JBQUcsRUFBRTtJQUVMLDhEQUE4RDtJQUM5RHJFLGdEQUFTQTtnQ0FBQztZQUNSc0U7UUFDRjsrQkFBRztRQUFDckM7S0FBYTtJQUVqQixNQUFNcUMsb0JBQW9CO1FBQ3hCLElBQUk7WUFDRnJCLFdBQVc7WUFDWCxNQUFNc0IsVUFBVWhELCtFQUFNQSxDQUFDVSxjQUFjO1lBQ3JDLE1BQU11QyxXQUFXLE1BQU01Qyx5RUFBd0JBLENBQUMsMEJBQWtDLE9BQVIyQztZQUMxRSxJQUFJLENBQUNDLFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTUMsU0FBUyxNQUFNSCxTQUFTSSxJQUFJO1lBQ2xDLE1BQU1DLE9BQU9GLE9BQU9FLElBQUksSUFBSUY7WUFDNUJ0QyxnQkFBZ0J5QyxNQUFNQyxPQUFPLENBQUNGLFFBQVFBLE9BQU8sRUFBRTtRQUNqRCxFQUFFLE9BQU9HLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMzQyxnQkFBZ0IsRUFBRTtZQUNsQjBCLE1BQU07Z0JBQ0pKLE9BQU87Z0JBQ1BDLGFBQWFvQixpQkFBaUJOLFFBQVFNLE1BQU1FLE9BQU8sR0FBRztnQkFDdERwQixTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1JiLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWlCLGdCQUFnQjtRQUNwQixJQUFJO1lBQ0YsTUFBTU0sV0FBVyxNQUFNNUMseUVBQXdCQSxDQUFDO1lBQ2hELElBQUksQ0FBQzRDLFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTUMsU0FBUyxNQUFNSCxTQUFTSSxJQUFJO1lBQ2xDSyxRQUFRRSxHQUFHLENBQUMsMEJBQTBCUjtZQUN0QyxNQUFNRSxPQUFPRixPQUFPRSxJQUFJLElBQUlGO1lBQzVCbEMsWUFBWXFDLE1BQU1DLE9BQU8sQ0FBQ0YsUUFBUUEsT0FBTyxFQUFFO1FBQzdDLEVBQUUsT0FBT0csT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQ3ZDLFlBQVksRUFBRTtZQUNkc0IsTUFBTTtnQkFDSkosT0FBTztnQkFDUEMsYUFBYW9CLGlCQUFpQk4sUUFBUU0sTUFBTUUsT0FBTyxHQUFHO2dCQUN0RHBCLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNSywrQkFBK0I7UUFDbkMsSUFBSTtZQUNGLE1BQU1LLFdBQVcsTUFBTTVDLHlFQUF3QkEsQ0FBQztZQUNoRCxJQUFJLENBQUM0QyxTQUFTQyxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBQ2xDLE1BQU1DLFNBQVMsTUFBTUgsU0FBU0ksSUFBSTtZQUNsQyxNQUFNQyxPQUFPRixPQUFPRSxJQUFJLElBQUlGO1lBQzVCaEMsMkJBQTJCbUMsTUFBTUMsT0FBTyxDQUFDRixRQUFRQSxPQUFPLEVBQUU7UUFDNUQsRUFBRSxPQUFPRyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0Q0FBNENBO1lBQzFEckMsMkJBQTJCLEVBQUU7UUFDL0I7SUFDRjtJQUVBLE1BQU15QixrQkFBa0I7UUFDdEIsSUFBSTtZQUNGLE1BQU1JLFdBQVcsTUFBTTVDLHlFQUF3QkEsQ0FBQztZQUNoRCxJQUFJLENBQUM0QyxTQUFTQyxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBQ2xDLE1BQU1DLFNBQVMsTUFBTUgsU0FBU0ksSUFBSTtZQUNsQyxNQUFNQyxPQUFPRixPQUFPRSxJQUFJLElBQUlGO1lBQzVCOUIsY0FBY2lDLE1BQU1DLE9BQU8sQ0FBQ0YsUUFBUUEsT0FBTyxFQUFFO1FBQy9DLEVBQUUsT0FBT0csT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtZQUM1Q25DLGNBQWMsRUFBRTtRQUNsQjtJQUNGO0lBRUEsTUFBTW9CLHVCQUF1QjtRQUMzQixJQUFJO1lBQ0YsTUFBTU8sV0FBVyxNQUFNNUMseUVBQXdCQSxDQUFDO1lBQ2hELElBQUksQ0FBQzRDLFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTUMsU0FBUyxNQUFNSCxTQUFTSSxJQUFJO1lBQ2xDLE1BQU1DLE9BQU9GLE9BQU9FLElBQUksSUFBSUY7WUFDNUJwQyxtQkFBbUJ1QyxNQUFNQyxPQUFPLENBQUNGLFFBQVFBLE9BQU8sRUFBRTtRQUNwRCxFQUFFLE9BQU9HLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLG9DQUFvQ0E7WUFDbER6QyxtQkFBbUIsRUFBRTtRQUN2QjtJQUNGO0lBRUEsTUFBTThCLHNCQUFzQjtRQUMxQixJQUFJO1lBQ0YsTUFBTUcsV0FBVyxNQUFNNUMseUVBQXdCQSxDQUFDO1lBQ2hELElBQUksQ0FBQzRDLFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTUMsU0FBUyxNQUFNSCxTQUFTSSxJQUFJO1lBQ2xDLE1BQU1DLE9BQU9GLE9BQU9FLElBQUksSUFBSUY7WUFDNUI1QixrQkFBa0I4QjtRQUNwQixFQUFFLE9BQU9HLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLG1DQUFtQ0E7WUFDakQsc0NBQXNDO1lBQ3RDakMsa0JBQWtCO2dCQUNoQnFDLHFCQUFxQjtnQkFDckJDLG1CQUFtQjtnQkFDbkJDLGNBQWM7b0JBQUM7b0JBQUc7b0JBQUc7b0JBQUc7b0JBQUc7aUJBQUU7Z0JBQzdCQyw4QkFBOEI7Z0JBQzlCQyw0QkFBNEI7WUFDOUI7UUFDRjtJQUNGO0lBRUEsTUFBTUMsMEJBQTBCLENBQUNDO1FBQy9CLE1BQU1DLGNBQW1CLENBQUM7UUFFMUIsSUFBSUQsWUFBWTtZQUNkLG1FQUFtRTtZQUNuRUMsWUFBWUMsVUFBVSxHQUFHakUsaUVBQWdCQSxDQUFDK0QsV0FBV0csS0FBSztZQUMxREYsWUFBWUcsUUFBUSxHQUFHbkUsaUVBQWdCQSxDQUFDK0QsV0FBV0ssR0FBRztRQUN4RCxPQUFPLElBQUk5RCxjQUFjO1lBQ3ZCLGtEQUFrRDtZQUNsRCxNQUFNK0QsWUFBWSxJQUFJN0QsS0FBS0Y7WUFDM0IrRCxVQUFVQyxRQUFRLENBQUMsR0FBRyxHQUFHLEdBQUc7WUFDNUIsTUFBTUMsVUFBVSxJQUFJL0QsS0FBSzZEO1lBQ3pCRSxRQUFRQyxVQUFVLENBQUNELFFBQVFFLFVBQVUsS0FBSztZQUUxQ1QsWUFBWUMsVUFBVSxHQUFHakUsaUVBQWdCQSxDQUFDcUU7WUFDMUNMLFlBQVlHLFFBQVEsR0FBR25FLGlFQUFnQkEsQ0FBQ3VFO1FBQzFDO1FBRUEzQyx1QkFBdUJvQztRQUN2QnhDLHVCQUF1QjtJQUN6QjtJQUVBLE1BQU1rRCx5QkFBeUIsQ0FBQ0M7UUFDOUIsZ0ZBQWdGO1FBQ2hGLElBQUlBLFlBQVlDLE1BQU0sS0FBSyxpQkFBaUJELFlBQVlDLE1BQU0sS0FBSyxhQUFhO1lBQzlFdEIsUUFBUUUsR0FBRyxDQUFDLGlEQUFpRG1CLFlBQVlFLEVBQUU7WUFDM0V4QyxPQUFPeUMsSUFBSSxDQUFDLHlCQUFrRUgsT0FBekNBLFlBQVlJLFVBQVUsRUFBQyxvQkFBaUMsT0FBZkosWUFBWUUsRUFBRTtZQUM1RjtRQUNGO1FBRUEsMkRBQTJEO1FBQzNEdkIsUUFBUUUsR0FBRyxDQUFDLG9DQUFvQ21CO1FBRWhELE1BQU1LLFdBQVc7WUFDZkgsSUFBSUYsWUFBWUUsRUFBRTtZQUNsQjdDLE9BQU8yQyxZQUFZM0MsS0FBSztZQUN4QkMsYUFBYTBDLFlBQVkxQyxXQUFXO1lBQ3BDOEMsWUFBWUosWUFBWUksVUFBVTtZQUNsQ0UsNEJBQTRCTixZQUFZTSwwQkFBMEI7WUFDbEVoQixZQUFZVSxZQUFZVixVQUFVO1lBQ2xDRSxVQUFVUSxZQUFZUixRQUFRO1lBQzlCZSxNQUFNUCxZQUFZTyxJQUFJO1lBQ3RCTixRQUFRRCxZQUFZQyxNQUFNO1lBQzFCTyxPQUFPUixZQUFZUSxLQUFLO1FBQzFCO1FBRUF2RCx1QkFBdUJvRDtRQUN2QnhELHVCQUF1QjtJQUN6QjtJQUVBLE1BQU00RCwwQkFBMEIsT0FBT0MsZUFBdUJDLFVBQWdCQztRQUM1RSxJQUFJO1lBQ0YsTUFBTTFDLFdBQVcsTUFBTTVDLHlFQUF3QkEsQ0FBQyxxQkFBbUMsT0FBZG9GLGdCQUFpQjtnQkFDcEZHLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkIzQixZQUFZcUIsU0FBU08sV0FBVztvQkFDaEMxQixVQUFVb0IsT0FBT00sV0FBVztnQkFDOUI7WUFDRjtZQUVBLElBQUksQ0FBQ2hELFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFFbEMsNkRBQTZEO1lBQzdESjtZQUNBTDtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSxNQUFNeUMsMEJBQTBCLE9BQU81QztRQUNyQyxJQUFJO1lBQ0YsTUFBTTZDLFlBQVk3QyxLQUFLMkIsRUFBRTtZQUN6QixNQUFNbUIsTUFBTUQsWUFBWSxxQkFBNkIsT0FBUjdDLEtBQUsyQixFQUFFLElBQUs7WUFDekQsTUFBTVcsU0FBU08sWUFBWSxRQUFRO1lBRW5DLG1DQUFtQztZQUNuQyxNQUFNLEVBQUVsQixFQUFFLEVBQUUsR0FBR29CLFlBQVksR0FBRy9DO1lBRTlCLE1BQU1MLFdBQVcsTUFBTTVDLHlFQUF3QkEsQ0FBQytGLEtBQUs7Z0JBQ25EUjtnQkFDQUMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDSztZQUN2QjtZQUVBLElBQUksQ0FBQ3BELFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU0sYUFBNkMsT0FBaENnRCxZQUFZLFdBQVcsVUFBUztZQUUvRSw2REFBNkQ7WUFDN0RwRDtZQUNBTDtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsU0FBMkMsT0FBbENILEtBQUsyQixFQUFFLEdBQUcsYUFBYSxZQUFXLGtCQUFnQnhCO1lBQ3pFLE1BQU1BO1FBQ1I7SUFDRjtJQUVBLHVEQUF1RDtJQUN2RCxNQUFNNkMsb0JBQW9CL0gsb0RBQWE7aURBQUM7WUFDdEMsTUFBTWlJLFNBQWlDLENBQUM7WUFDeEMzRixhQUFhNEYsT0FBTzt5REFBQzFCLENBQUFBO29CQUNuQixNQUFNMkIsT0FBTyxJQUFJOUYsS0FBS21FLFlBQVlWLFVBQVUsRUFBRTRCLFdBQVcsR0FBR1UsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO29CQUN6RUgsTUFBTSxDQUFDRSxLQUFLLEdBQUcsQ0FBQ0YsTUFBTSxDQUFDRSxLQUFLLElBQUksS0FBSztnQkFDdkM7O1lBQ0EsT0FBT0Y7UUFDVDtnREFBRztRQUFDM0Y7S0FBYTtJQUVqQixNQUFNK0YsMEJBQTBCLE9BQU9uQixlQUF1QlQ7UUFDNUQsSUFBSTtZQUNGLE1BQU0vQixXQUFXLE1BQU01Qyx5RUFBd0JBLENBQUMscUJBQW1DLE9BQWRvRixnQkFBaUI7Z0JBQ3BGRyxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVoQjtnQkFBTztZQUNoQztZQUVBLElBQUksQ0FBQy9CLFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFFbENYLE1BQU07Z0JBQ0pKLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtZQUVBVTtZQUNBTDtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRGpCLE1BQU07Z0JBQ0pKLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JFLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNc0UsMEJBQTBCLE9BQU9wQjtRQUNyQyxJQUFJLENBQUNxQixRQUFRLGtEQUFrRDtRQUUvRCxJQUFJO1lBQ0YsTUFBTTdELFdBQVcsTUFBTTVDLHlFQUF3QkEsQ0FBQyxxQkFBbUMsT0FBZG9GLGdCQUFpQjtnQkFDcEZHLFFBQVE7WUFDVjtZQUVBLElBQUksQ0FBQzNDLFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFFbENYLE1BQU07Z0JBQ0pKLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtZQUVBVTtZQUNBTDtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3Q2pCLE1BQU07Z0JBQ0pKLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JFLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNd0UsMEJBQTBCLE9BQU9oQyxhQUEwQmlDO1FBQy9EQSxNQUFNQyxlQUFlLElBQUksNENBQTRDO1FBRXJFL0UsaUJBQWlCO1lBQ2ZDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhLGlEQUEwRSxPQUF6QjBDLFlBQVltQyxZQUFZLEVBQUM7WUFDdkYzRSxTQUFTO1lBQ1RELFdBQVcsSUFBTTZFLHlCQUF5QnBDLFlBQVlFLEVBQUU7UUFDMUQ7SUFDRjtJQUVBLE1BQU1rQywyQkFBMkIsT0FBTzFCO1FBQ3RDLElBQUk7WUFDRixNQUFNeEMsV0FBVyxNQUFNNUMseUVBQXdCQSxDQUFDLHFCQUFtQyxPQUFkb0YsZ0JBQWlCO2dCQUNwRkcsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFaEIsUUFBUTtnQkFBWTtZQUM3QztZQUVBLElBQUksQ0FBQy9CLFNBQVNDLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFFbENYLE1BQU07Z0JBQ0pKLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtZQUVBVTtZQUNBTDtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsZ0NBQWdDQTtZQUM5Q2pCLE1BQU07Z0JBQ0pKLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JFLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNNkUsMEJBQTBCLE9BQU9yQyxhQUEwQmlDO1FBQy9EQSxNQUFNQyxlQUFlLElBQUksNENBQTRDO1FBRXJFL0UsaUJBQWlCO1lBQ2ZDLE1BQU07WUFDTkMsT0FBTztZQUNQQyxhQUFhLHFDQUE4RCxPQUF6QjBDLFlBQVltQyxZQUFZLEVBQUM7WUFDM0UzRSxTQUFTO1lBQ1RELFdBQVcsSUFBTStFLHlCQUF5QnRDO1FBQzVDO0lBQ0Y7SUFFQSxNQUFNc0MsMkJBQTJCLE9BQU90QztRQUV0QyxJQUFJO1lBQ0YsMkNBQTJDO1lBQzNDLE1BQU05QixXQUFXLE1BQU01Qyx5RUFBd0JBLENBQUMscUJBQW9DLE9BQWYwRSxZQUFZRSxFQUFFLEdBQUk7Z0JBQ3JGVyxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQUVoQixRQUFRO2dCQUFjO1lBQy9DO1lBRUEsSUFBSSxDQUFDL0IsU0FBU0MsRUFBRSxFQUFFLE1BQU0sSUFBSUMsTUFBTTtZQUVsQ1gsTUFBTTtnQkFDSkosT0FBTztnQkFDUEMsYUFBYTtZQUNmO1lBRUEsb0NBQW9DO1lBQ3BDSSxPQUFPeUMsSUFBSSxDQUFDLHlCQUFrRUgsT0FBekNBLFlBQVlJLFVBQVUsRUFBQyxvQkFBaUMsT0FBZkosWUFBWUUsRUFBRTtRQUM5RixFQUFFLE9BQU94QixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDakIsTUFBTTtnQkFDSkosT0FBTztnQkFDUEMsYUFBYTtnQkFDYkUsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDK0U7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7OzBDQUNDLDhEQUFDRTtnQ0FBR0QsV0FBVTswQ0FBcUM7Ozs7OzswQ0FDbkQsOERBQUNFO2dDQUFFRixXQUFVOzBDQUF3Qjs7Ozs7Ozs7Ozs7O2tDQUd2Qyw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN2SSx5REFBTUE7NEJBQUMwSSxTQUFTLElBQU14RDs7OENBQ3JCLDhEQUFDMUUsMElBQUlBO29DQUFDK0gsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU12Qyw4REFBQ3JJLHFEQUFJQTtnQkFDSHlJLE9BQU85RjtnQkFDUCtGLGVBQWUsQ0FBQ0Q7b0JBQ2QsZ0VBQWdFO29CQUNoRSxJQUFJbEcsU0FBUzt3QkFDWGUsTUFBTTs0QkFDSkosT0FBTzs0QkFDUEMsYUFBYTs0QkFDYkUsU0FBUzt3QkFDWDt3QkFDQTtvQkFDRjtvQkFDQVQsZUFBZTZGO2dCQUNqQjtnQkFDQUosV0FBVTs7a0NBRVYsOERBQUNuSSx5REFBUUE7d0JBQUNtSSxXQUFVOzswQ0FDbEIsOERBQUNsSSw0REFBV0E7Z0NBQ1ZzSSxPQUFNO2dDQUNOSixXQUFXLDhDQUE2RixPQUEvQzlGLFVBQVUsa0NBQWtDO2dDQUNyR29HLFVBQVVwRzs7a0RBRVYsOERBQUNuQywwSUFBWUE7d0NBQUNpSSxXQUFVOzs7Ozs7a0RBQ3hCLDhEQUFDTzt3Q0FBS1AsV0FBVTtrREFBbUI7Ozs7OztrREFDbkMsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUFZOzs7Ozs7b0NBQzNCOUYseUJBQVcsOERBQUM2Rjt3Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUU3Qiw4REFBQ2xJLDREQUFXQTtnQ0FDVnNJLE9BQU07Z0NBQ05KLFdBQVcsOENBQTZGLE9BQS9DOUYsVUFBVSxrQ0FBa0M7Z0NBQ3JHb0csVUFBVXBHOztrREFFViw4REFBQy9CLDBJQUFPQTt3Q0FBQzZILFdBQVU7Ozs7OztrREFDbkIsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUFtQjs7Ozs7O2tEQUNuQyw4REFBQ087d0NBQUtQLFdBQVU7a0RBQVk7Ozs7OztvQ0FDM0I5Rix5QkFBVyw4REFBQzZGO3dDQUFJQyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSS9CLDhEQUFDcEksNERBQVdBO3dCQUFDd0ksT0FBTTt3QkFBV0osV0FBVTtrQ0FDdEMsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQzVJLHFEQUFJQTtvQ0FBQzRJLFdBQVU7O3NEQUNkLDhEQUFDekksMkRBQVVBO3NEQUNULDRFQUFDQywwREFBU0E7Z0RBQUN3SSxXQUFVOztrRUFDbkIsOERBQUNqSSwwSUFBWUE7d0RBQUNpSSxXQUFVOzs7Ozs7b0RBQThCOzs7Ozs7Ozs7Ozs7c0RBSTFELDhEQUFDM0ksNERBQVdBO3NEQUNWLDRFQUFDMEk7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUM3SSw2REFBUUE7b0RBQ1BxSixNQUFLO29EQUNMQyxRQUFRL0gsa0RBQUlBO29EQUNaZ0ksVUFBVXZIO29EQUNWd0gsVUFBVSxDQUFDeEIsT0FBU0EsUUFBUS9GLGdCQUFnQitGO29EQUM1Qyx3Q0FBd0M7b0RBQ3hDLCtDQUErQztvREFDL0MsaUNBQWlDO29EQUNqQ2EsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPbEIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDNUkscURBQUlBOzswREFDSCw4REFBQ0csMkRBQVVBOztrRUFDVCw4REFBQ0MsMERBQVNBO3dEQUFDd0ksV0FBVTs7MEVBQ25CLDhEQUFDaEksMElBQUtBO2dFQUFDZ0ksV0FBVTs7Ozs7OzBFQUNqQiw4REFBQ087Z0VBQUtQLFdBQVU7O29FQUFtQjtvRUFBYXJILDZEQUFZQSxDQUFDUTs7Ozs7OzswRUFDN0QsOERBQUNvSDtnRUFBS1AsV0FBVTswRUFBWTs7Ozs7Ozs7Ozs7O2tFQUU5Qiw4REFBQzFJLGdFQUFlQTs7NERBQ2JnQyxhQUFhc0gsTUFBTTs0REFBQzs7Ozs7Ozs7Ozs7OzswREFHekIsOERBQUN2Siw0REFBV0E7MERBQ1Q2Qyx3QkFDQyw4REFBQzZGO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2hJLDBJQUFLQTs0REFBQ2dJLFdBQVU7Ozs7OztzRUFDakIsOERBQUNFO3NFQUFFOzs7Ozs7Ozs7OztnRUFFSDVHLGFBQWFzSCxNQUFNLEtBQUssa0JBQzFCLDhEQUFDYjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUM5SCwwSUFBS0E7NERBQUM4SCxXQUFVOzs7Ozs7c0VBQ2pCLDhEQUFDRTtzRUFBRTs7Ozs7Ozs7Ozs7OEVBR0wsOERBQUNIO29EQUFJQyxXQUFVOzhEQUNaMUcsYUFBYXVILEdBQUcsQ0FBQyxDQUFDckQsNEJBQ2pCLDhEQUFDdUM7NERBRUNDLFdBQVcsK0hBUVYsT0FQQ3hDLFlBQVlDLE1BQU0sS0FBSyxjQUNuQiw4Q0FDQUQsWUFBWUMsTUFBTSxLQUFLLGdCQUN2QixnRUFDQUQsWUFBWUMsTUFBTSxLQUFLLGNBQ3ZCLG1FQUNBOzREQUVOMEMsU0FBUzNDLFlBQVlDLE1BQU0sS0FBSyxjQUFjLElBQU1GLHVCQUF1QkMsZUFBZXNEOzs4RUFFMUYsOERBQUNmO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQ0Q7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDRDtvRkFBSUMsV0FBVTs7d0ZBQ1p4QyxZQUFZQyxNQUFNLEtBQUssK0JBQ3RCLDhEQUFDbkYsMElBQVFBOzRGQUFDMEgsV0FBVTs7Ozs7O3dGQUVyQnhDLFlBQVlDLE1BQU0sS0FBSyw2QkFDdEIsOERBQUNsRiwwSUFBV0E7NEZBQUN5SCxXQUFVOzs7Ozs7c0dBRXpCLDhEQUFDTzs0RkFBS1AsV0FBVTtzR0FDYnBILDZEQUFZQSxDQUFDNEUsWUFBWVYsVUFBVTs7Ozs7Ozs7Ozs7OzhGQUd4Qyw4REFBQ3lEO29GQUFLUCxXQUFVOzhGQUNicEgsNkRBQVlBLENBQUM0RSxZQUFZUixRQUFROzs7Ozs7Ozs7Ozs7c0ZBR3RDLDhEQUFDK0M7NEVBQUlDLFdBQVU7OzhGQUNiLDhEQUFDZTtvRkFBR2YsV0FBVTs4RkFBd0J4QyxZQUFZM0MsS0FBSzs7Ozs7OzhGQUN2RCw4REFBQ3FGO29GQUFFRixXQUFVOzhGQUNWeEMsWUFBWW1DLFlBQVk7Ozs7OztnRkFFMUJuQyxZQUFZd0QsNEJBQTRCLGtCQUN2Qyw4REFBQ2Q7b0ZBQUVGLFdBQVU7OEZBQ1Z4QyxZQUFZd0QsNEJBQTRCOzs7Ozs7Z0ZBRzNDeEQsQ0FBQUEsWUFBWUMsTUFBTSxLQUFLLGlCQUFpQkQsWUFBWUMsTUFBTSxLQUFLLFdBQVUsbUJBQ3pFLDhEQUFDeUM7b0ZBQUVGLFdBQVU7OEZBQ1Z4QyxZQUFZQyxNQUFNLEtBQUssZ0JBQ3BCLHdDQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEVBTVosOERBQUNzQztvRUFBSUMsV0FBVTs7c0ZBQ2IsOERBQUNEOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ3RJLHVEQUFLQTtvRkFDSnNELFNBQ0V3QyxZQUFZQyxNQUFNLEtBQUssY0FBYyxZQUNyQ0QsWUFBWUMsTUFBTSxLQUFLLGNBQWMsY0FDckNELFlBQVlDLE1BQU0sS0FBSyxjQUFjLGdCQUNyQ0QsWUFBWUMsTUFBTSxLQUFLLGdCQUFnQixZQUN2QztvRkFFRnVDLFdBQVU7O3dGQUVUeEMsWUFBWUMsTUFBTSxLQUFLLGVBQWU7d0ZBQ3RDRCxZQUFZQyxNQUFNLEtBQUssZUFBZTt3RkFDdENELFlBQVlDLE1BQU0sS0FBSyxlQUFlO3dGQUN0Q0QsWUFBWUMsTUFBTSxLQUFLLGVBQWU7d0ZBQ3RDRCxZQUFZQyxNQUFNLEtBQUssaUJBQWlCOzs7Ozs7O2dGQUUxQ0QsWUFBWVEsS0FBSyxrQkFDaEIsOERBQUN1QztvRkFBS1AsV0FBVTs7d0ZBQXdDO3dGQUNsRHhDLFlBQVlRLEtBQUssQ0FBQ2lELE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozt3RUFLbkN6RCxZQUFZQyxNQUFNLEtBQUssZUFBZUQsWUFBWUMsTUFBTSxLQUFLLDZCQUM1RCw4REFBQ3NDOzRFQUFJQyxXQUFVOztnRkFDWnhDLFlBQVlDLE1BQU0sS0FBSyw2QkFDdEIsOERBQUNoRyx5REFBTUE7b0ZBQ0x5SixNQUFLO29GQUNMbEcsU0FBUTtvRkFDUmdGLFdBQVU7b0ZBQ1ZHLFNBQVMsQ0FBQ2dCLElBQU10Qix3QkFBd0JyQyxhQUFhMkQ7O3NHQUVyRCw4REFBQzlJLDBJQUFJQTs0RkFBQzJILFdBQVU7Ozs7Ozt3RkFBaUI7Ozs7Ozs7OEZBSXJDLDhEQUFDdkkseURBQU1BO29GQUNMeUosTUFBSztvRkFDTGxHLFNBQVE7b0ZBQ1JnRixXQUFVO29GQUNWRyxTQUFTLENBQUNnQixJQUFNM0Isd0JBQXdCaEMsYUFBYTJEOztzR0FFckQsOERBQUMvSSwwSUFBQ0E7NEZBQUM0SCxXQUFVOzs7Ozs7d0ZBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyREE3RmpDeEMsWUFBWUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0E2R3JDLDhEQUFDOUYsNERBQVdBO3dCQUFDd0ksT0FBTTt3QkFBZUosV0FBVTtrQ0FDMUMsNEVBQUNqSCxxRUFBZ0JBOzRCQUNmTyxjQUFjRTs0QkFDZEkseUJBQXlCQTs0QkFDekJ3SCxxQkFBcUJ6RTs0QkFDckIwRSxvQkFBb0I5RDs0QkFDcEIrRCxxQkFBcUJyRDs0QkFDckIvRCxTQUFTQTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS2YsOERBQUNsQixvRUFBZUE7Z0JBQ2Q0QixNQUFNUjtnQkFDTm1ILGNBQWNsSDtnQkFDZFgsVUFBVUE7Z0JBQ1ZFLHlCQUF5QkE7Z0JBQ3pCRSxZQUFZQTtnQkFDWitDLGFBQWFyQztnQkFDYmdILFVBQVU3QztnQkFDVnpFLFNBQVNBOzs7Ozs7Ozs7Ozs7QUFJakI7R0FubkJNaEI7O1FBMkJjVixzREFBUUE7UUFDWFMsdURBQVNBOzs7S0E1QnBCQztBQXFuQk4saUVBQWVBLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2lyb3ZcXERvY3VtZW50c1xcbmV4dC1qc1xcbmVjdGFyXFxuZWN0YXItbmV4dGpzXFxzcmNcXGFwcFxcZGFzaGJvYXJkXFxhZ2VuZGFcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgQ2FsZW5kYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FsZW5kYXInO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9iYWRnZSc7XG5pbXBvcnQgeyBUYWJzLCBUYWJzQ29udGVudCwgVGFic0xpc3QsIFRhYnNUcmlnZ2VyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RhYnMnO1xuaW1wb3J0IHsgQ2FsZW5kYXIgYXMgQ2FsZW5kYXJJY29uLCBDbG9jaywgUGx1cywgVXNlcnMsIEdyaWQzWDMsIFgsIFBsYXksIEZpbGVUZXh0LCBDaGVja0NpcmNsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgQ29uZmlybURpYWxvZyBmcm9tICdAL2NvbXBvbmVudHMvQ29uZmlybURpYWxvZyc7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0JztcbmltcG9ydCB7IGZvcm1hdCB9IGZyb20gJ2RhdGUtZm5zJztcbmltcG9ydCB7IHB0QlIgfSBmcm9tICdkYXRlLWZucy9sb2NhbGUnO1xuaW1wb3J0IHsgZm9ybWF0RGF0ZUJSLCBmb3JtYXRUaW1lQlIsIGZvcm1hdERhdGVUaW1lQlIsIGdldEFwcG9pbnRtZW50U3RhdHVzQlIsIGdldEFwcG9pbnRtZW50VHlwZUJSLCB0b0xvY2FsSVNPU3RyaW5nIH0gZnJvbSAnQC9saWIvZGF0ZS11dGlscyc7XG5pbXBvcnQgeyBtYWtlQXV0aGVudGljYXRlZFJlcXVlc3QgfSBmcm9tICdAL2xpYi9hcGktY2xpZW50JztcbmltcG9ydCBGdWxsQ2FsZW5kYXJWaWV3IGZyb20gJ0AvY29tcG9uZW50cy9GdWxsQ2FsZW5kYXJWaWV3JztcbmltcG9ydCBBcHBvaW50bWVudEZvcm0gZnJvbSAnQC9jb21wb25lbnRzL0FwcG9pbnRtZW50Rm9ybSc7XG5pbXBvcnQgdHlwZSB7IERhdGVTZWxlY3RBcmcgfSBmcm9tICdAZnVsbGNhbGVuZGFyL2NvcmUnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcblxudHlwZSBQYXRpZW50ID0ge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGVtYWlsPzogc3RyaW5nO1xuICBwaG9uZT86IHN0cmluZztcbn07XG5cbnR5cGUgSGVhbHRoY2FyZVByb2Zlc3Npb25hbCA9IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBzcGVjaWFsdHk6IHN0cmluZyB8IG51bGw7XG4gIGlzX2FjdGl2ZTogYm9vbGVhbjtcbn07XG5cbnR5cGUgUHJvY2VkdXJlID0ge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsO1xuICBkZWZhdWx0X3ByaWNlOiBudW1iZXIgfCBudWxsO1xuICBkdXJhdGlvbl9taW51dGVzOiBudW1iZXIgfCBudWxsO1xufTtcblxudHlwZSBBcHBvaW50bWVudCA9IHtcbiAgaWQ6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZyB8IG51bGw7XG4gIHBhdGllbnRfaWQ6IHN0cmluZztcbiAgcGF0aWVudF9uYW1lPzogc3RyaW5nO1xuICBoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9pZDogc3RyaW5nIHwgbnVsbDtcbiAgaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfbmFtZT86IHN0cmluZztcbiAgc3RhcnRfdGltZTogc3RyaW5nO1xuICBlbmRfdGltZTogc3RyaW5nO1xuICB0eXBlOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICBwcmljZTogbnVtYmVyIHwgbnVsbDtcbn07XG5cbnR5cGUgQ2xpbmljU2V0dGluZ3MgPSB7XG4gIHdvcmtpbmdfaG91cnNfc3RhcnQ6IHN0cmluZztcbiAgd29ya2luZ19ob3Vyc19lbmQ6IHN0cmluZztcbiAgd29ya2luZ19kYXlzOiBudW1iZXJbXTtcbiAgYXBwb2ludG1lbnRfZHVyYXRpb25fbWludXRlczogbnVtYmVyO1xuICBhbGxvd193ZWVrZW5kX2FwcG9pbnRtZW50czogYm9vbGVhbjtcbn07XG5cbmNvbnN0IEFnZW5kYVBhZ2UgPSAoKSA9PiB7XG4gIGNvbnN0IFtzZWxlY3RlZERhdGUsIHNldFNlbGVjdGVkRGF0ZV0gPSB1c2VTdGF0ZTxEYXRlPihuZXcgRGF0ZSgpKTtcbiAgY29uc3QgW2FwcG9pbnRtZW50cywgc2V0QXBwb2ludG1lbnRzXSA9IHVzZVN0YXRlPEFwcG9pbnRtZW50W10+KFtdKTtcbiAgY29uc3QgW2FsbEFwcG9pbnRtZW50cywgc2V0QWxsQXBwb2ludG1lbnRzXSA9IHVzZVN0YXRlPEFwcG9pbnRtZW50W10+KFtdKTtcbiAgY29uc3QgW3BhdGllbnRzLCBzZXRQYXRpZW50c10gPSB1c2VTdGF0ZTxQYXRpZW50W10+KFtdKTtcbiAgY29uc3QgW2hlYWx0aGNhcmVQcm9mZXNzaW9uYWxzLCBzZXRIZWFsdGhjYXJlUHJvZmVzc2lvbmFsc10gPSB1c2VTdGF0ZTxIZWFsdGhjYXJlUHJvZmVzc2lvbmFsW10+KFtdKTtcbiAgY29uc3QgW3Byb2NlZHVyZXMsIHNldFByb2NlZHVyZXNdID0gdXNlU3RhdGU8UHJvY2VkdXJlW10+KFtdKTtcbiAgY29uc3QgW2NsaW5pY1NldHRpbmdzLCBzZXRDbGluaWNTZXR0aW5nc10gPSB1c2VTdGF0ZTxDbGluaWNTZXR0aW5ncyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2FwcG9pbnRtZW50Rm9ybU9wZW4sIHNldEFwcG9pbnRtZW50Rm9ybU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbY3VycmVudFZpZXcsIHNldEN1cnJlbnRWaWV3XSA9IHVzZVN0YXRlPCdjYWxlbmRhcicgfCAnZnVsbGNhbGVuZGFyJz4oJ2NhbGVuZGFyJyk7XG4gIGNvbnN0IFthcHBvaW50bWVudEZvcm1EYXRhLCBzZXRBcHBvaW50bWVudEZvcm1EYXRhXSA9IHVzZVN0YXRlPGFueT4obnVsbCk7XG5cbiAgLy8gQ29uZmlybWF0aW9uIGRpYWxvZyBzdGF0ZXNcbiAgY29uc3QgW2NvbmZpcm1EaWFsb2csIHNldENvbmZpcm1EaWFsb2ddID0gdXNlU3RhdGU8e1xuICAgIG9wZW46IGJvb2xlYW47XG4gICAgdGl0bGU6IHN0cmluZztcbiAgICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICAgIG9uQ29uZmlybTogKCkgPT4gdm9pZDtcbiAgICB2YXJpYW50PzogJ2RlZmF1bHQnIHwgJ2Rlc3RydWN0aXZlJztcbiAgfT4oe1xuICAgIG9wZW46IGZhbHNlLFxuICAgIHRpdGxlOiAnJyxcbiAgICBkZXNjcmlwdGlvbjogJycsXG4gICAgb25Db25maXJtOiAoKSA9PiB7fSxcbiAgICB2YXJpYW50OiAnZGVmYXVsdCdcbiAgfSk7XG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KCk7XG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuXG4gIC8vIEZldGNoIGluaXRpYWwgZGF0YSB0aGF0IGRvZXNuJ3QgZGVwZW5kIG9uIHNlbGVjdGVkIGRhdGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEFsbEFwcG9pbnRtZW50cygpO1xuICAgIGZldGNoUGF0aWVudHMoKTtcbiAgICBmZXRjaEhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzKCk7XG4gICAgZmV0Y2hQcm9jZWR1cmVzKCk7XG4gICAgZmV0Y2hDbGluaWNTZXR0aW5ncygpO1xuICB9LCBbXSk7XG5cbiAgLy8gRmV0Y2ggZGF0ZS1zcGVjaWZpYyBhcHBvaW50bWVudHMgd2hlbiBzZWxlY3RlZCBkYXRlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaEFwcG9pbnRtZW50cygpO1xuICB9LCBbc2VsZWN0ZWREYXRlXSk7XG5cbiAgY29uc3QgZmV0Y2hBcHBvaW50bWVudHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCBkYXRlU3RyID0gZm9ybWF0KHNlbGVjdGVkRGF0ZSwgJ3l5eXktTU0tZGQnKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KGAvYXBpL2FwcG9pbnRtZW50cz9kYXRlPSR7ZGF0ZVN0cn1gKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGFwcG9pbnRtZW50cycpO1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc3QgZGF0YSA9IHJlc3VsdC5kYXRhIHx8IHJlc3VsdDtcbiAgICAgIHNldEFwcG9pbnRtZW50cyhBcnJheS5pc0FycmF5KGRhdGEpID8gZGF0YSA6IFtdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgYXBwb2ludG1lbnRzOicsIGVycm9yKTtcbiAgICAgIHNldEFwcG9pbnRtZW50cyhbXSk7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkVycm8gYW8gY2FycmVnYXIgY29uc3VsdGFzXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdPY29ycmV1IHVtIGVycm8gaW5lc3BlcmFkbycsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxuICAgICAgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmZXRjaFBhdGllbnRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCgnL2FwaS9wYXRpZW50cycpO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggcGF0aWVudHMnKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCdQYXRpZW50cyBBUEkgcmVzcG9uc2U6JywgcmVzdWx0KTtcbiAgICAgIGNvbnN0IGRhdGEgPSByZXN1bHQuZGF0YSB8fCByZXN1bHQ7XG4gICAgICBzZXRQYXRpZW50cyhBcnJheS5pc0FycmF5KGRhdGEpID8gZGF0YSA6IFtdKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgcGF0aWVudHM6JywgZXJyb3IpO1xuICAgICAgc2V0UGF0aWVudHMoW10pO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcnJvIGFvIGNhcnJlZ2FyIHBhY2llbnRlc1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnT2NvcnJldSB1bSBlcnJvIGluZXNwZXJhZG8nLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCJcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmZXRjaEhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCgnL2FwaS9oZWFsdGhjYXJlLXByb2Zlc3Npb25hbHMnKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGhlYWx0aGNhcmUgcHJvZmVzc2lvbmFscycpO1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc3QgZGF0YSA9IHJlc3VsdC5kYXRhIHx8IHJlc3VsdDtcbiAgICAgIHNldEhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzKEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogW10pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBoZWFsdGhjYXJlIHByb2Zlc3Npb25hbHM6JywgZXJyb3IpO1xuICAgICAgc2V0SGVhbHRoY2FyZVByb2Zlc3Npb25hbHMoW10pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmZXRjaFByb2NlZHVyZXMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KCcvYXBpL3Byb2NlZHVyZXMnKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIHByb2NlZHVyZXMnKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnN0IGRhdGEgPSByZXN1bHQuZGF0YSB8fCByZXN1bHQ7XG4gICAgICBzZXRQcm9jZWR1cmVzKEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogW10pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwcm9jZWR1cmVzOicsIGVycm9yKTtcbiAgICAgIHNldFByb2NlZHVyZXMoW10pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmZXRjaEFsbEFwcG9pbnRtZW50cyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoJy9hcGkvYXBwb2ludG1lbnRzJyk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBhbGwgYXBwb2ludG1lbnRzJyk7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zdCBkYXRhID0gcmVzdWx0LmRhdGEgfHwgcmVzdWx0O1xuICAgICAgc2V0QWxsQXBwb2ludG1lbnRzKEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogW10pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBhbGwgYXBwb2ludG1lbnRzOicsIGVycm9yKTtcbiAgICAgIHNldEFsbEFwcG9pbnRtZW50cyhbXSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoQ2xpbmljU2V0dGluZ3MgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KCcvYXBpL2NsaW5pYy1zZXR0aW5ncycpO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggY2xpbmljIHNldHRpbmdzJyk7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zdCBkYXRhID0gcmVzdWx0LmRhdGEgfHwgcmVzdWx0O1xuICAgICAgc2V0Q2xpbmljU2V0dGluZ3MoZGF0YSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGNsaW5pYyBzZXR0aW5nczonLCBlcnJvcik7XG4gICAgICAvLyBTZXQgZGVmYXVsdCBzZXR0aW5ncyBpZiBmZXRjaCBmYWlsc1xuICAgICAgc2V0Q2xpbmljU2V0dGluZ3Moe1xuICAgICAgICB3b3JraW5nX2hvdXJzX3N0YXJ0OiAnMDg6MDAnLFxuICAgICAgICB3b3JraW5nX2hvdXJzX2VuZDogJzE4OjAwJyxcbiAgICAgICAgd29ya2luZ19kYXlzOiBbMSwgMiwgMywgNCwgNV0sXG4gICAgICAgIGFwcG9pbnRtZW50X2R1cmF0aW9uX21pbnV0ZXM6IDMwLFxuICAgICAgICBhbGxvd193ZWVrZW5kX2FwcG9pbnRtZW50czogZmFsc2VcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVBcHBvaW50bWVudENyZWF0ZSA9IChzZWxlY3RJbmZvPzogRGF0ZVNlbGVjdEFyZykgPT4ge1xuICAgIGNvbnN0IGluaXRpYWxEYXRhOiBhbnkgPSB7fTtcblxuICAgIGlmIChzZWxlY3RJbmZvKSB7XG4gICAgICAvLyBGdWxsQ2FsZW5kYXIgcHJvdmlkZXMgZGF0ZXMgaW4gbG9jYWwgdGltZXpvbmUsIHVzZSB0aGVtIGRpcmVjdGx5XG4gICAgICBpbml0aWFsRGF0YS5zdGFydF90aW1lID0gdG9Mb2NhbElTT1N0cmluZyhzZWxlY3RJbmZvLnN0YXJ0KTtcbiAgICAgIGluaXRpYWxEYXRhLmVuZF90aW1lID0gdG9Mb2NhbElTT1N0cmluZyhzZWxlY3RJbmZvLmVuZCk7XG4gICAgfSBlbHNlIGlmIChzZWxlY3RlZERhdGUpIHtcbiAgICAgIC8vIENyZWF0ZSBhcHBvaW50bWVudCBmb3Igc2VsZWN0ZWQgZGF0ZSBhdCA5OjAwIEFNXG4gICAgICBjb25zdCBzdGFydFRpbWUgPSBuZXcgRGF0ZShzZWxlY3RlZERhdGUpO1xuICAgICAgc3RhcnRUaW1lLnNldEhvdXJzKDksIDAsIDAsIDApO1xuICAgICAgY29uc3QgZW5kVGltZSA9IG5ldyBEYXRlKHN0YXJ0VGltZSk7XG4gICAgICBlbmRUaW1lLnNldE1pbnV0ZXMoZW5kVGltZS5nZXRNaW51dGVzKCkgKyAzMCk7XG5cbiAgICAgIGluaXRpYWxEYXRhLnN0YXJ0X3RpbWUgPSB0b0xvY2FsSVNPU3RyaW5nKHN0YXJ0VGltZSk7XG4gICAgICBpbml0aWFsRGF0YS5lbmRfdGltZSA9IHRvTG9jYWxJU09TdHJpbmcoZW5kVGltZSk7XG4gICAgfVxuXG4gICAgc2V0QXBwb2ludG1lbnRGb3JtRGF0YShpbml0aWFsRGF0YSk7XG4gICAgc2V0QXBwb2ludG1lbnRGb3JtT3Blbih0cnVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVBcHBvaW50bWVudENsaWNrID0gKGFwcG9pbnRtZW50OiBBcHBvaW50bWVudCkgPT4ge1xuICAgIC8vIEZvciBpbi1wcm9ncmVzcyBhbmQgY29tcGxldGVkIGFwcG9pbnRtZW50cywgbmF2aWdhdGUgdG8gbWVkaWNhbCByZWNvcmQgc2NyZWVuXG4gICAgaWYgKGFwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2luX3Byb2dyZXNzJyB8fCBhcHBvaW50bWVudC5zdGF0dXMgPT09ICdjb21wbGV0ZWQnKSB7XG4gICAgICBjb25zb2xlLmxvZygnTmF2aWdhdGluZyB0byBtZWRpY2FsIHJlY29yZCBmb3IgYXBwb2ludG1lbnQ6JywgYXBwb2ludG1lbnQuaWQpO1xuICAgICAgcm91dGVyLnB1c2goYC9kYXNoYm9hcmQvcHJvbnR1YXJpby8ke2FwcG9pbnRtZW50LnBhdGllbnRfaWR9P2FwcG9pbnRtZW50X2lkPSR7YXBwb2ludG1lbnQuaWR9YCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgLy8gRm9yIG90aGVyIHN0YXR1c2VzLCBvcGVuIGVkaXQgZm9ybSB3aXRoIGFwcG9pbnRtZW50IGRhdGFcbiAgICBjb25zb2xlLmxvZygnQXBwb2ludG1lbnQgY2xpY2tlZCBmb3IgZWRpdGluZzonLCBhcHBvaW50bWVudCk7XG5cbiAgICBjb25zdCBlZGl0RGF0YSA9IHtcbiAgICAgIGlkOiBhcHBvaW50bWVudC5pZCxcbiAgICAgIHRpdGxlOiBhcHBvaW50bWVudC50aXRsZSxcbiAgICAgIGRlc2NyaXB0aW9uOiBhcHBvaW50bWVudC5kZXNjcmlwdGlvbixcbiAgICAgIHBhdGllbnRfaWQ6IGFwcG9pbnRtZW50LnBhdGllbnRfaWQsXG4gICAgICBoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9pZDogYXBwb2ludG1lbnQuaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfaWQsXG4gICAgICBzdGFydF90aW1lOiBhcHBvaW50bWVudC5zdGFydF90aW1lLFxuICAgICAgZW5kX3RpbWU6IGFwcG9pbnRtZW50LmVuZF90aW1lLFxuICAgICAgdHlwZTogYXBwb2ludG1lbnQudHlwZSxcbiAgICAgIHN0YXR1czogYXBwb2ludG1lbnQuc3RhdHVzLFxuICAgICAgcHJpY2U6IGFwcG9pbnRtZW50LnByaWNlLFxuICAgIH07XG5cbiAgICBzZXRBcHBvaW50bWVudEZvcm1EYXRhKGVkaXREYXRhKTtcbiAgICBzZXRBcHBvaW50bWVudEZvcm1PcGVuKHRydWUpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFwcG9pbnRtZW50VXBkYXRlID0gYXN5bmMgKGFwcG9pbnRtZW50SWQ6IHN0cmluZywgbmV3U3RhcnQ6IERhdGUsIG5ld0VuZDogRGF0ZSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdChgL2FwaS9hcHBvaW50bWVudHMvJHthcHBvaW50bWVudElkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBzdGFydF90aW1lOiBuZXdTdGFydC50b0lTT1N0cmluZygpLFxuICAgICAgICAgIGVuZF90aW1lOiBuZXdFbmQudG9JU09TdHJpbmcoKSxcbiAgICAgICAgfSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgYXBwb2ludG1lbnQnKTtcblxuICAgICAgLy8gUmVmcmVzaCBib3RoIGRhaWx5IGFuZCBhbGwgYXBwb2ludG1lbnRzIGZvciBjYWxlbmRhciB2aWV3c1xuICAgICAgZmV0Y2hBcHBvaW50bWVudHMoKTtcbiAgICAgIGZldGNoQWxsQXBwb2ludG1lbnRzKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGFwcG9pbnRtZW50OicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVBcHBvaW50bWVudFN1Ym1pdCA9IGFzeW5jIChkYXRhOiBhbnkpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgaXNFZGl0aW5nID0gZGF0YS5pZDtcbiAgICAgIGNvbnN0IHVybCA9IGlzRWRpdGluZyA/IGAvYXBpL2FwcG9pbnRtZW50cy8ke2RhdGEuaWR9YCA6ICcvYXBpL2FwcG9pbnRtZW50cyc7XG4gICAgICBjb25zdCBtZXRob2QgPSBpc0VkaXRpbmcgPyAnUFVUJyA6ICdQT1NUJztcblxuICAgICAgLy8gUmVtb3ZlIGlkIGZyb20gZGF0YSBmb3IgQVBJIGNhbGxcbiAgICAgIGNvbnN0IHsgaWQsIC4uLnN1Ym1pdERhdGEgfSA9IGRhdGE7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KHVybCwge1xuICAgICAgICBtZXRob2QsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShzdWJtaXREYXRhKVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvICR7aXNFZGl0aW5nID8gJ3VwZGF0ZScgOiAnY3JlYXRlJ30gYXBwb2ludG1lbnRgKTtcblxuICAgICAgLy8gUmVmcmVzaCBib3RoIGRhaWx5IGFuZCBhbGwgYXBwb2ludG1lbnRzIGZvciBjYWxlbmRhciB2aWV3c1xuICAgICAgZmV0Y2hBcHBvaW50bWVudHMoKTtcbiAgICAgIGZldGNoQWxsQXBwb2ludG1lbnRzKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yICR7ZGF0YS5pZCA/ICd1cGRhdGluZycgOiAnY3JlYXRpbmcnfSBhcHBvaW50bWVudDpgLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgLy8gQ2FsY3VsYXRlIGFwcG9pbnRtZW50IGNvdW50cyBmb3IgY2FsZW5kYXIgaW5kaWNhdG9yc1xuICBjb25zdCBhcHBvaW50bWVudENvdW50cyA9IFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IGNvdW50czogUmVjb3JkPHN0cmluZywgbnVtYmVyPiA9IHt9O1xuICAgIGFwcG9pbnRtZW50cy5mb3JFYWNoKGFwcG9pbnRtZW50ID0+IHtcbiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShhcHBvaW50bWVudC5zdGFydF90aW1lKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07XG4gICAgICBjb3VudHNbZGF0ZV0gPSAoY291bnRzW2RhdGVdIHx8IDApICsgMTtcbiAgICB9KTtcbiAgICByZXR1cm4gY291bnRzO1xuICB9LCBbYXBwb2ludG1lbnRzXSk7XG5cbiAgY29uc3QgdXBkYXRlQXBwb2ludG1lbnRTdGF0dXMgPSBhc3luYyAoYXBwb2ludG1lbnRJZDogc3RyaW5nLCBzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdChgL2FwaS9hcHBvaW50bWVudHMvJHthcHBvaW50bWVudElkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgc3RhdHVzIH0pXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIGFwcG9pbnRtZW50IHN0YXR1cycpO1xuXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIlN1Y2Vzc28hXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIlN0YXR1cyBkYSBjb25zdWx0YSBhdHVhbGl6YWRvLlwiLFxuICAgICAgfSk7XG5cbiAgICAgIGZldGNoQXBwb2ludG1lbnRzKCk7XG4gICAgICBmZXRjaEFsbEFwcG9pbnRtZW50cygpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBhcHBvaW50bWVudCBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcnJvXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkVycm8gYW8gYXR1YWxpemFyIHN0YXR1cyBkYSBjb25zdWx0YS5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlQXBwb2ludG1lbnQgPSBhc3luYyAoYXBwb2ludG1lbnRJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKCdUZW0gY2VydGV6YSBxdWUgZGVzZWphIGV4Y2x1aXIgZXN0YSBjb25zdWx0YT8nKSkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KGAvYXBpL2FwcG9pbnRtZW50cy8ke2FwcG9pbnRtZW50SWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIGFwcG9pbnRtZW50Jyk7XG5cbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiU3VjZXNzbyFcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQ29uc3VsdGEgZXhjbHXDrWRhIGNvbSBzdWNlc3NvLlwiLFxuICAgICAgfSk7XG5cbiAgICAgIGZldGNoQXBwb2ludG1lbnRzKCk7XG4gICAgICBmZXRjaEFsbEFwcG9pbnRtZW50cygpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyBhcHBvaW50bWVudDonLCBlcnJvcik7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkVycm9cIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRXJybyBhbyBleGNsdWlyIGNvbnN1bHRhLlwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCJcbiAgICAgIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDYW5jZWxBcHBvaW50bWVudCA9IGFzeW5jIChhcHBvaW50bWVudDogQXBwb2ludG1lbnQsIGV2ZW50OiBSZWFjdC5Nb3VzZUV2ZW50KSA9PiB7XG4gICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7IC8vIFByZXZlbnQgdHJpZ2dlcmluZyB0aGUgZWRpdCBjbGljayBoYW5kbGVyXG5cbiAgICBzZXRDb25maXJtRGlhbG9nKHtcbiAgICAgIG9wZW46IHRydWUsXG4gICAgICB0aXRsZTogJ0NhbmNlbGFyIENvbnN1bHRhJyxcbiAgICAgIGRlc2NyaXB0aW9uOiBgVGVtIGNlcnRlemEgcXVlIGRlc2VqYSBjYW5jZWxhciBhIGNvbnN1bHRhIGRlICR7YXBwb2ludG1lbnQucGF0aWVudF9uYW1lfT9gLFxuICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJyxcbiAgICAgIG9uQ29uZmlybTogKCkgPT4gcGVyZm9ybUNhbmNlbEFwcG9pbnRtZW50KGFwcG9pbnRtZW50LmlkKVxuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IHBlcmZvcm1DYW5jZWxBcHBvaW50bWVudCA9IGFzeW5jIChhcHBvaW50bWVudElkOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoYC9hcGkvYXBwb2ludG1lbnRzLyR7YXBwb2ludG1lbnRJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHN0YXR1czogJ2NhbmNlbGxlZCcgfSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBjYW5jZWwgYXBwb2ludG1lbnQnKTtcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJTdWNlc3NvIVwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJDb25zdWx0YSBjYW5jZWxhZGEgY29tIHN1Y2Vzc28uXCIsXG4gICAgICB9KTtcblxuICAgICAgZmV0Y2hBcHBvaW50bWVudHMoKTtcbiAgICAgIGZldGNoQWxsQXBwb2ludG1lbnRzKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNhbmNlbGluZyBhcHBvaW50bWVudDonLCBlcnJvcik7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkVycm9cIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRXJybyBhbyBjYW5jZWxhciBjb25zdWx0YS5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU3RhcnRDb25zdWx0YXRpb24gPSBhc3luYyAoYXBwb2ludG1lbnQ6IEFwcG9pbnRtZW50LCBldmVudDogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpOyAvLyBQcmV2ZW50IHRyaWdnZXJpbmcgdGhlIGVkaXQgY2xpY2sgaGFuZGxlclxuXG4gICAgc2V0Q29uZmlybURpYWxvZyh7XG4gICAgICBvcGVuOiB0cnVlLFxuICAgICAgdGl0bGU6ICdJbmljaWFyIEF0ZW5kaW1lbnRvJyxcbiAgICAgIGRlc2NyaXB0aW9uOiBgRGVzZWphIGluaWNpYXIgbyBhdGVuZGltZW50byBwYXJhICR7YXBwb2ludG1lbnQucGF0aWVudF9uYW1lfT9gLFxuICAgICAgdmFyaWFudDogJ2RlZmF1bHQnLFxuICAgICAgb25Db25maXJtOiAoKSA9PiBwZXJmb3JtU3RhcnRDb25zdWx0YXRpb24oYXBwb2ludG1lbnQpXG4gICAgfSk7XG4gIH07XG5cbiAgY29uc3QgcGVyZm9ybVN0YXJ0Q29uc3VsdGF0aW9uID0gYXN5bmMgKGFwcG9pbnRtZW50OiBBcHBvaW50bWVudCkgPT4ge1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFVwZGF0ZSBhcHBvaW50bWVudCBzdGF0dXMgdG8gaW5fcHJvZ3Jlc3NcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KGAvYXBpL2FwcG9pbnRtZW50cy8ke2FwcG9pbnRtZW50LmlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgICAgaGVhZGVyczogeyAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgc3RhdHVzOiAnaW5fcHJvZ3Jlc3MnIH0pXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gc3RhcnQgY29uc3VsdGF0aW9uJyk7XG5cbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiU3VjZXNzbyFcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQXRlbmRpbWVudG8gaW5pY2lhZG8gY29tIHN1Y2Vzc28uXCIsXG4gICAgICB9KTtcblxuICAgICAgLy8gTmF2aWdhdGUgdG8gbWVkaWNhbCByZWNvcmQgc2NyZWVuXG4gICAgICByb3V0ZXIucHVzaChgL2Rhc2hib2FyZC9wcm9udHVhcmlvLyR7YXBwb2ludG1lbnQucGF0aWVudF9pZH0/YXBwb2ludG1lbnRfaWQ9JHthcHBvaW50bWVudC5pZH1gKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3RhcnRpbmcgY29uc3VsdGF0aW9uOicsIGVycm9yKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiRXJyb1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJFcnJvIGFvIGluaWNpYXIgYXRlbmRpbWVudG8uXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmRcIj5BZ2VuZGE8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkdlcmVuY2llIHN1YXMgY29uc3VsdGFzIGUgaG9yw6FyaW9zPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBcHBvaW50bWVudENyZWF0ZSgpfT5cbiAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICBOb3ZhIENvbnN1bHRhXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxUYWJzXG4gICAgICAgIHZhbHVlPXtjdXJyZW50Vmlld31cbiAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB7XG4gICAgICAgICAgLy8gUHJldmVudCB0YWIgc3dpdGNoaW5nIGR1cmluZyBsb2FkaW5nIHRvIGF2b2lkIHJhY2UgY29uZGl0aW9uc1xuICAgICAgICAgIGlmIChsb2FkaW5nKSB7XG4gICAgICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgICAgIHRpdGxlOiBcIkFndWFyZGVcIixcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IFwiQWd1YXJkZSBvIGNhcnJlZ2FtZW50byBkb3MgZGFkb3MgYW50ZXMgZGUgdHJvY2FyIGRlIGFiYS5cIixcbiAgICAgICAgICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgICAgc2V0Q3VycmVudFZpZXcodmFsdWUgYXMgJ2NhbGVuZGFyJyB8ICdmdWxsY2FsZW5kYXInKTtcbiAgICAgICAgfX1cbiAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgID5cbiAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy0yXCI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyXG4gICAgICAgICAgICB2YWx1ZT1cImNhbGVuZGFyXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQteHMgc206dGV4dC1zbSAke2xvYWRpbmcgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJyd9YH1cbiAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxDYWxlbmRhckljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4geHM6aW5saW5lXCI+Q2FsZW5kw6FyaW88L3NwYW4+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ4czpoaWRkZW5cIj5DYWwuPC9zcGFuPlxuICAgICAgICAgICAge2xvYWRpbmcgJiYgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMyB3LTMgYm9yZGVyLWItMiBib3JkZXItY3VycmVudCBtbC0xXCI+PC9kaXY+fVxuICAgICAgICAgIDwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyXG4gICAgICAgICAgICB2YWx1ZT1cImZ1bGxjYWxlbmRhclwiXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXhzIHNtOnRleHQtc20gJHtsb2FkaW5nID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJyA6ICcnfWB9XG4gICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8R3JpZDNYMyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiB4czppbmxpbmVcIj5BZ2VuZGEgQ29tcGxldGE8L3NwYW4+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ4czpoaWRkZW5cIj5BZ2VuZGE8L3NwYW4+XG4gICAgICAgICAgICB7bG9hZGluZyAmJiA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zIHctMyBib3JkZXItYi0yIGJvcmRlci1jdXJyZW50IG1sLTFcIj48L2Rpdj59XG4gICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJjYWxlbmRhclwiIGNsYXNzTmFtZT1cInNwYWNlLXktNiBtdC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPENhbGVuZGFySWNvbiBjbGFzc05hbWU9XCJtci0yIGgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICAgIENhbGVuZMOhcmlvXG4gICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50ID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxDYWxlbmRhclxuICAgICAgICAgICAgICAgICAgICBtb2RlPVwic2luZ2xlXCJcbiAgICAgICAgICAgICAgICAgICAgbG9jYWxlPXtwdEJSfVxuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZD17c2VsZWN0ZWREYXRlfVxuICAgICAgICAgICAgICAgICAgICBvblNlbGVjdD17KGRhdGUpID0+IGRhdGUgJiYgc2V0U2VsZWN0ZWREYXRlKGRhdGUpfVxuICAgICAgICAgICAgICAgICAgICAvLyBhcHBvaW50bWVudENvdW50cz17YXBwb2ludG1lbnRDb3VudHN9XG4gICAgICAgICAgICAgICAgICAgIC8vIGNsaW5pY1NldHRpbmdzPXtjbGluaWNTZXR0aW5ncyB8fCB1bmRlZmluZWR9XG4gICAgICAgICAgICAgICAgICAgIC8vIGFwcG9pbnRtZW50cz17YWxsQXBwb2ludG1lbnRzfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLW1kIGJvcmRlci0wIHNoYWRvdy1ub25lIHctZnVsbFwiXG5cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwibXItMiBoLTUgdy01IHRleHQtcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiBzbTppbmxpbmVcIj5Db25zdWx0YXMgLSB7Zm9ybWF0RGF0ZUJSKHNlbGVjdGVkRGF0ZSl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzbTpoaWRkZW5cIj5Db25zdWx0YXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIHthcHBvaW50bWVudHMubGVuZ3RofSBjb25zdWx0YShzKSBhZ2VuZGFkYShzKSBwYXJhIGVzdGUgZGlhXG4gICAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgbWItNCBvcGFjaXR5LTUwIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHA+Q2FycmVnYW5kbyBjb25zdWx0YXMuLi48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IGFwcG9pbnRtZW50cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgbWItNCBvcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8cD5OZW5odW1hIGNvbnN1bHRhIGFnZW5kYWRhIHBhcmEgZXN0ZSBkaWE8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnRzLm1hcCgoYXBwb2ludG1lbnQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXthcHBvaW50bWVudC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBzbTppdGVtcy1jZW50ZXIgc206anVzdGlmeS1iZXR3ZWVuIHAtNCByb3VuZGVkLWxnIGJvcmRlciB0cmFuc2l0aW9uLWNvbG9ycyBzcGFjZS15LTMgc206c3BhY2UteS0wICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXBwb2ludG1lbnQuc3RhdHVzID09PSAnY2FuY2VsbGVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctcmVkLTUwIGJvcmRlci1yZWQtMjAwIGhvdmVyOmJnLXJlZC0xMDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGFwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2luX3Byb2dyZXNzJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS01MCBib3JkZXItYmx1ZS0yMDAgaG92ZXI6YmctYmx1ZS0xMDAgY3Vyc29yLXBvaW50ZXInXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IGFwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwIGJvcmRlci1ncmVlbi0yMDAgaG92ZXI6YmctZ3JlZW4tMTAwIGN1cnNvci1wb2ludGVyJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctY2FyZC81MCBob3ZlcjpiZy1jYXJkIGN1cnNvci1wb2ludGVyJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17YXBwb2ludG1lbnQuc3RhdHVzICE9PSAnY2FuY2VsbGVkJyA/ICgpID0+IGhhbmRsZUFwcG9pbnRtZW50Q2xpY2soYXBwb2ludG1lbnQpIDogdW5kZWZpbmVkfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBtaW4tdy0wIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuc3RhdHVzID09PSAnaW5fcHJvZ3Jlc3MnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC0zIHctMyB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTMgdy0zIHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRUaW1lQlIoYXBwb2ludG1lbnQuc3RhcnRfdGltZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWVCUihhcHBvaW50bWVudC5lbmRfdGltZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRydW5jYXRlXCI+e2FwcG9pbnRtZW50LnRpdGxlfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQucGF0aWVudF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LmhlYWx0aGNhcmVfcHJvZmVzc2lvbmFsX25hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCB0cnVuY2F0ZSBoaWRkZW4gc206YmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfbmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsoYXBwb2ludG1lbnQuc3RhdHVzID09PSAnaW5fcHJvZ3Jlc3MnIHx8IGFwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtNjAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuc3RhdHVzID09PSAnaW5fcHJvZ3Jlc3MnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdDbGlxdWUgcGFyYSBjb250aW51YXIgbyBhdGVuZGltZW50bydcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ0NsaXF1ZSBwYXJhIHZlciBvIHByb250dcOhcmlvJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBpdGVtcy1zdGFydCBzbTppdGVtcy1jZW50ZXIgc3BhY2UteS0yIHNtOnNwYWNlLXktMCBzbTpzcGFjZS14LTIgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD17XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXBwb2ludG1lbnQuc3RhdHVzID09PSAnY29uZmlybWVkJyA/ICdkZWZhdWx0JyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXBwb2ludG1lbnQuc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdzZWNvbmRhcnknIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhcHBvaW50bWVudC5zdGF0dXMgPT09ICdjYW5jZWxsZWQnID8gJ2Rlc3RydWN0aXZlJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXBwb2ludG1lbnQuc3RhdHVzID09PSAnaW5fcHJvZ3Jlc3MnID8gJ2RlZmF1bHQnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAnb3V0bGluZSdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ3NjaGVkdWxlZCcgJiYgJ0FnZW5kYWRvJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NvbmZpcm1lZCcgJiYgJ0NvbmZpcm1hZG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuc3RhdHVzID09PSAnY29tcGxldGVkJyAmJiAnQ29uY2x1w61kbyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthcHBvaW50bWVudC5zdGF0dXMgPT09ICdjYW5jZWxsZWQnICYmICdDYW5jZWxhZG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuc3RhdHVzID09PSAnaW5fcHJvZ3Jlc3MnICYmICdFbSBBbmRhbWVudG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthcHBvaW50bWVudC5wcmljZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBSJCB7YXBwb2ludG1lbnQucHJpY2UudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnN0YXR1cyAhPT0gJ2NhbmNlbGxlZCcgJiYgYXBwb2ludG1lbnQuc3RhdHVzICE9PSAnY29tcGxldGVkJyAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthcHBvaW50bWVudC5zdGF0dXMgPT09ICdzY2hlZHVsZWQnICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC04IHB4LTIgdGV4dC14c1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gaGFuZGxlU3RhcnRDb25zdWx0YXRpb24oYXBwb2ludG1lbnQsIGUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQbGF5IGNsYXNzTmFtZT1cImgtMyB3LTMgbXItMVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBJbmljaWFyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTggcHgtMiB0ZXh0LXhzIHRleHQtcmVkLTYwMCBob3Zlcjp0ZXh0LXJlZC03MDAgaG92ZXI6YmctcmVkLTUwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gaGFuZGxlQ2FuY2VsQXBwb2ludG1lbnQoYXBwb2ludG1lbnQsIGUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwiaC0zIHctMyBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDYW5jZWxhclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJmdWxsY2FsZW5kYXJcIiBjbGFzc05hbWU9XCJzcGFjZS15LTYgbXQtNlwiPlxuICAgICAgICAgIDxGdWxsQ2FsZW5kYXJWaWV3XG4gICAgICAgICAgICBhcHBvaW50bWVudHM9e2FsbEFwcG9pbnRtZW50c31cbiAgICAgICAgICAgIGhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzPXtoZWFsdGhjYXJlUHJvZmVzc2lvbmFsc31cbiAgICAgICAgICAgIG9uQXBwb2ludG1lbnRDcmVhdGU9e2hhbmRsZUFwcG9pbnRtZW50Q3JlYXRlfVxuICAgICAgICAgICAgb25BcHBvaW50bWVudENsaWNrPXtoYW5kbGVBcHBvaW50bWVudENsaWNrfVxuICAgICAgICAgICAgb25BcHBvaW50bWVudFVwZGF0ZT17aGFuZGxlQXBwb2ludG1lbnRVcGRhdGV9XG4gICAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG4gICAgICA8L1RhYnM+XG5cbiAgICAgIDxBcHBvaW50bWVudEZvcm1cbiAgICAgICAgb3Blbj17YXBwb2ludG1lbnRGb3JtT3Blbn1cbiAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRBcHBvaW50bWVudEZvcm1PcGVufVxuICAgICAgICBwYXRpZW50cz17cGF0aWVudHN9XG4gICAgICAgIGhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzPXtoZWFsdGhjYXJlUHJvZmVzc2lvbmFsc31cbiAgICAgICAgcHJvY2VkdXJlcz17cHJvY2VkdXJlc31cbiAgICAgICAgaW5pdGlhbERhdGE9e2FwcG9pbnRtZW50Rm9ybURhdGF9XG4gICAgICAgIG9uU3VibWl0PXtoYW5kbGVBcHBvaW50bWVudFN1Ym1pdH1cbiAgICAgICAgbG9hZGluZz17bG9hZGluZ31cbiAgICAgIC8+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBBZ2VuZGFQYWdlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDYWxlbmRhciIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJCYWRnZSIsIlRhYnMiLCJUYWJzQ29udGVudCIsIlRhYnNMaXN0IiwiVGFic1RyaWdnZXIiLCJDYWxlbmRhckljb24iLCJDbG9jayIsIlBsdXMiLCJVc2VycyIsIkdyaWQzWDMiLCJYIiwiUGxheSIsIkZpbGVUZXh0IiwiQ2hlY2tDaXJjbGUiLCJ1c2VUb2FzdCIsImZvcm1hdCIsInB0QlIiLCJmb3JtYXREYXRlQlIiLCJmb3JtYXRUaW1lQlIiLCJ0b0xvY2FsSVNPU3RyaW5nIiwibWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0IiwiRnVsbENhbGVuZGFyVmlldyIsIkFwcG9pbnRtZW50Rm9ybSIsInVzZVJvdXRlciIsIkFnZW5kYVBhZ2UiLCJzZWxlY3RlZERhdGUiLCJzZXRTZWxlY3RlZERhdGUiLCJEYXRlIiwiYXBwb2ludG1lbnRzIiwic2V0QXBwb2ludG1lbnRzIiwiYWxsQXBwb2ludG1lbnRzIiwic2V0QWxsQXBwb2ludG1lbnRzIiwicGF0aWVudHMiLCJzZXRQYXRpZW50cyIsImhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzIiwic2V0SGVhbHRoY2FyZVByb2Zlc3Npb25hbHMiLCJwcm9jZWR1cmVzIiwic2V0UHJvY2VkdXJlcyIsImNsaW5pY1NldHRpbmdzIiwic2V0Q2xpbmljU2V0dGluZ3MiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFwcG9pbnRtZW50Rm9ybU9wZW4iLCJzZXRBcHBvaW50bWVudEZvcm1PcGVuIiwiY3VycmVudFZpZXciLCJzZXRDdXJyZW50VmlldyIsImFwcG9pbnRtZW50Rm9ybURhdGEiLCJzZXRBcHBvaW50bWVudEZvcm1EYXRhIiwiY29uZmlybURpYWxvZyIsInNldENvbmZpcm1EaWFsb2ciLCJvcGVuIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIm9uQ29uZmlybSIsInZhcmlhbnQiLCJ0b2FzdCIsInJvdXRlciIsImZldGNoQWxsQXBwb2ludG1lbnRzIiwiZmV0Y2hQYXRpZW50cyIsImZldGNoSGVhbHRoY2FyZVByb2Zlc3Npb25hbHMiLCJmZXRjaFByb2NlZHVyZXMiLCJmZXRjaENsaW5pY1NldHRpbmdzIiwiZmV0Y2hBcHBvaW50bWVudHMiLCJkYXRlU3RyIiwicmVzcG9uc2UiLCJvayIsIkVycm9yIiwicmVzdWx0IiwianNvbiIsImRhdGEiLCJBcnJheSIsImlzQXJyYXkiLCJlcnJvciIsImNvbnNvbGUiLCJtZXNzYWdlIiwibG9nIiwid29ya2luZ19ob3Vyc19zdGFydCIsIndvcmtpbmdfaG91cnNfZW5kIiwid29ya2luZ19kYXlzIiwiYXBwb2ludG1lbnRfZHVyYXRpb25fbWludXRlcyIsImFsbG93X3dlZWtlbmRfYXBwb2ludG1lbnRzIiwiaGFuZGxlQXBwb2ludG1lbnRDcmVhdGUiLCJzZWxlY3RJbmZvIiwiaW5pdGlhbERhdGEiLCJzdGFydF90aW1lIiwic3RhcnQiLCJlbmRfdGltZSIsImVuZCIsInN0YXJ0VGltZSIsInNldEhvdXJzIiwiZW5kVGltZSIsInNldE1pbnV0ZXMiLCJnZXRNaW51dGVzIiwiaGFuZGxlQXBwb2ludG1lbnRDbGljayIsImFwcG9pbnRtZW50Iiwic3RhdHVzIiwiaWQiLCJwdXNoIiwicGF0aWVudF9pZCIsImVkaXREYXRhIiwiaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfaWQiLCJ0eXBlIiwicHJpY2UiLCJoYW5kbGVBcHBvaW50bWVudFVwZGF0ZSIsImFwcG9pbnRtZW50SWQiLCJuZXdTdGFydCIsIm5ld0VuZCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInRvSVNPU3RyaW5nIiwiaGFuZGxlQXBwb2ludG1lbnRTdWJtaXQiLCJpc0VkaXRpbmciLCJ1cmwiLCJzdWJtaXREYXRhIiwiYXBwb2ludG1lbnRDb3VudHMiLCJ1c2VNZW1vIiwiY291bnRzIiwiZm9yRWFjaCIsImRhdGUiLCJzcGxpdCIsInVwZGF0ZUFwcG9pbnRtZW50U3RhdHVzIiwiaGFuZGxlRGVsZXRlQXBwb2ludG1lbnQiLCJjb25maXJtIiwiaGFuZGxlQ2FuY2VsQXBwb2ludG1lbnQiLCJldmVudCIsInN0b3BQcm9wYWdhdGlvbiIsInBhdGllbnRfbmFtZSIsInBlcmZvcm1DYW5jZWxBcHBvaW50bWVudCIsImhhbmRsZVN0YXJ0Q29uc3VsdGF0aW9uIiwicGVyZm9ybVN0YXJ0Q29uc3VsdGF0aW9uIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJwIiwib25DbGljayIsInZhbHVlIiwib25WYWx1ZUNoYW5nZSIsImRpc2FibGVkIiwic3BhbiIsIm1vZGUiLCJsb2NhbGUiLCJzZWxlY3RlZCIsIm9uU2VsZWN0IiwibGVuZ3RoIiwibWFwIiwidW5kZWZpbmVkIiwiaDMiLCJoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9uYW1lIiwidG9GaXhlZCIsInNpemUiLCJlIiwib25BcHBvaW50bWVudENyZWF0ZSIsIm9uQXBwb2ludG1lbnRDbGljayIsIm9uQXBwb2ludG1lbnRVcGRhdGUiLCJvbk9wZW5DaGFuZ2UiLCJvblN1Ym1pdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});