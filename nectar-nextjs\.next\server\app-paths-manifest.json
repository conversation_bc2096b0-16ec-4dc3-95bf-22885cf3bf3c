{"/api/patients/route": "app/api/patients/route.js", "/api/clinic-settings/route": "app/api/clinic-settings/route.js", "/api/appointments/route": "app/api/appointments/route.js", "/api/user-roles/route": "app/api/user-roles/route.js", "/api/healthcare-professionals/route": "app/api/healthcare-professionals/route.js", "/api/procedures/route": "app/api/procedures/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/patients/[id]/route": "app/api/patients/[id]/route.js", "/api/appointments/[id]/route": "app/api/appointments/[id]/route.js", "/api/medical-records/route": "app/api/medical-records/route.js", "/dashboard/pacientes/page": "app/dashboard/pacientes/page.js", "/dashboard/pacientes/[id]/page": "app/dashboard/pacientes/[id]/page.js", "/dashboard/agenda/page": "app/dashboard/agenda/page.js", "/dashboard/prontuario/[patientId]/page": "app/dashboard/prontuario/[patientId]/page.js", "/dashboard/admin/usuarios/page": "app/dashboard/admin/usuarios/page.js"}