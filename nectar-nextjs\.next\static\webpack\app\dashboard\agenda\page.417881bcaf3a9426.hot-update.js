"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/FullCalendarView.tsx":
/*!*********************************************!*\
  !*** ./src/components/FullCalendarView.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fullcalendar/react */ \"(app-pages-browser)/./node_modules/@fullcalendar/react/dist/index.js\");\n/* harmony import */ var _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fullcalendar/daygrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/daygrid/index.js\");\n/* harmony import */ var _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fullcalendar/timegrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/timegrid/index.js\");\n/* harmony import */ var _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fullcalendar/interaction */ \"(app-pages-browser)/./node_modules/@fullcalendar/interaction/index.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Move getStatusColor outside the component to avoid initialization issues\nconst getStatusColor = (status)=>{\n    const colors = {\n        'scheduled': '#3b82f6',\n        'confirmed': '#10b981',\n        'in_progress': '#f59e0b',\n        'completed': '#6b7280',\n        'cancelled': '#ef4444'\n    };\n    return colors[status] || '#6b7280';\n};\nconst FullCalendarView = (param)=>{\n    let { appointments, healthcareProfessionals, onAppointmentCreate, onAppointmentClick, onAppointmentUpdate, loading = false } = param;\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedProfessional, setSelectedProfessional] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('timeGridWeek');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Initialize component after data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullCalendarView.useEffect\": ()=>{\n            if (!loading && appointments && healthcareProfessionals) {\n                setIsInitialized(true);\n            }\n        }\n    }[\"FullCalendarView.useEffect\"], [\n        loading,\n        appointments,\n        healthcareProfessionals\n    ]);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullCalendarView.useEffect\": ()=>{\n            const checkMobile = {\n                \"FullCalendarView.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    // Auto-switch to day view on mobile\n                    if (window.innerWidth < 768 && currentView === 'timeGridWeek') {\n                        setCurrentView('timeGridDay');\n                    }\n                }\n            }[\"FullCalendarView.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"FullCalendarView.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"FullCalendarView.useEffect\"];\n        }\n    }[\"FullCalendarView.useEffect\"], [\n        currentView\n    ]);\n    // Filter appointments by selected healthcare professional\n    const filteredAppointments = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[filteredAppointments]\": ()=>{\n            if (selectedProfessional === 'all') {\n                return appointments;\n            }\n            return appointments.filter({\n                \"FullCalendarView.useMemo[filteredAppointments]\": (apt)=>apt.healthcare_professional_id === selectedProfessional\n            }[\"FullCalendarView.useMemo[filteredAppointments]\"]);\n        }\n    }[\"FullCalendarView.useMemo[filteredAppointments]\"], [\n        appointments,\n        selectedProfessional\n    ]);\n    // Convert appointments to FullCalendar events\n    const events = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[events]\": ()=>{\n            return filteredAppointments.map({\n                \"FullCalendarView.useMemo[events]\": (appointment)=>({\n                        id: appointment.id,\n                        title: appointment.title,\n                        start: appointment.start_time,\n                        end: appointment.end_time,\n                        backgroundColor: getStatusColor(appointment.status),\n                        borderColor: getStatusColor(appointment.status),\n                        textColor: '#ffffff',\n                        extendedProps: {\n                            appointment,\n                            description: appointment.description,\n                            patientName: appointment.patient_name,\n                            professionalName: appointment.healthcare_professional_name,\n                            status: appointment.status,\n                            type: appointment.type,\n                            price: appointment.price\n                        }\n                    })\n            }[\"FullCalendarView.useMemo[events]\"]);\n        }\n    }[\"FullCalendarView.useMemo[events]\"], [\n        filteredAppointments\n    ]);\n    const handleDateSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleDateSelect]\": (selectInfo)=>{\n            if (onAppointmentCreate) {\n                onAppointmentCreate(selectInfo);\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleDateSelect]\"], [\n        onAppointmentCreate\n    ]);\n    const handleEventClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleEventClick]\": (clickInfo)=>{\n            const appointment = clickInfo.event.extendedProps.appointment;\n            if (onAppointmentClick && appointment) {\n                onAppointmentClick(appointment);\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleEventClick]\"], [\n        onAppointmentClick\n    ]);\n    const handleEventDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleEventDrop]\": async (dropInfo)=>{\n            try {\n                const appointmentId = dropInfo.event.id;\n                const newStart = dropInfo.event.start;\n                const newEnd = dropInfo.event.end;\n                if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {\n                    dropInfo.revert();\n                    return;\n                }\n                await onAppointmentUpdate(appointmentId, newStart, newEnd);\n                toast({\n                    title: \"Sucesso!\",\n                    description: \"Consulta reagendada com sucesso.\"\n                });\n            } catch (error) {\n                console.error('Error updating appointment:', error);\n                dropInfo.revert();\n                toast({\n                    title: \"Erro\",\n                    description: \"Erro ao reagendar consulta.\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleEventDrop]\"], [\n        onAppointmentUpdate,\n        toast\n    ]);\n    const handleViewChange = (view)=>{\n        var _calendarRef_current;\n        setCurrentView(view);\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.changeView(view);\n        }\n    };\n    const goToToday = ()=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.today();\n        }\n    };\n    const navigateCalendar = (direction)=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            if (direction === 'prev') {\n                calendarApi.prev();\n            } else {\n                calendarApi.next();\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Agenda Completa\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 sm:space-y-0 sm:flex sm:flex-wrap sm:justify-between sm:items-center sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                            value: selectedProfessional,\n                                            onValueChange: setSelectedProfessional,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                    className: \"w-full sm:w-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                        placeholder: \"Filtrar por profissional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"Todos os profissionais\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                value: professional.id,\n                                                                children: [\n                                                                    professional.name,\n                                                                    professional.specialty && \" - \".concat(professional.specialty)\n                                                                ]\n                                                            }, professional.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col xs:flex-row gap-2 xs:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'dayGridMonth' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('dayGridMonth'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'M' : 'Mês'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridWeek' ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>handleViewChange('timeGridWeek'),\n                                                    children: \"Semana\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridDay' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('timeGridDay'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'D' : 'Dia'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('prev'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"‹\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: goToToday,\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"Hoje\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('next'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"›\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fullcalendar-container\",\n                    children: !isInitialized || loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-muted-foreground\",\n                                    children: \"Carregando agenda...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ref: calendarRef,\n                        plugins: [\n                            _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                            _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                        ],\n                        initialView: isMobile ? \"timeGridDay\" : \"timeGridWeek\",\n                        headerToolbar: false,\n                        height: \"auto\",\n                        contentHeight: isMobile ? 400 : \"auto\",\n                        events: events || [],\n                        selectable: true,\n                        selectMirror: true,\n                        editable: !isMobile,\n                        droppable: !isMobile,\n                        eventResizableFromStart: !isMobile,\n                        select: handleDateSelect,\n                        eventClick: handleEventClick,\n                        eventDrop: handleEventDrop,\n                        slotMinTime: \"00:00:00\",\n                        slotMaxTime: \"24:00:00\",\n                        slotDuration: \"00:30:00\",\n                        slotLabelInterval: isMobile ? \"02:00:00\" : \"01:00:00\",\n                        allDaySlot: false,\n                        nowIndicator: true,\n                        businessHours: {\n                            daysOfWeek: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ],\n                            startTime: '08:00',\n                            endTime: '18:00'\n                        },\n                        eventDisplay: \"block\",\n                        dayMaxEvents: isMobile ? 3 : true,\n                        moreLinkClick: \"popover\",\n                        // Mobile-specific settings\n                        aspectRatio: isMobile ? 1.2 : 1.35,\n                        handleWindowResize: true,\n                        stickyHeaderDates: !isMobile,\n                        // Locale configuration - Fixed to use string format\n                        locale: \"pt-br\",\n                        buttonText: {\n                            today: 'Hoje',\n                            month: 'Mês',\n                            week: 'Semana',\n                            day: 'Dia',\n                            list: 'Lista'\n                        },\n                        weekText: \"Sm\",\n                        allDayText: \"Todo o dia\",\n                        moreLinkText: \"mais\",\n                        noEventsText: \"N\\xe3o h\\xe1 eventos para mostrar\",\n                        firstDay: 0,\n                        eventContent: (eventInfo)=>{\n                            var _eventInfo_event_extendedProps, _eventInfo_event_extendedProps1;\n                            const title = eventInfo.event.title || '';\n                            const patientName = (_eventInfo_event_extendedProps = eventInfo.event.extendedProps) === null || _eventInfo_event_extendedProps === void 0 ? void 0 : _eventInfo_event_extendedProps.patientName;\n                            const professionalName = (_eventInfo_event_extendedProps1 = eventInfo.event.extendedProps) === null || _eventInfo_event_extendedProps1 === void 0 ? void 0 : _eventInfo_event_extendedProps1.professionalName;\n                            const startTime = eventInfo.event.start;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-1 \".concat(isMobile ? 'text-xs' : 'text-xs'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium truncate text-xs sm:text-sm\",\n                                        children: isMobile && title.length > 20 ? title.substring(0, 20) + '...' : title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    patientName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-90 truncate\",\n                                        children: patientName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    professionalName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: professionalName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 23\n                                    }, void 0),\n                                    isMobile && startTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(startTime, 'HH:mm')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 23\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 19\n                            }, void 0);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n        lineNumber: 194,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FullCalendarView, \"rkLhhgpT0lsjBttF3RmzTu/JmYA=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = FullCalendarView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FullCalendarView);\nvar _c;\n$RefreshReg$(_c, \"FullCalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0Z1bGxDYWxlbmRhclZpZXcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXdFO0FBQ3pCO0FBQ0c7QUFDRTtBQUNNO0FBRXNCO0FBQ2hDO0FBQ3VEO0FBQ2pEO0FBQ1Q7QUFDWDtBQWlDbEMsMkVBQTJFO0FBQzNFLE1BQU11QixpQkFBaUIsQ0FBQ0M7SUFDdEIsTUFBTUMsU0FBUztRQUNiLGFBQWE7UUFDYixhQUFhO1FBQ2IsZUFBZTtRQUNmLGFBQWE7UUFDYixhQUFhO0lBQ2Y7SUFDQSxPQUFPQSxNQUFNLENBQUNELE9BQThCLElBQUk7QUFDbEQ7QUFFQSxNQUFNRSxtQkFBb0Q7UUFBQyxFQUN6REMsWUFBWSxFQUNaQyx1QkFBdUIsRUFDdkJDLG1CQUFtQixFQUNuQkMsa0JBQWtCLEVBQ2xCQyxtQkFBbUIsRUFDbkJDLFVBQVUsS0FBSyxFQUNoQjs7SUFDQyxNQUFNQyxjQUFjaEMsNkNBQU1BLENBQWU7SUFDekMsTUFBTSxDQUFDaUMsc0JBQXNCQyx3QkFBd0IsR0FBR2hDLCtDQUFRQSxDQUFTO0lBQ3pFLE1BQU0sQ0FBQ2lDLGFBQWFDLGVBQWUsR0FBR2xDLCtDQUFRQSxDQUFTO0lBQ3ZELE1BQU0sQ0FBQ21DLFVBQVVDLFlBQVksR0FBR3BDLCtDQUFRQSxDQUFVO0lBQ2xELE1BQU0sQ0FBQ3FDLGVBQWVDLGlCQUFpQixHQUFHdEMsK0NBQVFBLENBQVU7SUFDNUQsTUFBTSxFQUFFdUMsS0FBSyxFQUFFLEdBQUdyQiwwREFBUUE7SUFFMUIsNENBQTRDO0lBQzVDbkIsZ0RBQVNBO3NDQUFDO1lBQ1IsSUFBSSxDQUFDOEIsV0FBV0wsZ0JBQWdCQyx5QkFBeUI7Z0JBQ3ZEYSxpQkFBaUI7WUFDbkI7UUFDRjtxQ0FBRztRQUFDVDtRQUFTTDtRQUFjQztLQUF3QjtJQUVuRCw0QkFBNEI7SUFDNUIxQixnREFBU0E7c0NBQUM7WUFDUixNQUFNeUM7MERBQWM7b0JBQ2xCSixZQUFZSyxPQUFPQyxVQUFVLEdBQUc7b0JBQ2hDLG9DQUFvQztvQkFDcEMsSUFBSUQsT0FBT0MsVUFBVSxHQUFHLE9BQU9ULGdCQUFnQixnQkFBZ0I7d0JBQzdEQyxlQUFlO29CQUNqQjtnQkFDRjs7WUFFQU07WUFDQUMsT0FBT0UsZ0JBQWdCLENBQUMsVUFBVUg7WUFDbEM7OENBQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7O1FBQ3BEO3FDQUFHO1FBQUNQO0tBQVk7SUFFaEIsMERBQTBEO0lBQzFELE1BQU1ZLHVCQUF1QmhELG9EQUFhOzBEQUFDO1lBQ3pDLElBQUlrQyx5QkFBeUIsT0FBTztnQkFDbEMsT0FBT1A7WUFDVDtZQUNBLE9BQU9BLGFBQWF1QixNQUFNO2tFQUFDQyxDQUFBQSxNQUFPQSxJQUFJQywwQkFBMEIsS0FBS2xCOztRQUN2RTt5REFBRztRQUFDUDtRQUFjTztLQUFxQjtJQUV2Qyw4Q0FBOEM7SUFDOUMsTUFBTW1CLFNBQXVCckQsb0RBQWE7NENBQUM7WUFDekMsT0FBT2dELHFCQUFxQk0sR0FBRztvREFBQ0MsQ0FBQUEsY0FBZ0I7d0JBQzlDQyxJQUFJRCxZQUFZQyxFQUFFO3dCQUNsQkMsT0FBT0YsWUFBWUUsS0FBSzt3QkFDeEJDLE9BQU9ILFlBQVlJLFVBQVU7d0JBQzdCQyxLQUFLTCxZQUFZTSxRQUFRO3dCQUN6QkMsaUJBQWlCdkMsZUFBZWdDLFlBQVkvQixNQUFNO3dCQUNsRHVDLGFBQWF4QyxlQUFlZ0MsWUFBWS9CLE1BQU07d0JBQzlDd0MsV0FBVzt3QkFDWEMsZUFBZTs0QkFDYlY7NEJBQ0FXLGFBQWFYLFlBQVlXLFdBQVc7NEJBQ3BDQyxhQUFhWixZQUFZYSxZQUFZOzRCQUNyQ0Msa0JBQWtCZCxZQUFZZSw0QkFBNEI7NEJBQzFEOUMsUUFBUStCLFlBQVkvQixNQUFNOzRCQUMxQitDLE1BQU1oQixZQUFZZ0IsSUFBSTs0QkFDdEJDLE9BQU9qQixZQUFZaUIsS0FBSzt3QkFDMUI7b0JBQ0Y7O1FBQ0Y7MkNBQUc7UUFBQ3hCO0tBQXFCO0lBRXpCLE1BQU15QixtQkFBbUJyRSxrREFBV0E7MERBQUMsQ0FBQ3NFO1lBQ3BDLElBQUk3QyxxQkFBcUI7Z0JBQ3ZCQSxvQkFBb0I2QztZQUN0QjtRQUNGO3lEQUFHO1FBQUM3QztLQUFvQjtJQUV4QixNQUFNOEMsbUJBQW1CdkUsa0RBQVdBOzBEQUFDLENBQUN3RTtZQUNwQyxNQUFNckIsY0FBY3FCLFVBQVVDLEtBQUssQ0FBQ1osYUFBYSxDQUFDVixXQUFXO1lBQzdELElBQUl6QixzQkFBc0J5QixhQUFhO2dCQUNyQ3pCLG1CQUFtQnlCO1lBQ3JCO1FBQ0Y7eURBQUc7UUFBQ3pCO0tBQW1CO0lBRXZCLE1BQU1nRCxrQkFBa0IxRSxrREFBV0E7eURBQUMsT0FBTzJFO1lBQ3pDLElBQUk7Z0JBQ0YsTUFBTUMsZ0JBQWdCRCxTQUFTRixLQUFLLENBQUNyQixFQUFFO2dCQUN2QyxNQUFNeUIsV0FBV0YsU0FBU0YsS0FBSyxDQUFDbkIsS0FBSztnQkFDckMsTUFBTXdCLFNBQVNILFNBQVNGLEtBQUssQ0FBQ2pCLEdBQUc7Z0JBRWpDLElBQUksQ0FBQ29CLGlCQUFpQixDQUFDQyxZQUFZLENBQUNDLFVBQVUsQ0FBQ25ELHFCQUFxQjtvQkFDbEVnRCxTQUFTSSxNQUFNO29CQUNmO2dCQUNGO2dCQUVBLE1BQU1wRCxvQkFBb0JpRCxlQUFlQyxVQUFVQztnQkFFbkR4QyxNQUFNO29CQUNKZSxPQUFPO29CQUNQUyxhQUFhO2dCQUNmO1lBQ0YsRUFBRSxPQUFPa0IsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7Z0JBQzdDTCxTQUFTSSxNQUFNO2dCQUNmekMsTUFBTTtvQkFDSmUsT0FBTztvQkFDUFMsYUFBYTtvQkFDYm9CLFNBQVM7Z0JBQ1g7WUFDRjtRQUNGO3dEQUFHO1FBQUN2RDtRQUFxQlc7S0FBTTtJQUUvQixNQUFNNkMsbUJBQW1CLENBQUNDO1lBRUp2RDtRQURwQkksZUFBZW1EO1FBQ2YsTUFBTUMsZUFBY3hELHVCQUFBQSxZQUFZeUQsT0FBTyxjQUFuQnpELDJDQUFBQSxxQkFBcUIwRCxNQUFNO1FBQy9DLElBQUlGLGFBQWE7WUFDZkEsWUFBWUcsVUFBVSxDQUFDSjtRQUN6QjtJQUNGO0lBRUEsTUFBTUssWUFBWTtZQUNJNUQ7UUFBcEIsTUFBTXdELGVBQWN4RCx1QkFBQUEsWUFBWXlELE9BQU8sY0FBbkJ6RCwyQ0FBQUEscUJBQXFCMEQsTUFBTTtRQUMvQyxJQUFJRixhQUFhO1lBQ2ZBLFlBQVlLLEtBQUs7UUFDbkI7SUFDRjtJQUVBLE1BQU1DLG1CQUFtQixDQUFDQztZQUNKL0Q7UUFBcEIsTUFBTXdELGVBQWN4RCx1QkFBQUEsWUFBWXlELE9BQU8sY0FBbkJ6RCwyQ0FBQUEscUJBQXFCMEQsTUFBTTtRQUMvQyxJQUFJRixhQUFhO1lBQ2YsSUFBSU8sY0FBYyxRQUFRO2dCQUN4QlAsWUFBWVEsSUFBSTtZQUNsQixPQUFPO2dCQUNMUixZQUFZUyxJQUFJO1lBQ2xCO1FBQ0Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDekYscURBQUlBO1FBQUMwRixXQUFVOzswQkFDZCw4REFBQ3hGLDJEQUFVQTswQkFDVCw0RUFBQ3lGO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ3ZGLDBEQUFTQTs0QkFBQ3VGLFdBQVU7OzhDQUNuQiw4REFBQ2hGLDJGQUFRQTtvQ0FBQ2dGLFdBQVU7Ozs7OztnQ0FBOEI7Ozs7Ozs7c0NBS3BELDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBRWIsOERBQUNDO29DQUFJRCxXQUFVOztzREFDYiw4REFBQy9FLDJGQUFNQTs0Q0FBQytFLFdBQVU7Ozs7OztzREFDbEIsOERBQUNyRix5REFBTUE7NENBQUN1RixPQUFPbkU7NENBQXNCb0UsZUFBZW5FOzs4REFDbEQsOERBQUNsQixnRUFBYUE7b0RBQUNrRixXQUFVOzhEQUN2Qiw0RUFBQ2pGLDhEQUFXQTt3REFBQ3FGLGFBQVk7Ozs7Ozs7Ozs7OzhEQUUzQiw4REFBQ3hGLGdFQUFhQTs7c0VBQ1osOERBQUNDLDZEQUFVQTs0REFBQ3FGLE9BQU07c0VBQU07Ozs7Ozt3REFDdkJ6RSx3QkFDRXNCLE1BQU0sQ0FBQ3NELENBQUFBLE9BQVFBLEtBQUtDLFNBQVMsRUFDN0JuRCxHQUFHLENBQUNvRCxDQUFBQSw2QkFDSCw4REFBQzFGLDZEQUFVQTtnRUFBdUJxRixPQUFPSyxhQUFhbEQsRUFBRTs7b0VBQ3JEa0QsYUFBYUMsSUFBSTtvRUFDakJELGFBQWFFLFNBQVMsSUFBSSxNQUE2QixPQUF2QkYsYUFBYUUsU0FBUzs7K0RBRnhDRixhQUFhbEQsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBUzFDLDhEQUFDNEM7b0NBQUlELFdBQVU7O3NEQUViLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUN0Rix5REFBTUE7b0RBQ0x5RSxTQUFTbEQsZ0JBQWdCLGlCQUFpQixZQUFZO29EQUN0RHlFLE1BQU12RSxXQUFXLE9BQU87b0RBQ3hCd0UsU0FBUyxJQUFNdkIsaUJBQWlCO29EQUNoQ1ksV0FBVTs4REFFVDdELFdBQVcsTUFBTTs7Ozs7O2dEQUVuQixDQUFDQSwwQkFDQSw4REFBQ3pCLHlEQUFNQTtvREFDTHlFLFNBQVNsRCxnQkFBZ0IsaUJBQWlCLFlBQVk7b0RBQ3REeUUsTUFBSztvREFDTEMsU0FBUyxJQUFNdkIsaUJBQWlCOzhEQUNqQzs7Ozs7OzhEQUlILDhEQUFDMUUseURBQU1BO29EQUNMeUUsU0FBU2xELGdCQUFnQixnQkFBZ0IsWUFBWTtvREFDckR5RSxNQUFNdkUsV0FBVyxPQUFPO29EQUN4QndFLFNBQVMsSUFBTXZCLGlCQUFpQjtvREFDaENZLFdBQVU7OERBRVQ3RCxXQUFXLE1BQU07Ozs7Ozs7Ozs7OztzREFLdEIsOERBQUM4RDs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUN0Rix5REFBTUE7b0RBQ0x5RSxTQUFRO29EQUNSdUIsTUFBSztvREFDTEMsU0FBUyxJQUFNZixpQkFBaUI7b0RBQ2hDSSxXQUFVOzhEQUNYOzs7Ozs7OERBR0QsOERBQUN0Rix5REFBTUE7b0RBQ0x5RSxTQUFRO29EQUNSdUIsTUFBSztvREFDTEMsU0FBU2pCO29EQUNUTSxXQUFVOzhEQUNYOzs7Ozs7OERBR0QsOERBQUN0Rix5REFBTUE7b0RBQ0x5RSxTQUFRO29EQUNSdUIsTUFBSztvREFDTEMsU0FBUyxJQUFNZixpQkFBaUI7b0RBQ2hDSSxXQUFVOzhEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTWCw4REFBQ3pGLDREQUFXQTswQkFDViw0RUFBQzBGO29CQUFJRCxXQUFVOzhCQUNaLENBQUMzRCxpQkFBaUJSLHdCQUNqQiw4REFBQ29FO3dCQUFJRCxXQUFVO2tDQUNiLDRFQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNDO29DQUFJRCxXQUFVOzs7Ozs7OENBQ2YsOERBQUNZO29DQUFFWixXQUFVOzhDQUE2Qjs7Ozs7Ozs7Ozs7Ozs7OztrREFJOUMsOERBQUM5RiwyREFBWUE7d0JBQ1gyRyxLQUFLL0U7d0JBQ0xnRixTQUFTOzRCQUFDM0csNkRBQWFBOzRCQUFFQywrREFBY0E7NEJBQUVDLGtFQUFpQkE7eUJBQUM7d0JBQzNEMEcsYUFBYTVFLFdBQVcsZ0JBQWdCO3dCQUN4QzZFLGVBQWU7d0JBQ2ZDLFFBQU87d0JBQ1BDLGVBQWUvRSxXQUFXLE1BQU07d0JBQ2hDZSxRQUFRQSxVQUFVLEVBQUU7d0JBQ3BCaUUsWUFBWTt3QkFDWkMsY0FBYzt3QkFDZEMsVUFBVSxDQUFDbEY7d0JBQ1htRixXQUFXLENBQUNuRjt3QkFDWm9GLHlCQUF5QixDQUFDcEY7d0JBQzFCcUYsUUFBUWxEO3dCQUNSbUQsWUFBWWpEO3dCQUNaa0QsV0FBVy9DO3dCQUNYZ0QsYUFBWTt3QkFDWkMsYUFBWTt3QkFDWkMsY0FBYTt3QkFDYkMsbUJBQW1CM0YsV0FBVyxhQUFhO3dCQUMzQzRGLFlBQVk7d0JBQ1pDLGNBQWM7d0JBQ2RDLGVBQWU7NEJBQ2JDLFlBQVk7Z0NBQUM7Z0NBQUc7Z0NBQUc7Z0NBQUc7Z0NBQUc7NkJBQUU7NEJBQzNCQyxXQUFXOzRCQUNYQyxTQUFTO3dCQUNYO3dCQUNBQyxjQUFhO3dCQUNiQyxjQUFjbkcsV0FBVyxJQUFJO3dCQUM3Qm9HLGVBQWM7d0JBQ2QsMkJBQTJCO3dCQUMzQkMsYUFBYXJHLFdBQVcsTUFBTTt3QkFDOUJzRyxvQkFBb0I7d0JBQ3BCQyxtQkFBbUIsQ0FBQ3ZHO3dCQUNwQixvREFBb0Q7d0JBQ3BEd0csUUFBTzt3QkFDUEMsWUFBWTs0QkFDVmpELE9BQU87NEJBQ1BrRCxPQUFPOzRCQUNQQyxNQUFNOzRCQUNOQyxLQUFLOzRCQUNMQyxNQUFNO3dCQUNSO3dCQUNBQyxVQUFTO3dCQUNUQyxZQUFXO3dCQUNYQyxjQUFhO3dCQUNiQyxjQUFhO3dCQUNiQyxVQUFVO3dCQUNWQyxjQUFjLENBQUNDO2dDQUVPQSxnQ0FDS0E7NEJBRnpCLE1BQU1qRyxRQUFRaUcsVUFBVTdFLEtBQUssQ0FBQ3BCLEtBQUssSUFBSTs0QkFDdkMsTUFBTVUsZUFBY3VGLGlDQUFBQSxVQUFVN0UsS0FBSyxDQUFDWixhQUFhLGNBQTdCeUYscURBQUFBLCtCQUErQnZGLFdBQVc7NEJBQzlELE1BQU1FLG9CQUFtQnFGLGtDQUFBQSxVQUFVN0UsS0FBSyxDQUFDWixhQUFhLGNBQTdCeUYsc0RBQUFBLGdDQUErQnJGLGdCQUFnQjs0QkFDeEUsTUFBTWlFLFlBQVlvQixVQUFVN0UsS0FBSyxDQUFDbkIsS0FBSzs0QkFFdkMscUJBQ0UsOERBQUMwQztnQ0FBSUQsV0FBVyxPQUF3QyxPQUFqQzdELFdBQVcsWUFBWTs7a0RBQzVDLDhEQUFDOEQ7d0NBQUlELFdBQVU7a0RBQ1o3RCxZQUFZbUIsTUFBTWtHLE1BQU0sR0FBRyxLQUFLbEcsTUFBTW1HLFNBQVMsQ0FBQyxHQUFHLE1BQU0sUUFBUW5HOzs7Ozs7b0NBRW5FVSxlQUFlLENBQUM3QiwwQkFDZiw4REFBQzhEO3dDQUFJRCxXQUFVO2tEQUNaaEM7Ozs7OztvQ0FHSkUsb0JBQW9CLENBQUMvQiwwQkFDcEIsOERBQUM4RDt3Q0FBSUQsV0FBVTtrREFDWjlCOzs7Ozs7b0NBR0ovQixZQUFZZ0csMkJBQ1gsOERBQUNsQzt3Q0FBSUQsV0FBVTtrREFDWjdFLCtFQUFNQSxDQUFDZ0gsV0FBVzs7Ozs7Ozs7Ozs7O3dCQUs3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9kO0dBalVNNUc7O1FBYWNMLHNEQUFRQTs7O0tBYnRCSztBQW1VTixpRUFBZUEsZ0JBQWdCQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNpcm92XFxEb2N1bWVudHNcXG5leHQtanNcXG5lY3RhclxcbmVjdGFyLW5leHRqc1xcc3JjXFxjb21wb25lbnRzXFxGdWxsQ2FsZW5kYXJWaWV3LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QsIHsgdXNlUmVmLCB1c2VFZmZlY3QsIHVzZVN0YXRlLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBGdWxsQ2FsZW5kYXIgZnJvbSAnQGZ1bGxjYWxlbmRhci9yZWFjdCc7XG5pbXBvcnQgZGF5R3JpZFBsdWdpbiBmcm9tICdAZnVsbGNhbGVuZGFyL2RheWdyaWQnO1xuaW1wb3J0IHRpbWVHcmlkUGx1Z2luIGZyb20gJ0BmdWxsY2FsZW5kYXIvdGltZWdyaWQnO1xuaW1wb3J0IGludGVyYWN0aW9uUGx1Z2luIGZyb20gJ0BmdWxsY2FsZW5kYXIvaW50ZXJhY3Rpb24nO1xuaW1wb3J0IHsgRXZlbnRJbnB1dCwgRGF0ZVNlbGVjdEFyZywgRXZlbnRDbGlja0FyZywgRXZlbnREcm9wQXJnLCBFdmVudFJlc2l6ZURvbmVBcmcgfSBmcm9tICdAZnVsbGNhbGVuZGFyL2NvcmUnO1xuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zZWxlY3QnO1xuaW1wb3J0IHsgQ2FsZW5kYXIsIEZpbHRlciwgUGx1cyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0JztcbmltcG9ydCB7IGZvcm1hdCB9IGZyb20gJ2RhdGUtZm5zJztcblxudHlwZSBBcHBvaW50bWVudCA9IHtcbiAgaWQ6IHN0cmluZztcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZyB8IG51bGw7XG4gIHBhdGllbnRfaWQ6IHN0cmluZztcbiAgcGF0aWVudF9uYW1lPzogc3RyaW5nO1xuICBoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9pZDogc3RyaW5nIHwgbnVsbDtcbiAgaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfbmFtZT86IHN0cmluZztcbiAgc3RhcnRfdGltZTogc3RyaW5nO1xuICBlbmRfdGltZTogc3RyaW5nO1xuICB0eXBlOiBzdHJpbmc7XG4gIHN0YXR1czogc3RyaW5nO1xuICBwcmljZTogbnVtYmVyIHwgbnVsbDtcbn07XG5cbnR5cGUgSGVhbHRoY2FyZVByb2Zlc3Npb25hbCA9IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBzcGVjaWFsdHk6IHN0cmluZyB8IG51bGw7XG4gIGlzX2FjdGl2ZTogYm9vbGVhbjtcbn07XG5cbmludGVyZmFjZSBGdWxsQ2FsZW5kYXJWaWV3UHJvcHMge1xuICBhcHBvaW50bWVudHM6IEFwcG9pbnRtZW50W107XG4gIGhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzOiBIZWFsdGhjYXJlUHJvZmVzc2lvbmFsW107XG4gIG9uQXBwb2ludG1lbnRDcmVhdGU6IChzZWxlY3RJbmZvOiBEYXRlU2VsZWN0QXJnKSA9PiB2b2lkO1xuICBvbkFwcG9pbnRtZW50Q2xpY2s6IChhcHBvaW50bWVudDogQXBwb2ludG1lbnQpID0+IHZvaWQ7XG4gIG9uQXBwb2ludG1lbnRVcGRhdGU6IChhcHBvaW50bWVudElkOiBzdHJpbmcsIG5ld1N0YXJ0OiBEYXRlLCBuZXdFbmQ6IERhdGUpID0+IFByb21pc2U8dm9pZD47XG4gIGxvYWRpbmc/OiBib29sZWFuO1xufVxuXG4vLyBNb3ZlIGdldFN0YXR1c0NvbG9yIG91dHNpZGUgdGhlIGNvbXBvbmVudCB0byBhdm9pZCBpbml0aWFsaXphdGlvbiBpc3N1ZXNcbmNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgY29uc3QgY29sb3JzID0ge1xuICAgICdzY2hlZHVsZWQnOiAnIzNiODJmNicsIC8vIGJsdWVcbiAgICAnY29uZmlybWVkJzogJyMxMGI5ODEnLCAvLyBncmVlblxuICAgICdpbl9wcm9ncmVzcyc6ICcjZjU5ZTBiJywgLy8gYW1iZXJcbiAgICAnY29tcGxldGVkJzogJyM2YjcyODAnLCAvLyBncmF5XG4gICAgJ2NhbmNlbGxlZCc6ICcjZWY0NDQ0JywgLy8gcmVkXG4gIH07XG4gIHJldHVybiBjb2xvcnNbc3RhdHVzIGFzIGtleW9mIHR5cGVvZiBjb2xvcnNdIHx8ICcjNmI3MjgwJztcbn07XG5cbmNvbnN0IEZ1bGxDYWxlbmRhclZpZXc6IFJlYWN0LkZDPEZ1bGxDYWxlbmRhclZpZXdQcm9wcz4gPSAoe1xuICBhcHBvaW50bWVudHMsXG4gIGhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzLFxuICBvbkFwcG9pbnRtZW50Q3JlYXRlLFxuICBvbkFwcG9pbnRtZW50Q2xpY2ssXG4gIG9uQXBwb2ludG1lbnRVcGRhdGUsXG4gIGxvYWRpbmcgPSBmYWxzZVxufSkgPT4ge1xuICBjb25zdCBjYWxlbmRhclJlZiA9IHVzZVJlZjxGdWxsQ2FsZW5kYXI+KG51bGwpO1xuICBjb25zdCBbc2VsZWN0ZWRQcm9mZXNzaW9uYWwsIHNldFNlbGVjdGVkUHJvZmVzc2lvbmFsXSA9IHVzZVN0YXRlPHN0cmluZz4oJ2FsbCcpO1xuICBjb25zdCBbY3VycmVudFZpZXcsIHNldEN1cnJlbnRWaWV3XSA9IHVzZVN0YXRlPHN0cmluZz4oJ3RpbWVHcmlkV2VlaycpO1xuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcbiAgY29uc3QgW2lzSW5pdGlhbGl6ZWQsIHNldElzSW5pdGlhbGl6ZWRdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpO1xuXG4gIC8vIEluaXRpYWxpemUgY29tcG9uZW50IGFmdGVyIGRhdGEgaXMgbG9hZGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFsb2FkaW5nICYmIGFwcG9pbnRtZW50cyAmJiBoZWFsdGhjYXJlUHJvZmVzc2lvbmFscykge1xuICAgICAgc2V0SXNJbml0aWFsaXplZCh0cnVlKTtcbiAgICB9XG4gIH0sIFtsb2FkaW5nLCBhcHBvaW50bWVudHMsIGhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzXSk7XG5cbiAgLy8gRGV0ZWN0IG1vYmlsZSBzY3JlZW4gc2l6ZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGNoZWNrTW9iaWxlID0gKCkgPT4ge1xuICAgICAgc2V0SXNNb2JpbGUod2luZG93LmlubmVyV2lkdGggPCA3NjgpO1xuICAgICAgLy8gQXV0by1zd2l0Y2ggdG8gZGF5IHZpZXcgb24gbW9iaWxlXG4gICAgICBpZiAod2luZG93LmlubmVyV2lkdGggPCA3NjggJiYgY3VycmVudFZpZXcgPT09ICd0aW1lR3JpZFdlZWsnKSB7XG4gICAgICAgIHNldEN1cnJlbnRWaWV3KCd0aW1lR3JpZERheScpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBjaGVja01vYmlsZSgpO1xuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XG4gIH0sIFtjdXJyZW50Vmlld10pO1xuXG4gIC8vIEZpbHRlciBhcHBvaW50bWVudHMgYnkgc2VsZWN0ZWQgaGVhbHRoY2FyZSBwcm9mZXNzaW9uYWxcbiAgY29uc3QgZmlsdGVyZWRBcHBvaW50bWVudHMgPSBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoc2VsZWN0ZWRQcm9mZXNzaW9uYWwgPT09ICdhbGwnKSB7XG4gICAgICByZXR1cm4gYXBwb2ludG1lbnRzO1xuICAgIH1cbiAgICByZXR1cm4gYXBwb2ludG1lbnRzLmZpbHRlcihhcHQgPT4gYXB0LmhlYWx0aGNhcmVfcHJvZmVzc2lvbmFsX2lkID09PSBzZWxlY3RlZFByb2Zlc3Npb25hbCk7XG4gIH0sIFthcHBvaW50bWVudHMsIHNlbGVjdGVkUHJvZmVzc2lvbmFsXSk7XG5cbiAgLy8gQ29udmVydCBhcHBvaW50bWVudHMgdG8gRnVsbENhbGVuZGFyIGV2ZW50c1xuICBjb25zdCBldmVudHM6IEV2ZW50SW5wdXRbXSA9IFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIHJldHVybiBmaWx0ZXJlZEFwcG9pbnRtZW50cy5tYXAoYXBwb2ludG1lbnQgPT4gKHtcbiAgICAgIGlkOiBhcHBvaW50bWVudC5pZCxcbiAgICAgIHRpdGxlOiBhcHBvaW50bWVudC50aXRsZSxcbiAgICAgIHN0YXJ0OiBhcHBvaW50bWVudC5zdGFydF90aW1lLFxuICAgICAgZW5kOiBhcHBvaW50bWVudC5lbmRfdGltZSxcbiAgICAgIGJhY2tncm91bmRDb2xvcjogZ2V0U3RhdHVzQ29sb3IoYXBwb2ludG1lbnQuc3RhdHVzKSxcbiAgICAgIGJvcmRlckNvbG9yOiBnZXRTdGF0dXNDb2xvcihhcHBvaW50bWVudC5zdGF0dXMpLFxuICAgICAgdGV4dENvbG9yOiAnI2ZmZmZmZicsXG4gICAgICBleHRlbmRlZFByb3BzOiB7XG4gICAgICAgIGFwcG9pbnRtZW50LFxuICAgICAgICBkZXNjcmlwdGlvbjogYXBwb2ludG1lbnQuZGVzY3JpcHRpb24sXG4gICAgICAgIHBhdGllbnROYW1lOiBhcHBvaW50bWVudC5wYXRpZW50X25hbWUsXG4gICAgICAgIHByb2Zlc3Npb25hbE5hbWU6IGFwcG9pbnRtZW50LmhlYWx0aGNhcmVfcHJvZmVzc2lvbmFsX25hbWUsXG4gICAgICAgIHN0YXR1czogYXBwb2ludG1lbnQuc3RhdHVzLFxuICAgICAgICB0eXBlOiBhcHBvaW50bWVudC50eXBlLFxuICAgICAgICBwcmljZTogYXBwb2ludG1lbnQucHJpY2VcbiAgICAgIH1cbiAgICB9KSk7XG4gIH0sIFtmaWx0ZXJlZEFwcG9pbnRtZW50c10pO1xuXG4gIGNvbnN0IGhhbmRsZURhdGVTZWxlY3QgPSB1c2VDYWxsYmFjaygoc2VsZWN0SW5mbzogRGF0ZVNlbGVjdEFyZykgPT4ge1xuICAgIGlmIChvbkFwcG9pbnRtZW50Q3JlYXRlKSB7XG4gICAgICBvbkFwcG9pbnRtZW50Q3JlYXRlKHNlbGVjdEluZm8pO1xuICAgIH1cbiAgfSwgW29uQXBwb2ludG1lbnRDcmVhdGVdKTtcblxuICBjb25zdCBoYW5kbGVFdmVudENsaWNrID0gdXNlQ2FsbGJhY2soKGNsaWNrSW5mbzogRXZlbnRDbGlja0FyZykgPT4ge1xuICAgIGNvbnN0IGFwcG9pbnRtZW50ID0gY2xpY2tJbmZvLmV2ZW50LmV4dGVuZGVkUHJvcHMuYXBwb2ludG1lbnQgYXMgQXBwb2ludG1lbnQ7XG4gICAgaWYgKG9uQXBwb2ludG1lbnRDbGljayAmJiBhcHBvaW50bWVudCkge1xuICAgICAgb25BcHBvaW50bWVudENsaWNrKGFwcG9pbnRtZW50KTtcbiAgICB9XG4gIH0sIFtvbkFwcG9pbnRtZW50Q2xpY2tdKTtcblxuICBjb25zdCBoYW5kbGVFdmVudERyb3AgPSB1c2VDYWxsYmFjayhhc3luYyAoZHJvcEluZm86IEV2ZW50RHJvcEFyZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBhcHBvaW50bWVudElkID0gZHJvcEluZm8uZXZlbnQuaWQ7XG4gICAgICBjb25zdCBuZXdTdGFydCA9IGRyb3BJbmZvLmV2ZW50LnN0YXJ0O1xuICAgICAgY29uc3QgbmV3RW5kID0gZHJvcEluZm8uZXZlbnQuZW5kO1xuXG4gICAgICBpZiAoIWFwcG9pbnRtZW50SWQgfHwgIW5ld1N0YXJ0IHx8ICFuZXdFbmQgfHwgIW9uQXBwb2ludG1lbnRVcGRhdGUpIHtcbiAgICAgICAgZHJvcEluZm8ucmV2ZXJ0KCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgYXdhaXQgb25BcHBvaW50bWVudFVwZGF0ZShhcHBvaW50bWVudElkLCBuZXdTdGFydCwgbmV3RW5kKTtcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJTdWNlc3NvIVwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJDb25zdWx0YSByZWFnZW5kYWRhIGNvbSBzdWNlc3NvLlwiLFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGFwcG9pbnRtZW50OicsIGVycm9yKTtcbiAgICAgIGRyb3BJbmZvLnJldmVydCgpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcnJvXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkVycm8gYW8gcmVhZ2VuZGFyIGNvbnN1bHRhLlwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCJcbiAgICAgIH0pO1xuICAgIH1cbiAgfSwgW29uQXBwb2ludG1lbnRVcGRhdGUsIHRvYXN0XSk7XG5cbiAgY29uc3QgaGFuZGxlVmlld0NoYW5nZSA9ICh2aWV3OiBzdHJpbmcpID0+IHtcbiAgICBzZXRDdXJyZW50Vmlldyh2aWV3KTtcbiAgICBjb25zdCBjYWxlbmRhckFwaSA9IGNhbGVuZGFyUmVmLmN1cnJlbnQ/LmdldEFwaSgpO1xuICAgIGlmIChjYWxlbmRhckFwaSkge1xuICAgICAgY2FsZW5kYXJBcGkuY2hhbmdlVmlldyh2aWV3KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ29Ub1RvZGF5ID0gKCkgPT4ge1xuICAgIGNvbnN0IGNhbGVuZGFyQXBpID0gY2FsZW5kYXJSZWYuY3VycmVudD8uZ2V0QXBpKCk7XG4gICAgaWYgKGNhbGVuZGFyQXBpKSB7XG4gICAgICBjYWxlbmRhckFwaS50b2RheSgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBuYXZpZ2F0ZUNhbGVuZGFyID0gKGRpcmVjdGlvbjogJ3ByZXYnIHwgJ25leHQnKSA9PiB7XG4gICAgY29uc3QgY2FsZW5kYXJBcGkgPSBjYWxlbmRhclJlZi5jdXJyZW50Py5nZXRBcGkoKTtcbiAgICBpZiAoY2FsZW5kYXJBcGkpIHtcbiAgICAgIGlmIChkaXJlY3Rpb24gPT09ICdwcmV2Jykge1xuICAgICAgICBjYWxlbmRhckFwaS5wcmV2KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjYWxlbmRhckFwaS5uZXh0KCk7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPENhcmQgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICA8Q2FsZW5kYXIgY2xhc3NOYW1lPVwibXItMiBoLTUgdy01IHRleHQtcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICBBZ2VuZGEgQ29tcGxldGFcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cblxuICAgICAgICAgIHsvKiBNb2JpbGUtZmlyc3QgcmVzcG9uc2l2ZSBjb250cm9scyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBzbTpzcGFjZS15LTAgc206ZmxleCBzbTpmbGV4LXdyYXAgc206anVzdGlmeS1iZXR3ZWVuIHNtOml0ZW1zLWNlbnRlciBzbTpnYXAtNFwiPlxuICAgICAgICAgICAgey8qIEhlYWx0aGNhcmUgUHJvZmVzc2lvbmFsIEZpbHRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgbWluLXctMCBmbGV4LTEgc206ZmxleC1pbml0aWFsXCI+XG4gICAgICAgICAgICAgIDxGaWx0ZXIgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmQgZmxleC1zaHJpbmstMFwiIC8+XG4gICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3NlbGVjdGVkUHJvZmVzc2lvbmFsfSBvblZhbHVlQ2hhbmdlPXtzZXRTZWxlY3RlZFByb2Zlc3Npb25hbH0+XG4gICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwidy1mdWxsIHNtOnctWzIwMHB4XVwiPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIHBsYWNlaG9sZGVyPVwiRmlsdHJhciBwb3IgcHJvZmlzc2lvbmFsXCIgLz5cbiAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cImFsbFwiPlRvZG9zIG9zIHByb2Zpc3Npb25haXM8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICB7aGVhbHRoY2FyZVByb2Zlc3Npb25hbHNcbiAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihwcm9mID0+IHByb2YuaXNfYWN0aXZlKVxuICAgICAgICAgICAgICAgICAgICAubWFwKHByb2Zlc3Npb25hbCA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXtwcm9mZXNzaW9uYWwuaWR9IHZhbHVlPXtwcm9mZXNzaW9uYWwuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3Byb2Zlc3Npb25hbC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAge3Byb2Zlc3Npb25hbC5zcGVjaWFsdHkgJiYgYCAtICR7cHJvZmVzc2lvbmFsLnNwZWNpYWx0eX1gfVxuICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgeHM6ZmxleC1yb3cgZ2FwLTIgeHM6Z2FwLTRcIj5cbiAgICAgICAgICAgICAgey8qIFZpZXcgQ29udHJvbHMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PXtjdXJyZW50VmlldyA9PT0gJ2RheUdyaWRNb250aCcgPyAnZGVmYXVsdCcgOiAnb3V0bGluZSd9XG4gICAgICAgICAgICAgICAgICBzaXplPXtpc01vYmlsZSA/ICdzbScgOiAnc20nfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0NoYW5nZSgnZGF5R3JpZE1vbnRoJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgeHM6ZmxleC1pbml0aWFsXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXNNb2JpbGUgPyAnTScgOiAnTcOqcyd9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgeyFpc01vYmlsZSAmJiAoXG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2N1cnJlbnRWaWV3ID09PSAndGltZUdyaWRXZWVrJyA/ICdkZWZhdWx0JyA6ICdvdXRsaW5lJ31cbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0NoYW5nZSgndGltZUdyaWRXZWVrJyl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIFNlbWFuYVxuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PXtjdXJyZW50VmlldyA9PT0gJ3RpbWVHcmlkRGF5JyA/ICdkZWZhdWx0JyA6ICdvdXRsaW5lJ31cbiAgICAgICAgICAgICAgICAgIHNpemU9e2lzTW9iaWxlID8gJ3NtJyA6ICdzbSd9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVWaWV3Q2hhbmdlKCd0aW1lR3JpZERheScpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHhzOmZsZXgtaW5pdGlhbFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAge2lzTW9iaWxlID8gJ0QnIDogJ0RpYSd9XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBOYXZpZ2F0aW9uIENvbnRyb2xzICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0xXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG5hdmlnYXRlQ2FsZW5kYXIoJ3ByZXYnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSB4czpmbGV4LWluaXRpYWxcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOKAuVxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtnb1RvVG9kYXl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgeHM6ZmxleC1pbml0aWFsXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBIb2plXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG5hdmlnYXRlQ2FsZW5kYXIoJ25leHQnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSB4czpmbGV4LWluaXRpYWxcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIOKAulxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgIFxuICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZ1bGxjYWxlbmRhci1jb250YWluZXJcIj5cbiAgICAgICAgICB7IWlzSW5pdGlhbGl6ZWQgfHwgbG9hZGluZyA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC02NFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItcHJpbWFyeSBteC1hdXRvXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibXQtMiB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5DYXJyZWdhbmRvIGFnZW5kYS4uLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPEZ1bGxDYWxlbmRhclxuICAgICAgICAgICAgICByZWY9e2NhbGVuZGFyUmVmfVxuICAgICAgICAgICAgICBwbHVnaW5zPXtbZGF5R3JpZFBsdWdpbiwgdGltZUdyaWRQbHVnaW4sIGludGVyYWN0aW9uUGx1Z2luXX1cbiAgICAgICAgICAgICAgaW5pdGlhbFZpZXc9e2lzTW9iaWxlID8gXCJ0aW1lR3JpZERheVwiIDogXCJ0aW1lR3JpZFdlZWtcIn1cbiAgICAgICAgICAgICAgaGVhZGVyVG9vbGJhcj17ZmFsc2V9IC8vIFdlJ3JlIHVzaW5nIGN1c3RvbSBoZWFkZXJcbiAgICAgICAgICAgICAgaGVpZ2h0PVwiYXV0b1wiXG4gICAgICAgICAgICAgIGNvbnRlbnRIZWlnaHQ9e2lzTW9iaWxlID8gNDAwIDogXCJhdXRvXCJ9XG4gICAgICAgICAgICAgIGV2ZW50cz17ZXZlbnRzIHx8IFtdfVxuICAgICAgICAgICAgICBzZWxlY3RhYmxlPXt0cnVlfVxuICAgICAgICAgICAgICBzZWxlY3RNaXJyb3I9e3RydWV9XG4gICAgICAgICAgICAgIGVkaXRhYmxlPXshaXNNb2JpbGV9IC8vIERpc2FibGUgZHJhZyBvbiBtb2JpbGUgZm9yIGJldHRlciBVWFxuICAgICAgICAgICAgICBkcm9wcGFibGU9eyFpc01vYmlsZX1cbiAgICAgICAgICAgICAgZXZlbnRSZXNpemFibGVGcm9tU3RhcnQ9eyFpc01vYmlsZX1cbiAgICAgICAgICAgICAgc2VsZWN0PXtoYW5kbGVEYXRlU2VsZWN0fVxuICAgICAgICAgICAgICBldmVudENsaWNrPXtoYW5kbGVFdmVudENsaWNrfVxuICAgICAgICAgICAgICBldmVudERyb3A9e2hhbmRsZUV2ZW50RHJvcH1cbiAgICAgICAgICAgICAgc2xvdE1pblRpbWU9XCIwMDowMDowMFwiXG4gICAgICAgICAgICAgIHNsb3RNYXhUaW1lPVwiMjQ6MDA6MDBcIlxuICAgICAgICAgICAgICBzbG90RHVyYXRpb249XCIwMDozMDowMFwiXG4gICAgICAgICAgICAgIHNsb3RMYWJlbEludGVydmFsPXtpc01vYmlsZSA/IFwiMDI6MDA6MDBcIiA6IFwiMDE6MDA6MDBcIn1cbiAgICAgICAgICAgICAgYWxsRGF5U2xvdD17ZmFsc2V9XG4gICAgICAgICAgICAgIG5vd0luZGljYXRvcj17dHJ1ZX1cbiAgICAgICAgICAgICAgYnVzaW5lc3NIb3Vycz17e1xuICAgICAgICAgICAgICAgIGRheXNPZldlZWs6IFsxLCAyLCAzLCA0LCA1XSwgLy8gTW9uZGF5IC0gRnJpZGF5XG4gICAgICAgICAgICAgICAgc3RhcnRUaW1lOiAnMDg6MDAnLFxuICAgICAgICAgICAgICAgIGVuZFRpbWU6ICcxODowMCcsXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIGV2ZW50RGlzcGxheT1cImJsb2NrXCJcbiAgICAgICAgICAgICAgZGF5TWF4RXZlbnRzPXtpc01vYmlsZSA/IDMgOiB0cnVlfVxuICAgICAgICAgICAgICBtb3JlTGlua0NsaWNrPVwicG9wb3ZlclwiXG4gICAgICAgICAgICAgIC8vIE1vYmlsZS1zcGVjaWZpYyBzZXR0aW5nc1xuICAgICAgICAgICAgICBhc3BlY3RSYXRpbz17aXNNb2JpbGUgPyAxLjIgOiAxLjM1fVxuICAgICAgICAgICAgICBoYW5kbGVXaW5kb3dSZXNpemU9e3RydWV9XG4gICAgICAgICAgICAgIHN0aWNreUhlYWRlckRhdGVzPXshaXNNb2JpbGV9XG4gICAgICAgICAgICAgIC8vIExvY2FsZSBjb25maWd1cmF0aW9uIC0gRml4ZWQgdG8gdXNlIHN0cmluZyBmb3JtYXRcbiAgICAgICAgICAgICAgbG9jYWxlPVwicHQtYnJcIlxuICAgICAgICAgICAgICBidXR0b25UZXh0PXt7XG4gICAgICAgICAgICAgICAgdG9kYXk6ICdIb2plJyxcbiAgICAgICAgICAgICAgICBtb250aDogJ03DqnMnLFxuICAgICAgICAgICAgICAgIHdlZWs6ICdTZW1hbmEnLFxuICAgICAgICAgICAgICAgIGRheTogJ0RpYScsXG4gICAgICAgICAgICAgICAgbGlzdDogJ0xpc3RhJ1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICB3ZWVrVGV4dD1cIlNtXCJcbiAgICAgICAgICAgICAgYWxsRGF5VGV4dD1cIlRvZG8gbyBkaWFcIlxuICAgICAgICAgICAgICBtb3JlTGlua1RleHQ9XCJtYWlzXCJcbiAgICAgICAgICAgICAgbm9FdmVudHNUZXh0PVwiTsOjbyBow6EgZXZlbnRvcyBwYXJhIG1vc3RyYXJcIlxuICAgICAgICAgICAgICBmaXJzdERheT17MH0gLy8gU3VuZGF5ID0gMFxuICAgICAgICAgICAgICBldmVudENvbnRlbnQ9eyhldmVudEluZm8pID0+IHtcbiAgICAgICAgICAgICAgICBjb25zdCB0aXRsZSA9IGV2ZW50SW5mby5ldmVudC50aXRsZSB8fCAnJztcbiAgICAgICAgICAgICAgICBjb25zdCBwYXRpZW50TmFtZSA9IGV2ZW50SW5mby5ldmVudC5leHRlbmRlZFByb3BzPy5wYXRpZW50TmFtZTtcbiAgICAgICAgICAgICAgICBjb25zdCBwcm9mZXNzaW9uYWxOYW1lID0gZXZlbnRJbmZvLmV2ZW50LmV4dGVuZGVkUHJvcHM/LnByb2Zlc3Npb25hbE5hbWU7XG4gICAgICAgICAgICAgICAgY29uc3Qgc3RhcnRUaW1lID0gZXZlbnRJbmZvLmV2ZW50LnN0YXJ0O1xuXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgcC0xICR7aXNNb2JpbGUgPyAndGV4dC14cycgOiAndGV4dC14cyd9YH0+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdHJ1bmNhdGUgdGV4dC14cyBzbTp0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2lzTW9iaWxlICYmIHRpdGxlLmxlbmd0aCA+IDIwID8gdGl0bGUuc3Vic3RyaW5nKDAsIDIwKSArICcuLi4nIDogdGl0bGV9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICB7cGF0aWVudE5hbWUgJiYgIWlzTW9iaWxlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgb3BhY2l0eS05MCB0cnVuY2F0ZVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge3BhdGllbnROYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICB7cHJvZmVzc2lvbmFsTmFtZSAmJiAhaXNNb2JpbGUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyBvcGFjaXR5LTc1IHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvZmVzc2lvbmFsTmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAge2lzTW9iaWxlICYmIHN0YXJ0VGltZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIG9wYWNpdHktNzUgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXQoc3RhcnRUaW1lLCAnSEg6bW0nKX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9DYXJkQ29udGVudD5cbiAgICA8L0NhcmQ+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBGdWxsQ2FsZW5kYXJWaWV3OyJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVJlZiIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJGdWxsQ2FsZW5kYXIiLCJkYXlHcmlkUGx1Z2luIiwidGltZUdyaWRQbHVnaW4iLCJpbnRlcmFjdGlvblBsdWdpbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsIkNhbGVuZGFyIiwiRmlsdGVyIiwidXNlVG9hc3QiLCJmb3JtYXQiLCJnZXRTdGF0dXNDb2xvciIsInN0YXR1cyIsImNvbG9ycyIsIkZ1bGxDYWxlbmRhclZpZXciLCJhcHBvaW50bWVudHMiLCJoZWFsdGhjYXJlUHJvZmVzc2lvbmFscyIsIm9uQXBwb2ludG1lbnRDcmVhdGUiLCJvbkFwcG9pbnRtZW50Q2xpY2siLCJvbkFwcG9pbnRtZW50VXBkYXRlIiwibG9hZGluZyIsImNhbGVuZGFyUmVmIiwic2VsZWN0ZWRQcm9mZXNzaW9uYWwiLCJzZXRTZWxlY3RlZFByb2Zlc3Npb25hbCIsImN1cnJlbnRWaWV3Iiwic2V0Q3VycmVudFZpZXciLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwiaXNJbml0aWFsaXplZCIsInNldElzSW5pdGlhbGl6ZWQiLCJ0b2FzdCIsImNoZWNrTW9iaWxlIiwid2luZG93IiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiZmlsdGVyZWRBcHBvaW50bWVudHMiLCJ1c2VNZW1vIiwiZmlsdGVyIiwiYXB0IiwiaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfaWQiLCJldmVudHMiLCJtYXAiLCJhcHBvaW50bWVudCIsImlkIiwidGl0bGUiLCJzdGFydCIsInN0YXJ0X3RpbWUiLCJlbmQiLCJlbmRfdGltZSIsImJhY2tncm91bmRDb2xvciIsImJvcmRlckNvbG9yIiwidGV4dENvbG9yIiwiZXh0ZW5kZWRQcm9wcyIsImRlc2NyaXB0aW9uIiwicGF0aWVudE5hbWUiLCJwYXRpZW50X25hbWUiLCJwcm9mZXNzaW9uYWxOYW1lIiwiaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfbmFtZSIsInR5cGUiLCJwcmljZSIsImhhbmRsZURhdGVTZWxlY3QiLCJzZWxlY3RJbmZvIiwiaGFuZGxlRXZlbnRDbGljayIsImNsaWNrSW5mbyIsImV2ZW50IiwiaGFuZGxlRXZlbnREcm9wIiwiZHJvcEluZm8iLCJhcHBvaW50bWVudElkIiwibmV3U3RhcnQiLCJuZXdFbmQiLCJyZXZlcnQiLCJlcnJvciIsImNvbnNvbGUiLCJ2YXJpYW50IiwiaGFuZGxlVmlld0NoYW5nZSIsInZpZXciLCJjYWxlbmRhckFwaSIsImN1cnJlbnQiLCJnZXRBcGkiLCJjaGFuZ2VWaWV3IiwiZ29Ub1RvZGF5IiwidG9kYXkiLCJuYXZpZ2F0ZUNhbGVuZGFyIiwiZGlyZWN0aW9uIiwicHJldiIsIm5leHQiLCJjbGFzc05hbWUiLCJkaXYiLCJ2YWx1ZSIsIm9uVmFsdWVDaGFuZ2UiLCJwbGFjZWhvbGRlciIsInByb2YiLCJpc19hY3RpdmUiLCJwcm9mZXNzaW9uYWwiLCJuYW1lIiwic3BlY2lhbHR5Iiwic2l6ZSIsIm9uQ2xpY2siLCJwIiwicmVmIiwicGx1Z2lucyIsImluaXRpYWxWaWV3IiwiaGVhZGVyVG9vbGJhciIsImhlaWdodCIsImNvbnRlbnRIZWlnaHQiLCJzZWxlY3RhYmxlIiwic2VsZWN0TWlycm9yIiwiZWRpdGFibGUiLCJkcm9wcGFibGUiLCJldmVudFJlc2l6YWJsZUZyb21TdGFydCIsInNlbGVjdCIsImV2ZW50Q2xpY2siLCJldmVudERyb3AiLCJzbG90TWluVGltZSIsInNsb3RNYXhUaW1lIiwic2xvdER1cmF0aW9uIiwic2xvdExhYmVsSW50ZXJ2YWwiLCJhbGxEYXlTbG90Iiwibm93SW5kaWNhdG9yIiwiYnVzaW5lc3NIb3VycyIsImRheXNPZldlZWsiLCJzdGFydFRpbWUiLCJlbmRUaW1lIiwiZXZlbnREaXNwbGF5IiwiZGF5TWF4RXZlbnRzIiwibW9yZUxpbmtDbGljayIsImFzcGVjdFJhdGlvIiwiaGFuZGxlV2luZG93UmVzaXplIiwic3RpY2t5SGVhZGVyRGF0ZXMiLCJsb2NhbGUiLCJidXR0b25UZXh0IiwibW9udGgiLCJ3ZWVrIiwiZGF5IiwibGlzdCIsIndlZWtUZXh0IiwiYWxsRGF5VGV4dCIsIm1vcmVMaW5rVGV4dCIsIm5vRXZlbnRzVGV4dCIsImZpcnN0RGF5IiwiZXZlbnRDb250ZW50IiwiZXZlbnRJbmZvIiwibGVuZ3RoIiwic3Vic3RyaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FullCalendarView.tsx\n"));

/***/ })

});