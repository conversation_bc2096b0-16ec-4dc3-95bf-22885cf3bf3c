"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,CheckCircle,Clock,FileText,Grid3X3,Play,Plus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ConfirmDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ConfirmDialog */ \"(app-pages-browser)/./src/components/ConfirmDialog.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Confirmation dialog states\n    const [confirmDialog, setConfirmDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        title: '',\n        description: '',\n        onConfirm: {\n            \"AgendaPage.useState\": ()=>{}\n        }[\"AgendaPage.useState\"],\n        variant: 'default'\n    });\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter)();\n    // Fetch initial data that doesn't depend on selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], []);\n    // Fetch date-specific appointments when selected date changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            // FullCalendar provides dates in local timezone, use them directly\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.toLocalISOString)(selectInfo.start);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.toLocalISOString)(selectInfo.end);\n        } else if (selectedDate) {\n            // Create appointment for selected date at 9:00 AM\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.toLocalISOString)(startTime);\n            initialData.end_time = (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.toLocalISOString)(endTime);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // For in-progress and completed appointments, navigate to medical record screen\n        if (appointment.status === 'in_progress' || appointment.status === 'completed') {\n            console.log('Navigating to medical record for appointment:', appointment.id);\n            router.push(\"/dashboard/prontuario/\".concat(appointment.patient_id, \"?appointment_id=\").concat(appointment.id));\n            return;\n        }\n        // For other statuses, open edit form with appointment data\n        console.log('Appointment clicked for editing:', appointment);\n        const editData = {\n            id: appointment.id,\n            title: appointment.title,\n            description: appointment.description,\n            patient_id: appointment.patient_id,\n            healthcare_professional_id: appointment.healthcare_professional_id,\n            start_time: appointment.start_time,\n            end_time: appointment.end_time,\n            type: appointment.type,\n            status: appointment.status,\n            total_price: appointment.total_price\n        };\n        setAppointmentFormData(editData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const isEditing = data.id;\n            const url = isEditing ? \"/api/appointments/\".concat(data.id) : '/api/appointments';\n            const method = isEditing ? 'PUT' : 'POST';\n            // Remove id from data for API call\n            const { id, ...submitData } = data;\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(submitData)\n            });\n            if (!response.ok) throw new Error(\"Failed to \".concat(isEditing ? 'update' : 'create', \" appointment\"));\n            // Refresh both daily and all appointments for calendar views\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error(\"Error \".concat(data.id ? 'updating' : 'creating', \" appointment:\"), error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = (appointmentId)=>{\n        setConfirmDialog({\n            open: true,\n            title: 'Excluir Consulta',\n            description: 'Tem certeza que deseja excluir esta consulta? Esta ação não pode ser desfeita.',\n            variant: 'destructive',\n            onConfirm: ()=>performDeleteAppointment(appointmentId)\n        });\n    };\n    const performDeleteAppointment = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleCancelAppointment = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        setConfirmDialog({\n            open: true,\n            title: 'Cancelar Consulta',\n            description: \"Tem certeza que deseja cancelar a consulta de \".concat(appointment.patient_name, \"?\"),\n            variant: 'destructive',\n            onConfirm: ()=>performCancelAppointment(appointment.id)\n        });\n    };\n    const performCancelAppointment = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'cancelled'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to cancel appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta cancelada com sucesso.\"\n            });\n            fetchAppointments();\n            fetchAllAppointments();\n        } catch (error) {\n            console.error('Error canceling appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao cancelar consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStartConsultation = async (appointment, event)=>{\n        event.stopPropagation(); // Prevent triggering the edit click handler\n        setConfirmDialog({\n            open: true,\n            title: 'Iniciar Atendimento',\n            description: \"Deseja iniciar o atendimento para \".concat(appointment.patient_name, \"?\"),\n            variant: 'default',\n            onConfirm: ()=>performStartConsultation(appointment)\n        });\n    };\n    const performStartConsultation = async (appointment)=>{\n        try {\n            // Update appointment status to in_progress\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointment.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'in_progress'\n                })\n            });\n            if (!response.ok) throw new Error('Failed to start consultation');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Atendimento iniciado com sucesso.\"\n            });\n            // Navigate to medical record screen\n            router.push(\"/dashboard/prontuario/\".concat(appointment.patient_id, \"?appointment_id=\").concat(appointment.id));\n        } catch (error) {\n            console.error('Error starting consultation:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao iniciar atendimento.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 465,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 469,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 463,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 521,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_18__.ptBR,\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    // appointmentCounts={appointmentCounts}\n                                                    // clinicSettings={clinicSettings || undefined}\n                                                    // appointments={allAppointments}\n                                                    className: \"rounded-md border-0 shadow-none w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateBR)(selectedDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 547,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.filter((apt)=>apt.status !== 'cancelled').length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border transition-colors space-y-3 sm:space-y-0 \".concat(appointment.status === 'cancelled' ? 'bg-red-50 border-red-200 hover:bg-red-100' : appointment.status === 'in_progress' ? 'bg-blue-50 border-blue-200 hover:bg-blue-100 cursor-pointer' : appointment.status === 'completed' ? 'bg-green-50 border-green-200 hover:bg-green-100 cursor-pointer' : 'bg-card/50 hover:bg-card cursor-pointer'),\n                                                            onClick: appointment.status !== 'cancelled' ? ()=>handleAppointmentClick(appointment) : undefined,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-1\",\n                                                                                    children: [\n                                                                                        appointment.status === 'in_progress' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"h-3 w-3 text-blue-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 585,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        appointment.status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                            className: \"h-3 w-3 text-green-600\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 588,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm font-medium\",\n                                                                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.start_time)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 590,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 583,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.end_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 594,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 582,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 599,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 600,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 604,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                (appointment.status === 'in_progress' || appointment.status === 'completed') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-blue-600 mt-1\",\n                                                                                    children: appointment.status === 'in_progress' ? 'Clique para continuar o atendimento' : 'Clique para ver o prontuário'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 609,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                                    variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : appointment.status === 'in_progress' ? 'default' : 'outline',\n                                                                                    className: \"text-xs\",\n                                                                                    children: [\n                                                                                        appointment.status === 'scheduled' && 'Agendado',\n                                                                                        appointment.status === 'confirmed' && 'Confirmado',\n                                                                                        appointment.status === 'completed' && 'Concluído',\n                                                                                        appointment.status === 'cancelled' && 'Cancelado',\n                                                                                        appointment.status === 'in_progress' && 'Em Andamento'\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 620,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.total_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium whitespace-nowrap\",\n                                                                                    children: [\n                                                                                        \"R$ \",\n                                                                                        appointment.total_price.toFixed(2)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 637,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.status !== 'cancelled' && appointment.status !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex space-x-1\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs\",\n                                                                                    onClick: (e)=>handleStartConsultation(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 652,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        \"Iniciar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 646,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    size: \"sm\",\n                                                                                    variant: \"outline\",\n                                                                                    className: \"h-8 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                                                                    onClick: (e)=>handleCancelAppointment(appointment, e),\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_CheckCircle_Clock_FileText_Grid3X3_Play_Plus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                            className: \"h-3 w-3 mr-1\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                            lineNumber: 662,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        \"Cancelar\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 517,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 516,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            appointments: allAppointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 679,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 477,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 690,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConfirmDialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                open: confirmDialog.open,\n                onOpenChange: (open)=>setConfirmDialog((prev)=>({\n                            ...prev,\n                            open\n                        })),\n                title: confirmDialog.title,\n                description: confirmDialog.description,\n                variant: confirmDialog.variant,\n                onConfirm: confirmDialog.onConfirm\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 701,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 462,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"4/yEZimNaegVRrl3D+KfCx0g7HA=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_13__.useRouter\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});