import { z } from 'zod'

// Utility schemas
const phoneRegex = /^(\+55\s?)?(\(?\d{2}\)?\s?)?\d{4,5}-?\d{4}$/
const cpfRegex = /^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$/
const crmRegex = /^\d{4,6}\/[A-Z]{2}$/

// Patient validation schema
export const patientSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços'),
  
  email: z.string()
    .email('E-mail inválido')
    .optional()
    .or(z.literal('')),
  
  phone: z.string()
    .regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999')
    .optional()
    .or(z.literal('')),
  
  birth_date: z.string()
    .optional()
    .refine((date) => {
      if (!date) return true
      const birthDate = new Date(date)
      const today = new Date()
      const age = today.getFullYear() - birthDate.getFullYear()
      return age >= 0 && age <= 150
    }, 'Data de nascimento inválida'),
  
  cpf: z.string()
    .regex(cpfRegex, 'CPF inválido. Use formato: 000.000.000-00')
    .optional()
    .or(z.literal('')),
  
  address: z.string()
    .max(500, 'Endereço deve ter no máximo 500 caracteres')
    .optional()
    .or(z.literal('')),
  
  notes: z.string()
    .max(1000, 'Observações devem ter no máximo 1000 caracteres')
    .optional()
    .or(z.literal(''))
})

export type PatientFormData = z.infer<typeof patientSchema>

// Healthcare Professional validation schema
export const healthcareProfessionalSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços'),
  
  specialty: z.string()
    .max(100, 'Especialidade deve ter no máximo 100 caracteres')
    .optional()
    .or(z.literal('')),
  
  crm: z.string()
    .regex(crmRegex, 'CRM inválido. Use formato: 123456/SP')
    .optional()
    .or(z.literal('')),
  
  phone: z.string()
    .regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999')
    .optional()
    .or(z.literal('')),
  
  email: z.string()
    .email('E-mail inválido')
    .optional()
    .or(z.literal('')),
  
  is_active: z.boolean().default(true)
})

export type HealthcareProfessionalFormData = z.infer<typeof healthcareProfessionalSchema>

// Procedure validation schema
export const procedureSchema = z.object({
  name: z.string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(200, 'Nome deve ter no máximo 200 caracteres'),
  
  description: z.string()
    .max(1000, 'Descrição deve ter no máximo 1000 caracteres')
    .optional()
    .or(z.literal('')),
  
  default_price: z.number()
    .min(0, 'Preço deve ser maior ou igual a zero')
    .max(99999.99, 'Preço deve ser menor que R$ 100.000,00')
    .optional(),
  
  duration_minutes: z.number()
    .int('Duração deve ser um número inteiro')
    .min(5, 'Duração mínima é 5 minutos')
    .max(480, 'Duração máxima é 8 horas')
    .optional()
})

export type ProcedureFormData = z.infer<typeof procedureSchema>

// Appointment validation schema
export const appointmentSchema = z.object({
  title: z.string()
    .min(2, 'Título deve ter pelo menos 2 caracteres')
    .max(200, 'Título deve ter no máximo 200 caracteres'),
  
  description: z.string()
    .max(1000, 'Descrição deve ter no máximo 1000 caracteres')
    .optional()
    .or(z.literal('')),
  
  patient_id: z.string()
    .uuid('ID do paciente inválido'),
  
  healthcare_professional_id: z.string()
    .uuid('ID do profissional inválido')
    .optional()
    .or(z.literal('')),
  
  start_time: z.string()
    .min(1, 'Data/hora de início é obrigatória')
    .refine((val) => {
      console.log('[VALIDATION DEBUG] Validating start_time:', val);
      // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format
      const datetimeLocalRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/;
      const isDatetimeLocal = datetimeLocalRegex.test(val);
      const isValidDate = !isNaN(new Date(val).getTime());
      console.log('[VALIDATION DEBUG] start_time validation:', { val, isDatetimeLocal, isValidDate });
      return isDatetimeLocal || isValidDate;
    }, 'Data/hora de início inválida'),

  end_time: z.string()
    .min(1, 'Data/hora de fim é obrigatória')
    .refine((val) => {
      console.log('[VALIDATION DEBUG] Validating end_time:', val);
      // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format
      const datetimeLocalRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/;
      const isDatetimeLocal = datetimeLocalRegex.test(val);
      const isValidDate = !isNaN(new Date(val).getTime());
      console.log('[VALIDATION DEBUG] end_time validation:', { val, isDatetimeLocal, isValidDate });
      return isDatetimeLocal || isValidDate;
    }, 'Data/hora de fim inválida'),
  
  type: z.enum(['consultation', 'follow_up'], {
    errorMap: () => ({ message: 'Tipo de consulta inválido' })
  }),

  status: z.enum(['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled'], {
    errorMap: () => ({ message: 'Status da consulta inválido' })
  }).optional(),

  notes: z.string()
    .max(1000, 'Observações devem ter no máximo 1000 caracteres')
    .optional()
    .or(z.literal('')),
  
  // Recurrence fields
  has_recurrence: z.boolean().default(false),
  
  recurrence_type: z.enum(['daily', 'weekly', 'monthly']).optional(),
  
  recurrence_interval: z.number()
    .int('Intervalo deve ser um número inteiro')
    .min(1, 'Intervalo mínimo é 1')
    .max(12, 'Intervalo máximo é 12')
    .optional(),
  
  recurrence_days: z.array(z.number().int().min(1).max(7)).optional(),
  
  recurrence_end_type: z.enum(['never', 'date', 'count']).optional(),
  
  recurrence_end_date: z.string().optional(),
  
  recurrence_count: z.number()
    .int('Número de ocorrências deve ser um número inteiro')
    .min(1, 'Mínimo 1 ocorrência')
    .max(365, 'Máximo 365 ocorrências')
    .optional()
}).refine((data) => {
  // Validate that end_time is after start_time
  try {
    console.log('[DATE VALIDATION DEBUG] Validating dates:', {
      start_time: data.start_time,
      end_time: data.end_time,
      start_time_type: typeof data.start_time,
      end_time_type: typeof data.end_time
    });

    // Skip validation if either date is empty (will be caught by required validation)
    if (!data.start_time || !data.end_time) {
      console.log('[DATE VALIDATION DEBUG] One or both dates empty, skipping validation');
      return true
    }

    const start = new Date(data.start_time)
    const end = new Date(data.end_time)

    console.log('[DATE VALIDATION DEBUG] Parsed dates:', {
      start: start.toISOString(),
      end: end.toISOString(),
      startTime: start.getTime(),
      endTime: end.getTime(),
      startValid: !isNaN(start.getTime()),
      endValid: !isNaN(end.getTime())
    });

    // Check if dates are valid
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      console.log('[DATE VALIDATION DEBUG] Invalid date(s) detected');
      return false
    }

    const isValid = end > start;
    console.log('[DATE VALIDATION DEBUG] Comparison result:', {
      endGreaterThanStart: isValid,
      timeDifference: end.getTime() - start.getTime()
    });

    return isValid
  } catch (error) {
    console.error('[DATE VALIDATION DEBUG] Date validation error:', error)
    return false
  }
}, {
  message: 'Data/hora de fim deve ser posterior à data/hora de início',
  path: ['end_time']
}).refine((data) => {
  // Validate recurrence fields when has_recurrence is true
  if (data.has_recurrence) {
    return data.recurrence_type && data.recurrence_interval
  }
  return true
}, {
  message: 'Campos de recorrência são obrigatórios quando recorrência está ativada',
  path: ['recurrence_type']
}).refine((data) => {
  // Validate recurrence end date when end_type is 'date'
  if (data.has_recurrence && data.recurrence_end_type === 'date') {
    return data.recurrence_end_date
  }
  return true
}, {
  message: 'Data de fim é obrigatória quando tipo de fim é "data"',
  path: ['recurrence_end_date']
}).refine((data) => {
  // Validate recurrence count when end_type is 'count'
  if (data.has_recurrence && data.recurrence_end_type === 'count') {
    return data.recurrence_count && data.recurrence_count > 0
  }
  return true
}, {
  message: 'Número de ocorrências é obrigatório quando tipo de fim é "contagem"',
  path: ['recurrence_count']
})

export type AppointmentFormData = z.infer<typeof appointmentSchema>

// Clinic Settings validation schema
export const clinicSettingsSchema = z.object({
  clinic_name: z.string()
    .max(200, 'Nome da clínica deve ter no máximo 200 caracteres')
    .optional()
    .or(z.literal('')),
  
  working_hours_start: z.string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de início inválido'),
  
  working_hours_end: z.string()
    .regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de fim inválido'),
  
  working_days: z.array(z.number().int().min(1).max(7))
    .min(1, 'Pelo menos um dia de funcionamento deve ser selecionado'),
  
  appointment_duration_minutes: z.number()
    .int('Duração deve ser um número inteiro')
    .min(15, 'Duração mínima é 15 minutos')
    .max(180, 'Duração máxima é 3 horas'),
  
  allow_weekend_appointments: z.boolean().default(false),
  
  timezone: z.string()
    .min(1, 'Fuso horário é obrigatório')
}).refine((data) => {
  // Validate that end time is after start time
  const [startHour, startMinute] = data.working_hours_start.split(':').map(Number)
  const [endHour, endMinute] = data.working_hours_end.split(':').map(Number)
  
  const startMinutes = startHour * 60 + startMinute
  const endMinutes = endHour * 60 + endMinute
  
  return endMinutes > startMinutes
}, {
  message: 'Horário de fim deve ser posterior ao horário de início',
  path: ['working_hours_end']
})

export type ClinicSettingsFormData = z.infer<typeof clinicSettingsSchema>

// File upload validation
export const fileUploadSchema = z.object({
  file: z.instanceof(File)
    .refine((file) => file.size <= 50 * 1024 * 1024, 'Arquivo deve ter no máximo 50MB')
    .refine((file) => {
      const allowedTypes = [
        'image/jpeg',
        'image/png', 
        'image/gif',
        'application/pdf',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ]
      return allowedTypes.includes(file.type)
    }, 'Tipo de arquivo não permitido'),
  
  patient_id: z.string().uuid('ID do paciente inválido')
})

export type FileUploadFormData = z.infer<typeof fileUploadSchema>

// Common validation utilities
export const validateCPF = (cpf: string): boolean => {
  // Remove formatting
  const cleanCPF = cpf.replace(/[^\d]/g, '')
  
  if (cleanCPF.length !== 11) return false
  
  // Check for repeated digits
  if (/^(\d)\1{10}$/.test(cleanCPF)) return false
  
  // Validate check digits
  let sum = 0
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (10 - i)
  }
  let remainder = (sum * 10) % 11
  if (remainder === 10 || remainder === 11) remainder = 0
  if (remainder !== parseInt(cleanCPF.charAt(9))) return false
  
  sum = 0
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cleanCPF.charAt(i)) * (11 - i)
  }
  remainder = (sum * 10) % 11
  if (remainder === 10 || remainder === 11) remainder = 0
  if (remainder !== parseInt(cleanCPF.charAt(10))) return false
  
  return true
}

export const formatCPF = (cpf: string): string => {
  const cleanCPF = cpf.replace(/[^\d]/g, '')
  return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4')
}

export const formatPhone = (phone: string): string => {
  const cleanPhone = phone.replace(/[^\d]/g, '')
  if (cleanPhone.length === 11) {
    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3')
  } else if (cleanPhone.length === 10) {
    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3')
  }
  return phone
}
