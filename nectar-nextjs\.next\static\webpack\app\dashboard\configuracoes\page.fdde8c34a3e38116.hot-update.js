"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/configuracoes/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/configuracoes/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/configuracoes/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    // Estados iniciais com valores padrão\n    const defaultProfile = {\n        name: '',\n        email: '',\n        phone: '',\n        specialty: '',\n        crm: '',\n        clinic_name: '',\n        clinic_address: ''\n    };\n    const defaultNotifications = {\n        email_appointments: true,\n        email_messages: true,\n        sms_appointments: false,\n        sms_messages: false,\n        whatsapp_notifications: true\n    };\n    const defaultIntegrations = {\n        whatsapp_token: '',\n        whatsapp_phone: '',\n        email_smtp_host: '',\n        email_smtp_port: '',\n        email_smtp_user: '',\n        email_smtp_password: ''\n    };\n    const defaultClinicSettings = {\n        clinic_name: null,\n        working_hours_start: '08:00',\n        working_hours_end: '18:00',\n        working_days: [\n            1,\n            2,\n            3,\n            4,\n            5\n        ],\n        appointment_duration_minutes: 30,\n        allow_weekend_appointments: false,\n        timezone: 'America/Sao_Paulo'\n    };\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultProfile);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultNotifications);\n    const [integrations, setIntegrations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultIntegrations);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultClinicSettings);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            fetchSettings();\n            fetchClinicSettings();\n            fetchHealthcareProfessionals();\n            fetchUserRoles();\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    const fetchSettings = async ()=>{\n        try {\n            setLoading(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/settings');\n            if (!response.ok) throw new Error('Failed to fetch settings');\n            const result = await response.json();\n            const data = result.data || result;\n            if (data.profile) setProfile(data.profile);\n            if (data.notifications) setNotifications(data.notifications);\n            if (data.integrations) setIntegrations(data.integrations);\n        } catch (error) {\n            toast({\n                title: \"Erro ao carregar configurações\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n        }\n    };\n    const fetchUserRoles = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/user-roles');\n            if (!response.ok) throw new Error('Failed to fetch user roles');\n            const result = await response.json();\n            const data = result.data || result;\n            setUserRoles(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching user roles:', error);\n        }\n    };\n    const saveProfile = async ()=>{\n        try {\n            setSaving(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/settings/profile', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(profile)\n            });\n            if (!response.ok) throw new Error('Failed to save profile');\n            toast({\n                title: \"Perfil atualizado\",\n                description: \"Suas informações foram salvas com sucesso\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Erro ao salvar perfil\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveClinicSettings = async ()=>{\n        try {\n            setSaving(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/clinic-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(clinicSettings)\n            });\n            if (!response.ok) throw new Error('Failed to save clinic settings');\n            toast({\n                title: \"Configurações da clínica atualizadas\",\n                description: \"Suas configurações foram salvas com sucesso\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Erro ao salvar configurações\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveIntegrations = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/settings/integrations', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(integrations)\n            });\n            if (!response.ok) throw new Error('Failed to save integrations');\n            toast({\n                title: \"Integrações atualizadas\",\n                description: \"Suas configurações foram salvas com sucesso\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Erro ao salvar integrações\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n            lineNumber: 266,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-foreground\",\n                        children: \"Configura\\xe7\\xf5es\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Gerencie suas prefer\\xeancias e integra\\xe7\\xf5es\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                defaultValue: \"profile\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                        className: \"grid w-full grid-cols-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"profile\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Perfil\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"clinic\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Cl\\xednica\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"professionals\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Profissionais\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"integrations\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Integra\\xe7\\xf5es\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"security\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Seguran\\xe7a\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"profile\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Informa\\xe7\\xf5es Pessoais\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Atualize suas informa\\xe7\\xf5es pessoais e profissionais\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"name\",\n                                                            children: \"Nome Completo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"name\",\n                                                            value: profile.name,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    name: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"email\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"email\",\n                                                            type: \"email\",\n                                                            value: profile.email,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    email: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"phone\",\n                                                            children: \"Telefone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"phone\",\n                                                            value: profile.phone,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    phone: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"specialty\",\n                                                            children: \"Especialidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"specialty\",\n                                                            value: profile.specialty,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    specialty: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"crm\",\n                                                            children: \"CRM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"crm\",\n                                                            value: profile.crm,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    crm: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"clinic_name\",\n                                                            children: \"Nome da Cl\\xednica\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"clinic_name\",\n                                                            value: profile.clinic_name,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    clinic_name: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"clinic_address\",\n                                                    children: \"Endere\\xe7o da Cl\\xednica\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"clinic_address\",\n                                                    value: profile.clinic_address,\n                                                    onChange: (e)=>setProfile({\n                                                            ...profile,\n                                                            clinic_address: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: saveProfile,\n                                            disabled: saving,\n                                            children: saving ? 'Salvando...' : 'Salvar Perfil'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"clinic\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Configura\\xe7\\xf5es da Cl\\xednica\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Configure hor\\xe1rios de funcionamento e prefer\\xeancias da cl\\xednica\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"clinic_name\",\n                                                    children: \"Nome da Cl\\xednica\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"clinic_name\",\n                                                    value: clinicSettings.clinic_name || '',\n                                                    onChange: (e)=>setClinicSettings({\n                                                            ...clinicSettings,\n                                                            clinic_name: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 403,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"working_hours_start\",\n                                                            children: \"Hor\\xe1rio de In\\xedcio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"working_hours_start\",\n                                                            type: \"time\",\n                                                            value: clinicSettings.working_hours_start,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    working_hours_start: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"working_hours_end\",\n                                                            children: \"Hor\\xe1rio de T\\xe9rmino\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"working_hours_end\",\n                                                            type: \"time\",\n                                                            value: clinicSettings.working_hours_end,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    working_hours_end: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"Dias de Funcionamento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        {\n                                                            value: 1,\n                                                            label: 'Dom'\n                                                        },\n                                                        {\n                                                            value: 2,\n                                                            label: 'Seg'\n                                                        },\n                                                        {\n                                                            value: 3,\n                                                            label: 'Ter'\n                                                        },\n                                                        {\n                                                            value: 4,\n                                                            label: 'Qua'\n                                                        },\n                                                        {\n                                                            value: 5,\n                                                            label: 'Qui'\n                                                        },\n                                                        {\n                                                            value: 6,\n                                                            label: 'Sex'\n                                                        },\n                                                        {\n                                                            value: 7,\n                                                            label: 'Sáb'\n                                                        }\n                                                    ].map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: clinicSettings.working_days.includes(day.value) ? 'default' : 'outline',\n                                                            size: \"sm\",\n                                                            onClick: ()=>{\n                                                                const newDays = clinicSettings.working_days.includes(day.value) ? clinicSettings.working_days.filter((d)=>d !== day.value) : [\n                                                                    ...clinicSettings.working_days,\n                                                                    day.value\n                                                                ];\n                                                                setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    working_days: newDays\n                                                                });\n                                                            },\n                                                            children: day.label\n                                                        }, day.value, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"appointment_duration\",\n                                                            children: \"Dura\\xe7\\xe3o Padr\\xe3o da Consulta (minutos)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"appointment_duration\",\n                                                            type: \"number\",\n                                                            min: \"15\",\n                                                            max: \"180\",\n                                                            step: \"15\",\n                                                            value: clinicSettings.appointment_duration_minutes,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    appointment_duration_minutes: parseInt(e.target.value) || 30\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"timezone\",\n                                                            children: \"Fuso Hor\\xe1rio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"timezone\",\n                                                            value: clinicSettings.timezone,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    timezone: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                    id: \"allow_weekend\",\n                                                    checked: clinicSettings.allow_weekend_appointments,\n                                                    onCheckedChange: (checked)=>setClinicSettings({\n                                                            ...clinicSettings,\n                                                            allow_weekend_appointments: checked\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"allow_weekend\",\n                                                    children: \"Permitir agendamentos nos fins de semana\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 487,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: saveClinicSettings,\n                                            disabled: saving,\n                                            children: saving ? 'Salvando...' : 'Salvar Configurações'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 389,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"professionals\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Profissionais de Sa\\xfade\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Gerencie os profissionais de sa\\xfade da sua cl\\xednica\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: healthcareProfessionals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhum profissional cadastrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                className: \"mt-4\",\n                                                children: \"Adicionar Profissional\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 518,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            healthcareProfessionals.map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-10 h-10 rounded-full bg-primary/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                        lineNumber: 534,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium\",\n                                                                            children: professional.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: professional.specialty || 'Especialidade não informada'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                            lineNumber: 538,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        professional.crm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: [\n                                                                                \"CRM: \",\n                                                                                professional.crm\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                            lineNumber: 542,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: professional.is_active ? 'default' : 'secondary',\n                                                                    children: professional.is_active ? 'Ativo' : 'Inativo'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    children: \"Editar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, professional.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                        lineNumber: 557,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Adicionar Profissional\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 516,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"integrations\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Integra\\xe7\\xf5es\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 569,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Configure integra\\xe7\\xf5es com WhatsApp e email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"WhatsApp Business API\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"whatsapp_token\",\n                                                                    children: \"Token de Acesso\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"whatsapp_token\",\n                                                                    type: \"password\",\n                                                                    value: integrations.whatsapp_token,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            whatsapp_token: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"whatsapp_phone\",\n                                                                    children: \"N\\xfamero do WhatsApp\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 591,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"whatsapp_phone\",\n                                                                    value: integrations.whatsapp_phone,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            whatsapp_phone: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Configura\\xe7\\xf5es de Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 602,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_host\",\n                                                                    children: \"Servidor SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_host\",\n                                                                    value: integrations.email_smtp_host,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_host: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 606,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 604,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_port\",\n                                                                    children: \"Porta SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_port\",\n                                                                    value: integrations.email_smtp_port,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_port: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 603,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_user\",\n                                                                    children: \"Usu\\xe1rio SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 623,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_user\",\n                                                                    value: integrations.email_smtp_user,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_user: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_password\",\n                                                                    children: \"Senha SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_password\",\n                                                                    type: \"password\",\n                                                                    value: integrations.email_smtp_password,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_password: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 630,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 601,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: saveIntegrations,\n                                            disabled: saving,\n                                            children: saving ? 'Salvando...' : 'Salvar Integrações'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 642,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"security\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 653,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Seguran\\xe7a\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Gerencie configura\\xe7\\xf5es de seguran\\xe7a da sua conta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Alterar Senha\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"current_password\",\n                                                                    children: \"Senha Atual\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"current_password\",\n                                                                    type: \"password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 666,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 664,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"new_password\",\n                                                                    children: \"Nova Senha\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 669,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"new_password\",\n                                                                    type: \"password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"confirm_password\",\n                                                                    children: \"Confirmar Nova Senha\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"confirm_password\",\n                                                                    type: \"password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 674,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 672,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    children: \"Alterar Senha\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Autentica\\xe7\\xe3o de Dois Fatores\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 683,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Ativar 2FA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Adicione uma camada extra de seguran\\xe7a \\xe0 sua conta\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Sess\\xf5es Ativas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 696,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Sess\\xe3o atual\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                        lineNumber: 700,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Windows • Chrome • S\\xe3o Paulo, BR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                        lineNumber: 701,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                lineNumber: 699,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: \"Encerrar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                lineNumber: 703,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 650,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 649,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"hTpNI+rM4rP03K6nmCjKQpqYjFk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/configuracoes/page.tsx\n"));

/***/ })

});