"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/configuracoes/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/configuracoes/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/configuracoes/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Shield,Smartphone,User,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst SettingsPage = ()=>{\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    // Estados iniciais com valores padrão\n    const defaultProfile = {\n        name: '',\n        email: '',\n        phone: '',\n        specialty: '',\n        crm: '',\n        clinic_name: '',\n        clinic_address: ''\n    };\n    const defaultNotifications = {\n        email_appointments: true,\n        email_messages: true,\n        sms_appointments: false,\n        sms_messages: false,\n        whatsapp_notifications: true\n    };\n    const defaultIntegrations = {\n        whatsapp_token: '',\n        whatsapp_phone: '',\n        email_smtp_host: '',\n        email_smtp_port: '',\n        email_smtp_user: '',\n        email_smtp_password: ''\n    };\n    const defaultClinicSettings = {\n        clinic_name: null,\n        working_hours_start: '08:00',\n        working_hours_end: '18:00',\n        working_days: [\n            1,\n            2,\n            3,\n            4,\n            5\n        ],\n        appointment_duration_minutes: 30,\n        allow_weekend_appointments: false,\n        timezone: 'America/Sao_Paulo'\n    };\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultProfile);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultNotifications);\n    const [integrations, setIntegrations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultIntegrations);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultClinicSettings);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [userRoles, setUserRoles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            fetchSettings();\n            fetchClinicSettings();\n            fetchHealthcareProfessionals();\n            fetchUserRoles();\n        }\n    }[\"SettingsPage.useEffect\"], []);\n    const fetchSettings = async ()=>{\n        try {\n            setLoading(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/settings');\n            if (!response.ok) throw new Error('Failed to fetch settings');\n            const result = await response.json();\n            const data = result.data || result;\n            if (data.profile) setProfile(data.profile);\n            if (data.notifications) setNotifications(data.notifications);\n            if (data.integrations) setIntegrations(data.integrations);\n        } catch (error) {\n            toast({\n                title: \"Erro ao carregar configurações\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n        }\n    };\n    const fetchUserRoles = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/user-roles');\n            if (!response.ok) throw new Error('Failed to fetch user roles');\n            const result = await response.json();\n            const data = result.data || result;\n            setUserRoles(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching user roles:', error);\n        }\n    };\n    const saveProfile = async ()=>{\n        try {\n            setSaving(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/settings/profile', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(profile)\n            });\n            if (!response.ok) throw new Error('Failed to save profile');\n            toast({\n                title: \"Perfil atualizado\",\n                description: \"Suas informações foram salvas com sucesso\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Erro ao salvar perfil\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveClinicSettings = async ()=>{\n        try {\n            setSaving(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_11__.makeAuthenticatedRequest)('/api/clinic-settings', {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(clinicSettings)\n            });\n            if (!response.ok) throw new Error('Failed to save clinic settings');\n            toast({\n                title: \"Configurações da clínica atualizadas\",\n                description: \"Suas configurações foram salvas com sucesso\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Erro ao salvar configurações\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveNotifications = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/settings/notifications', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(notifications)\n            });\n            if (!response.ok) throw new Error('Failed to save notifications');\n            toast({\n                title: \"Notificações atualizadas\",\n                description: \"Suas preferências foram salvas com sucesso\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Erro ao salvar notificações\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const saveIntegrations = async ()=>{\n        try {\n            setSaving(true);\n            const response = await fetch('/api/settings/integrations', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(integrations)\n            });\n            if (!response.ok) throw new Error('Failed to save integrations');\n            toast({\n                title: \"Integrações atualizadas\",\n                description: \"Suas configurações foram salvas com sucesso\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Erro ao salvar integrações\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n            lineNumber: 291,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-foreground\",\n                        children: \"Configura\\xe7\\xf5es\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Gerencie suas prefer\\xeancias e integra\\xe7\\xf5es\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                defaultValue: \"profile\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                        className: \"grid w-full grid-cols-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"profile\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Perfil\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"clinic\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Cl\\xednica\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"professionals\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Profissionais\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"integrations\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Integra\\xe7\\xf5es\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                value: \"security\",\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Seguran\\xe7a\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"profile\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Informa\\xe7\\xf5es Pessoais\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Atualize suas informa\\xe7\\xf5es pessoais e profissionais\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"name\",\n                                                            children: \"Nome Completo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"name\",\n                                                            value: profile.name,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    name: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"email\",\n                                                            children: \"Email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"email\",\n                                                            type: \"email\",\n                                                            value: profile.email,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    email: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"phone\",\n                                                            children: \"Telefone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"phone\",\n                                                            value: profile.phone,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    phone: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"specialty\",\n                                                            children: \"Especialidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"specialty\",\n                                                            value: profile.specialty,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    specialty: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"crm\",\n                                                            children: \"CRM\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"crm\",\n                                                            value: profile.crm,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    crm: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"clinic_name\",\n                                                            children: \"Nome da Cl\\xednica\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"clinic_name\",\n                                                            value: profile.clinic_name,\n                                                            onChange: (e)=>setProfile({\n                                                                    ...profile,\n                                                                    clinic_name: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"clinic_address\",\n                                                    children: \"Endere\\xe7o da Cl\\xednica\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                    id: \"clinic_address\",\n                                                    value: profile.clinic_address,\n                                                    onChange: (e)=>setProfile({\n                                                            ...profile,\n                                                            clinic_address: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: saveProfile,\n                                            disabled: saving,\n                                            children: saving ? 'Salvando...' : 'Salvar Perfil'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"clinic\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Configura\\xe7\\xf5es da Cl\\xednica\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Configure hor\\xe1rios de funcionamento e prefer\\xeancias da cl\\xednica\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"clinic_name\",\n                                                    children: \"Nome da Cl\\xednica\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"clinic_name\",\n                                                    value: clinicSettings.clinic_name || '',\n                                                    onChange: (e)=>setClinicSettings({\n                                                            ...clinicSettings,\n                                                            clinic_name: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"working_hours_start\",\n                                                            children: \"Hor\\xe1rio de In\\xedcio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"working_hours_start\",\n                                                            type: \"time\",\n                                                            value: clinicSettings.working_hours_start,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    working_hours_start: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"working_hours_end\",\n                                                            children: \"Hor\\xe1rio de T\\xe9rmino\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"working_hours_end\",\n                                                            type: \"time\",\n                                                            value: clinicSettings.working_hours_end,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    working_hours_end: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"Dias de Funcionamento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        {\n                                                            value: 1,\n                                                            label: 'Dom'\n                                                        },\n                                                        {\n                                                            value: 2,\n                                                            label: 'Seg'\n                                                        },\n                                                        {\n                                                            value: 3,\n                                                            label: 'Ter'\n                                                        },\n                                                        {\n                                                            value: 4,\n                                                            label: 'Qua'\n                                                        },\n                                                        {\n                                                            value: 5,\n                                                            label: 'Qui'\n                                                        },\n                                                        {\n                                                            value: 6,\n                                                            label: 'Sex'\n                                                        },\n                                                        {\n                                                            value: 7,\n                                                            label: 'Sáb'\n                                                        }\n                                                    ].map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"button\",\n                                                            variant: clinicSettings.working_days.includes(day.value) ? 'default' : 'outline',\n                                                            size: \"sm\",\n                                                            onClick: ()=>{\n                                                                const newDays = clinicSettings.working_days.includes(day.value) ? clinicSettings.working_days.filter((d)=>d !== day.value) : [\n                                                                    ...clinicSettings.working_days,\n                                                                    day.value\n                                                                ];\n                                                                setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    working_days: newDays\n                                                                });\n                                                            },\n                                                            children: day.label\n                                                        }, day.value, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 21\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"appointment_duration\",\n                                                            children: \"Dura\\xe7\\xe3o Padr\\xe3o da Consulta (minutos)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"appointment_duration\",\n                                                            type: \"number\",\n                                                            min: \"15\",\n                                                            max: \"180\",\n                                                            step: \"15\",\n                                                            value: clinicSettings.appointment_duration_minutes,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    appointment_duration_minutes: parseInt(e.target.value) || 30\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"timezone\",\n                                                            children: \"Fuso Hor\\xe1rio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 503,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"timezone\",\n                                                            value: clinicSettings.timezone,\n                                                            onChange: (e)=>setClinicSettings({\n                                                                    ...clinicSettings,\n                                                                    timezone: e.target.value\n                                                                })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 502,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                    id: \"allow_weekend\",\n                                                    checked: clinicSettings.allow_weekend_appointments,\n                                                    onCheckedChange: (checked)=>setClinicSettings({\n                                                            ...clinicSettings,\n                                                            allow_weekend_appointments: checked\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"allow_weekend\",\n                                                    children: \"Permitir agendamentos nos fins de semana\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: saveClinicSettings,\n                                            disabled: saving,\n                                            children: saving ? 'Salvando...' : 'Salvar Configurações'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 415,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"professionals\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Profissionais de Sa\\xfade\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Gerencie os profissionais de sa\\xfade da sua cl\\xednica\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 537,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: healthcareProfessionals.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhum profissional cadastrado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                className: \"mt-4\",\n                                                children: \"Adicionar Profissional\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 543,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            healthcareProfessionals.map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center w-10 h-10 rounded-full bg-primary/10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 558,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"font-medium\",\n                                                                            children: professional.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                            lineNumber: 562,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: professional.specialty || 'Especialidade não informada'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                            lineNumber: 563,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        professional.crm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-muted-foreground\",\n                                                                            children: [\n                                                                                \"CRM: \",\n                                                                                professional.crm\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                                    variant: professional.is_active ? 'default' : 'secondary',\n                                                                    children: professional.is_active ? 'Ativo' : 'Inativo'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    children: \"Editar\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 575,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, professional.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 21\n                                                }, undefined)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Adicionar Profissional\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"integrations\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Integra\\xe7\\xf5es\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Configure integra\\xe7\\xf5es com WhatsApp e email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"WhatsApp Business API\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"whatsapp_token\",\n                                                                    children: \"Token de Acesso\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"whatsapp_token\",\n                                                                    type: \"password\",\n                                                                    value: integrations.whatsapp_token,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            whatsapp_token: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"whatsapp_phone\",\n                                                                    children: \"N\\xfamero do WhatsApp\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"whatsapp_phone\",\n                                                                    value: integrations.whatsapp_phone,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            whatsapp_phone: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 605,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Configura\\xe7\\xf5es de Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_host\",\n                                                                    children: \"Servidor SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_host\",\n                                                                    value: integrations.email_smtp_host,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_host: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 629,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_port\",\n                                                                    children: \"Porta SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 638,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_port\",\n                                                                    value: integrations.email_smtp_port,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_port: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 639,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_user\",\n                                                                    children: \"Usu\\xe1rio SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 648,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_user\",\n                                                                    value: integrations.email_smtp_user,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_user: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 649,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 647,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"email_smtp_password\",\n                                                                    children: \"Senha SMTP\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 656,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"email_smtp_password\",\n                                                                    type: \"password\",\n                                                                    value: integrations.email_smtp_password,\n                                                                    onChange: (e)=>setIntegrations({\n                                                                            ...integrations,\n                                                                            email_smtp_password: e.target.value\n                                                                        })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 646,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: saveIntegrations,\n                                            disabled: saving,\n                                            children: saving ? 'Salvando...' : 'Salvar Integrações'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                        value: \"security\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Shield_Smartphone_User_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Seguran\\xe7a\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Gerencie configura\\xe7\\xf5es de seguran\\xe7a da sua conta\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 681,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Alterar Senha\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"current_password\",\n                                                                    children: \"Senha Atual\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 690,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"current_password\",\n                                                                    type: \"password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 689,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"new_password\",\n                                                                    children: \"Nova Senha\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"new_password\",\n                                                                    type: \"password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 695,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"confirm_password\",\n                                                                    children: \"Confirmar Nova Senha\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"confirm_password\",\n                                                                    type: \"password\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 699,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 697,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 688,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    children: \"Alterar Senha\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 702,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Autentica\\xe7\\xe3o de Dois Fatores\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 708,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Ativar 2FA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-muted-foreground\",\n                                                                    children: \"Adicione uma camada extra de seguran\\xe7a \\xe0 sua conta\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"Sess\\xf5es Ativas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 721,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Sess\\xe3o atual\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: \"Windows • Chrome • S\\xe3o Paulo, BR\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                lineNumber: 724,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: \"Encerrar\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                                lineNumber: 728,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                        lineNumber: 723,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                                    lineNumber: 722,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n                lineNumber: 304,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\configuracoes\\\\page.tsx\",\n        lineNumber: 298,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SettingsPage, \"hTpNI+rM4rP03K6nmCjKQpqYjFk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/configuracoes/page.tsx\n"));

/***/ })

});