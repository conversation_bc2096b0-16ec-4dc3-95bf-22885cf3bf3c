"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/lib/validations.ts":
/*!********************************!*\
  !*** ./src/lib/validations.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appointmentSchema: () => (/* binding */ appointmentSchema),\n/* harmony export */   clinicSettingsSchema: () => (/* binding */ clinicSettingsSchema),\n/* harmony export */   fileUploadSchema: () => (/* binding */ fileUploadSchema),\n/* harmony export */   formatCPF: () => (/* binding */ formatCPF),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   healthcareProfessionalSchema: () => (/* binding */ healthcareProfessionalSchema),\n/* harmony export */   patientSchema: () => (/* binding */ patientSchema),\n/* harmony export */   procedureSchema: () => (/* binding */ procedureSchema),\n/* harmony export */   validateCPF: () => (/* binding */ validateCPF)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n\n// Utility schemas\nconst phoneRegex = /^(\\+55\\s?)?(\\(?\\d{2}\\)?\\s?)?\\d{4,5}-?\\d{4}$/;\nconst cpfRegex = /^\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}$|^\\d{11}$/;\nconst crmRegex = /^\\d{4,6}\\/[A-Z]{2}$/;\n// Patient validation schema\nconst patientSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100, 'Nome deve ter no máximo 100 caracteres').regex(/^[a-zA-ZÀ-ÿ\\s]+$/, 'Nome deve conter apenas letras e espaços'),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email('E-mail inválido').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    birth_date: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().refine((date)=>{\n        if (!date) return true;\n        const birthDate = new Date(date);\n        const today = new Date();\n        const age = today.getFullYear() - birthDate.getFullYear();\n        return age >= 0 && age <= 150;\n    }, 'Data de nascimento inválida'),\n    cpf: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(cpfRegex, 'CPF inválido. Use formato: 000.000.000-00').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(500, 'Endereço deve ter no máximo 500 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Observações devem ter no máximo 1000 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal(''))\n});\n// Healthcare Professional validation schema\nconst healthcareProfessionalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100, 'Nome deve ter no máximo 100 caracteres').regex(/^[a-zA-ZÀ-ÿ\\s]+$/, 'Nome deve conter apenas letras e espaços'),\n    specialty: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100, 'Especialidade deve ter no máximo 100 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    crm: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(crmRegex, 'CRM inválido. Use formato: 123456/SP').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email('E-mail inválido').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    is_active: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\n// Procedure validation schema\nconst procedureSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(200, 'Nome deve ter no máximo 200 caracteres'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Descrição deve ter no máximo 1000 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    default_price: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0, 'Preço deve ser maior ou igual a zero').max(99999.99, 'Preço deve ser menor que R$ 100.000,00').optional(),\n    duration_minutes: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Duração deve ser um número inteiro').min(5, 'Duração mínima é 5 minutos').max(480, 'Duração máxima é 8 horas').optional()\n});\n// Appointment validation schema\nconst appointmentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Título deve ter pelo menos 2 caracteres').max(200, 'Título deve ter no máximo 200 caracteres'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Descrição deve ter no máximo 1000 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    patient_id: zod__WEBPACK_IMPORTED_MODULE_0__.string().uuid('ID do paciente inválido'),\n    healthcare_professional_id: zod__WEBPACK_IMPORTED_MODULE_0__.string().uuid('ID do profissional inválido').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    start_time: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Data/hora de início é obrigatória').refine((val)=>{\n        console.log('[VALIDATION DEBUG] Validating start_time:', val);\n        // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format\n        const datetimeLocalRegex = /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}$/;\n        const isDatetimeLocal = datetimeLocalRegex.test(val);\n        const isValidDate = !isNaN(new Date(val).getTime());\n        console.log('[VALIDATION DEBUG] start_time validation:', {\n            val,\n            isDatetimeLocal,\n            isValidDate\n        });\n        return isDatetimeLocal || isValidDate;\n    }, 'Data/hora de início inválida'),\n    end_time: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Data/hora de fim é obrigatória').refine((val)=>{\n        console.log('[VALIDATION DEBUG] Validating end_time:', val);\n        // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format\n        const datetimeLocalRegex = /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}$/;\n        const isDatetimeLocal = datetimeLocalRegex.test(val);\n        const isValidDate = !isNaN(new Date(val).getTime());\n        console.log('[VALIDATION DEBUG] end_time validation:', {\n            val,\n            isDatetimeLocal,\n            isValidDate\n        });\n        return isDatetimeLocal || isValidDate;\n    }, 'Data/hora de fim inválida'),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'consultation',\n        'follow_up'\n    ], {\n        errorMap: ()=>({\n                message: 'Tipo de consulta inválido'\n            })\n    }),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'scheduled',\n        'confirmed',\n        'in_progress',\n        'completed',\n        'cancelled'\n    ], {\n        errorMap: ()=>({\n                message: 'Status da consulta inválido'\n            })\n    }).optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Observações devem ter no máximo 1000 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    // Recurrence fields\n    has_recurrence: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    recurrence_type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'daily',\n        'weekly',\n        'monthly'\n    ]).optional(),\n    recurrence_interval: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Intervalo deve ser um número inteiro').min(1, 'Intervalo mínimo é 1').max(12, 'Intervalo máximo é 12').optional(),\n    recurrence_days: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.number().int().min(1).max(7)).optional(),\n    recurrence_end_type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'never',\n        'date',\n        'count'\n    ]).optional(),\n    recurrence_end_date: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    recurrence_count: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Número de ocorrências deve ser um número inteiro').min(1, 'Mínimo 1 ocorrência').max(365, 'Máximo 365 ocorrências').optional()\n}).refine((data)=>{\n    // Validate that end_time is after start_time\n    try {\n        console.log('[DATE VALIDATION DEBUG] Validating dates:', {\n            start_time: data.start_time,\n            end_time: data.end_time,\n            start_time_type: typeof data.start_time,\n            end_time_type: typeof data.end_time\n        });\n        // Skip validation if either date is empty (will be caught by required validation)\n        if (!data.start_time || !data.end_time) {\n            console.log('[DATE VALIDATION DEBUG] One or both dates empty, skipping validation');\n            return true;\n        }\n        const start = new Date(data.start_time);\n        const end = new Date(data.end_time);\n        console.log('[DATE VALIDATION DEBUG] Parsed dates:', {\n            start: start.toISOString(),\n            end: end.toISOString(),\n            startTime: start.getTime(),\n            endTime: end.getTime(),\n            startValid: !isNaN(start.getTime()),\n            endValid: !isNaN(end.getTime())\n        });\n        // Check if dates are valid\n        if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n            console.log('[DATE VALIDATION DEBUG] Invalid date(s) detected');\n            return false;\n        }\n        const isValid = end > start;\n        console.log('[DATE VALIDATION DEBUG] Comparison result:', {\n            endGreaterThanStart: isValid,\n            timeDifference: end.getTime() - start.getTime()\n        });\n        return isValid;\n    } catch (error) {\n        console.error('[DATE VALIDATION DEBUG] Date validation error:', error);\n        return false;\n    }\n}, {\n    message: 'Data/hora de fim deve ser posterior à data/hora de início',\n    path: [\n        'end_time'\n    ]\n}).refine((data)=>{\n    // Validate recurrence fields when has_recurrence is true\n    if (data.has_recurrence) {\n        return data.recurrence_type && data.recurrence_interval;\n    }\n    return true;\n}, {\n    message: 'Campos de recorrência são obrigatórios quando recorrência está ativada',\n    path: [\n        'recurrence_type'\n    ]\n}).refine((data)=>{\n    // Validate recurrence end date when end_type is 'date'\n    if (data.has_recurrence && data.recurrence_end_type === 'date') {\n        return data.recurrence_end_date;\n    }\n    return true;\n}, {\n    message: 'Data de fim é obrigatória quando tipo de fim é \"data\"',\n    path: [\n        'recurrence_end_date'\n    ]\n}).refine((data)=>{\n    // Validate recurrence count when end_type is 'count'\n    if (data.has_recurrence && data.recurrence_end_type === 'count') {\n        return data.recurrence_count && data.recurrence_count > 0;\n    }\n    return true;\n}, {\n    message: 'Número de ocorrências é obrigatório quando tipo de fim é \"contagem\"',\n    path: [\n        'recurrence_count'\n    ]\n});\n// Clinic Settings validation schema\nconst clinicSettingsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clinic_name: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(200, 'Nome da clínica deve ter no máximo 200 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    working_hours_start: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de início inválido'),\n    working_hours_end: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de fim inválido'),\n    working_days: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.number().int().min(1).max(7)).min(1, 'Pelo menos um dia de funcionamento deve ser selecionado'),\n    appointment_duration_minutes: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Duração deve ser um número inteiro').min(15, 'Duração mínima é 15 minutos').max(180, 'Duração máxima é 3 horas'),\n    allow_weekend_appointments: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    timezone: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Fuso horário é obrigatório')\n}).refine((data)=>{\n    // Validate that end time is after start time\n    const [startHour, startMinute] = data.working_hours_start.split(':').map(Number);\n    const [endHour, endMinute] = data.working_hours_end.split(':').map(Number);\n    const startMinutes = startHour * 60 + startMinute;\n    const endMinutes = endHour * 60 + endMinute;\n    return endMinutes > startMinutes;\n}, {\n    message: 'Horário de fim deve ser posterior ao horário de início',\n    path: [\n        'working_hours_end'\n    ]\n});\n// File upload validation\nconst fileUploadSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    file: zod__WEBPACK_IMPORTED_MODULE_0__[\"instanceof\"](File).refine((file)=>file.size <= 50 * 1024 * 1024, 'Arquivo deve ter no máximo 50MB').refine((file)=>{\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/gif',\n            'application/pdf',\n            'text/plain',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        ];\n        return allowedTypes.includes(file.type);\n    }, 'Tipo de arquivo não permitido'),\n    patient_id: zod__WEBPACK_IMPORTED_MODULE_0__.string().uuid('ID do paciente inválido')\n});\n// Common validation utilities\nconst validateCPF = (cpf)=>{\n    // Remove formatting\n    const cleanCPF = cpf.replace(/[^\\d]/g, '');\n    if (cleanCPF.length !== 11) return false;\n    // Check for repeated digits\n    if (/^(\\d)\\1{10}$/.test(cleanCPF)) return false;\n    // Validate check digits\n    let sum = 0;\n    for(let i = 0; i < 9; i++){\n        sum += parseInt(cleanCPF.charAt(i)) * (10 - i);\n    }\n    let remainder = sum * 10 % 11;\n    if (remainder === 10 || remainder === 11) remainder = 0;\n    if (remainder !== parseInt(cleanCPF.charAt(9))) return false;\n    sum = 0;\n    for(let i = 0; i < 10; i++){\n        sum += parseInt(cleanCPF.charAt(i)) * (11 - i);\n    }\n    remainder = sum * 10 % 11;\n    if (remainder === 10 || remainder === 11) remainder = 0;\n    if (remainder !== parseInt(cleanCPF.charAt(10))) return false;\n    return true;\n};\nconst formatCPF = (cpf)=>{\n    const cleanCPF = cpf.replace(/[^\\d]/g, '');\n    return cleanCPF.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, '$1.$2.$3-$4');\n};\nconst formatPhone = (phone)=>{\n    const cleanPhone = phone.replace(/[^\\d]/g, '');\n    if (cleanPhone.length === 11) {\n        return cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n    } else if (cleanPhone.length === 10) {\n        return cleanPhone.replace(/(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3');\n    }\n    return phone;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/validations.ts\n"));

/***/ })

});