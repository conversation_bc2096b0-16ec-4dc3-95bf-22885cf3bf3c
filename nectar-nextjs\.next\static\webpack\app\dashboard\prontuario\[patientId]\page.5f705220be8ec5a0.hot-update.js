"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/prontuario/[patientId]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/prontuario/[patientId]/page.tsx":
/*!***********************************************************!*\
  !*** ./src/app/dashboard/prontuario/[patientId]/page.tsx ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,CheckCircle,FileText,Save,Stethoscope,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst MedicalRecordPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const patientId = params.patientId;\n    const appointmentId = searchParams.get('appointment_id');\n    const [patient, setPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [appointment, setAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecords, setMedicalRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentRecord, setCurrentRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [saving, setSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [autoSaving, setAutoSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDraft, setIsDraft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicalRecordPage.useEffect\": ()=>{\n            if (patientId) {\n                fetchPatientData();\n                if (appointmentId) {\n                    fetchAppointmentData();\n                    fetchMedicalRecords();\n                }\n            }\n        }\n    }[\"MedicalRecordPage.useEffect\"], [\n        patientId,\n        appointmentId\n    ]);\n    // Auto-save functionality - 2 minutes of inactivity\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MedicalRecordPage.useEffect\": ()=>{\n            if (currentRecord.trim() && appointmentId && !saving) {\n                const autoSaveTimer = setTimeout({\n                    \"MedicalRecordPage.useEffect.autoSaveTimer\": ()=>{\n                        handleSaveDraft();\n                    }\n                }[\"MedicalRecordPage.useEffect.autoSaveTimer\"], 120000); // Auto-save after 2 minutes of inactivity\n                return ({\n                    \"MedicalRecordPage.useEffect\": ()=>clearTimeout(autoSaveTimer)\n                })[\"MedicalRecordPage.useEffect\"];\n            }\n        }\n    }[\"MedicalRecordPage.useEffect\"], [\n        currentRecord,\n        appointmentId,\n        saving\n    ]);\n    const fetchPatientData = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch patient data');\n            const result = await response.json();\n            setPatient(result.data || result);\n        } catch (error) {\n            console.error('Error fetching patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados do paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchAppointmentData = async ()=>{\n        if (!appointmentId) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch appointment data');\n            const result = await response.json();\n            setAppointment(result.data || result);\n        } catch (error) {\n            console.error('Error fetching appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchMedicalRecords = async ()=>{\n        if (!appointmentId) return;\n        try {\n            setLoading(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/medical-records?appointment_id=\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch medical records');\n            const result = await response.json();\n            const records = result.data || result;\n            setMedicalRecords(Array.isArray(records) ? records : []);\n            // Check if there's a draft record\n            const draftRecord = records.find((record)=>record.is_draft);\n            if (draftRecord) {\n                setCurrentRecord(draftRecord.notes);\n                setIsDraft(true);\n            }\n        } catch (error) {\n            console.error('Error fetching medical records:', error);\n            setMedicalRecords([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSaveDraft = async ()=>{\n        if (!currentRecord.trim() || !appointmentId || saving) return;\n        try {\n            setAutoSaving(true);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId,\n                    patient_id: patientId,\n                    notes: currentRecord.trim(),\n                    is_draft: true\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.message || 'Failed to save draft');\n            }\n            setLastSaved(new Date());\n            setIsDraft(true);\n            toast({\n                title: \"Rascunho salvo\",\n                description: \"Suas alterações foram salvas automaticamente.\"\n            });\n        } catch (error) {\n            console.error('Error saving draft:', error);\n            toast({\n                title: \"Erro ao salvar rascunho\",\n                description: \"Não foi possível salvar o rascunho. Suas alterações podem ser perdidas.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setAutoSaving(false);\n        }\n    };\n    const cleanupDrafts = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/medical-records/cleanup-drafts\", {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId\n                })\n            });\n            if (!response.ok) {\n                console.warn('Failed to cleanup drafts, but continuing with completion');\n            }\n        } catch (error) {\n            console.warn('Error cleaning up drafts:', error);\n        // Don't throw error here - draft cleanup failure shouldn't prevent completion\n        }\n    };\n    const handleCompleteConsultation = async ()=>{\n        if (!currentRecord.trim() || !appointmentId) {\n            toast({\n                title: \"Erro\",\n                description: \"Por favor, adicione suas observações antes de finalizar.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            setSaving(true);\n            // Save final medical record\n            const recordResponse = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: appointmentId,\n                    patient_id: patientId,\n                    notes: currentRecord.trim(),\n                    is_draft: false\n                })\n            });\n            if (!recordResponse.ok) {\n                const errorData = await recordResponse.json().catch(()=>({}));\n                throw new Error(errorData.message || 'Failed to save medical record');\n            }\n            // Update appointment status to completed\n            const appointmentResponse = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_10__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status: 'completed'\n                })\n            });\n            if (!appointmentResponse.ok) {\n                const errorData = await appointmentResponse.json().catch(()=>({}));\n                throw new Error(errorData.message || 'Failed to update appointment status');\n            }\n            // Clean up draft records AFTER successful completion\n            await cleanupDrafts(appointmentId);\n            toast({\n                title: \"Sucesso!\",\n                description: \"Atendimento finalizado com sucesso.\"\n            });\n            // Navigate back to agenda\n            router.push('/dashboard/agenda');\n        } catch (error) {\n            console.error('Error completing consultation:', error);\n            toast({\n                title: \"Erro\",\n                description: error instanceof Error ? error.message : \"Erro ao finalizar atendimento.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setSaving(false);\n        }\n    };\n    const calculateAge = (birthDate)=>{\n        if (!birthDate) return 'Não informado';\n        const birth = new Date(birthDate);\n        const today = new Date();\n        let age = today.getFullYear() - birth.getFullYear();\n        const monthDiff = today.getMonth() - birth.getMonth();\n        if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birth.getDate()) {\n            age--;\n        }\n        return \"\".concat(age, \" anos\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-muted-foreground\",\n                        children: \"Carregando prontu\\xe1rio...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n            lineNumber: 305,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 max-w-6xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Voltar\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Prontu\\xe1rio\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: (patient === null || patient === void 0 ? void 0 : patient.name) && \"Hist\\xf3rico m\\xe9dico de \".concat(patient.name)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, undefined),\n            patient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Informa\\xe7\\xf5es do Paciente\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Nome\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.email || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Telefone\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.phone || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Data de Nascimento\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.birth_date ? (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateBR)(patient.birth_date) : 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Idade\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: calculateAge(patient.birth_date)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"CPF\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: patient.cpf || 'Não informado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, undefined),\n            appointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Informa\\xe7\\xf5es da Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"T\\xedtulo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Data\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateBR)(appointment.start_time)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Hor\\xe1rio\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: [\n                                                (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.start_time),\n                                                \" - \",\n                                                (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(appointment.end_time)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: appointment.status === 'completed' ? 'secondary' : appointment.status === 'in_progress' ? 'default' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                            children: [\n                                                appointment.status === 'scheduled' && 'Agendado',\n                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                appointment.status === 'completed' && 'Concluído',\n                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 15\n                                }, undefined),\n                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profissional\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.healthcare_professional_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 17\n                                }, undefined),\n                                appointment.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm\",\n                                            children: appointment.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 378,\n                columnNumber: 9\n            }, undefined),\n            appointmentId && (appointment === null || appointment === void 0 ? void 0 : appointment.status) !== 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"Nova Anota\\xe7\\xe3o\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Adicione uma nova entrada ao prontu\\xe1rio do paciente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                    children: [\n                                        autoSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-3 w-3 border-b border-primary\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        lastSaved && !autoSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-3 w-3 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Salvo \\xe0s \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatTimeBR)(lastSaved.toISOString())\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        isDraft && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: \"Rascunho\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 438,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n                                        htmlFor: \"medical-notes\",\n                                        children: \"Observa\\xe7\\xf5es M\\xe9dicas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        id: \"medical-notes\",\n                                        placeholder: \"Digite suas observa\\xe7\\xf5es sobre a consulta, diagn\\xf3stico, tratamento recomendado, etc...\",\n                                        value: currentRecord,\n                                        onChange: (e)=>setCurrentRecord(e.target.value),\n                                        rows: 8,\n                                        className: \"min-h-[200px]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: \"As anota\\xe7\\xf5es s\\xe3o automaticamente criptografadas e salvas como rascunho a cada 5 segundos.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleSaveDraft,\n                                        disabled: !currentRecord.trim() || autoSaving,\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Salvar Rascunho\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleCompleteConsultation,\n                                        disabled: !currentRecord.trim() || saving,\n                                        className: \"bg-green-600 hover:bg-green-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            saving ? 'Finalizando...' : 'Finalizar Atendimento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 496,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 437,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                        lineNumber: 513,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Hist\\xf3rico de Anota\\xe7\\xf5es\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: appointmentId ? 'Registros médicos desta consulta' : 'Histórico completo de registros médicos do paciente'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: medicalRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_CheckCircle_FileText_Save_Stethoscope_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Nenhum registro m\\xe9dico encontrado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: medicalRecords.filter((record)=>!record.is_draft) // Only show finalized records\n                            .map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border-l-4 border-primary pl-4 py-3 bg-muted/30 rounded-r-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateTimeBR)(record.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"outline\",\n                                                            className: \"text-xs\",\n                                                            children: record.created_by_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                record.updated_at && record.updated_at !== record.created_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: [\n                                                        \"Editado em \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_9__.formatDateTimeBR)(record.updated_at)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose prose-sm max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                                children: record.notes\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                            lineNumber: 550,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, record.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n                lineNumber: 510,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\prontuario\\\\[patientId]\\\\page.tsx\",\n        lineNumber: 315,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MedicalRecordPage, \"Gd55zBCyJLy2D5fiJMb/bjlVCS4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = MedicalRecordPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MedicalRecordPage);\nvar _c;\n$RefreshReg$(_c, \"MedicalRecordPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/prontuario/[patientId]/page.tsx\n"));

/***/ })

});