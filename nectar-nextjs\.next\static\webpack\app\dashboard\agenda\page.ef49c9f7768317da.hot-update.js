"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/AppointmentForm.tsx":
/*!********************************************!*\
  !*** ./src/components/AppointmentForm.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/datetime-input */ \"(app-pages-browser)/./src/components/ui/datetime-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,Plus,Search,Stethoscope,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=addMinutes!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/addMinutes.js\");\n/* harmony import */ var _lib_validations__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/validations */ \"(app-pages-browser)/./src/lib/validations.ts\");\n/* harmony import */ var _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/hooks/useAsyncOperation */ \"(app-pages-browser)/./src/hooks/useAsyncOperation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Utility functions for Brazilian timezone (UTC-3)\nconst brazilianTimezoneOffset = -3 * 60; // -3 hours in minutes\nconst toBrazilianTime = (date)=>{\n    if (!date) return null;\n    // Se for string, converte para Date\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    // Cria uma nova data ajustada para UTC-3\n    const utcTime = dateObj.getTime() + dateObj.getTimezoneOffset() * 60000;\n    const brazilianTime = new Date(utcTime + brazilianTimezoneOffset * 60000);\n    return brazilianTime;\n};\nconst formatDateTimeForBrazilianInput = (dateTime)=>{\n    if (!dateTime) return '';\n    try {\n        const date = toBrazilianTime(dateTime);\n        if (!date || isNaN(date.getTime())) return '';\n        // Formato para datetime-local: YYYY-MM-DDTHH:mm\n        const year = date.getFullYear();\n        const month = String(date.getMonth() + 1).padStart(2, '0');\n        const day = String(date.getDate()).padStart(2, '0');\n        const hours = String(date.getHours()).padStart(2, '0');\n        const minutes = String(date.getMinutes()).padStart(2, '0');\n        return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \"T\").concat(hours, \":\").concat(minutes);\n    } catch (error) {\n        console.error('[DATETIME] Error formatting date:', error);\n        return '';\n    }\n};\nconst parseBrazilianDateTime = (dateTimeString)=>{\n    if (!dateTimeString) return null;\n    try {\n        // Se já tem timezone, usa diretamente\n        if (dateTimeString.includes('T') && (dateTimeString.includes('Z') || dateTimeString.includes('-03:00'))) {\n            return new Date(dateTimeString);\n        }\n        // Se é formato datetime-local (YYYY-MM-DDTHH:mm), adiciona timezone brasileiro\n        if (dateTimeString.includes('T')) {\n            return new Date(dateTimeString + ':00-03:00');\n        }\n        // Fallback para outros formatos\n        const date = new Date(dateTimeString);\n        return toBrazilianTime(date);\n    } catch (error) {\n        console.error('[DATETIME] Error parsing date:', error);\n        return null;\n    }\n};\nconst AppointmentForm = (param)=>{\n    let { open, onOpenChange, patients, healthcareProfessionals, procedures, initialData, onSubmit, loading = false } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('scheduling');\n    const [searchProcedure, setSearchProcedure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedProcedures, setSelectedProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Check if we're in editing mode\n    const isEditing = Boolean(initialData === null || initialData === void 0 ? void 0 : initialData.id);\n    const { execute: submitForm, loading: submitting } = (0,_hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission)({\n        successMessage: isEditing ? 'Consulta atualizada com sucesso!' : 'Consulta agendada com sucesso!',\n        errorMessage: isEditing ? 'Erro ao atualizar consulta' : 'Erro ao agendar consulta'\n    });\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations__WEBPACK_IMPORTED_MODULE_16__.appointmentSchema),\n        defaultValues: {\n            title: '',\n            description: '',\n            patient_id: '',\n            healthcare_professional_id: '',\n            start_time: '',\n            end_time: '',\n            type: 'consultation',\n            status: 'scheduled',\n            notes: '',\n            has_recurrence: false,\n            recurrence_type: 'weekly',\n            recurrence_interval: 1,\n            recurrence_days: [],\n            recurrence_end_type: 'never',\n            recurrence_end_date: '',\n            recurrence_count: 1\n        }\n    });\n    const { control, handleSubmit, watch, setValue, reset, formState: { errors } } = form;\n    const watchedValues = watch();\n    // Initialize form with initial data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (initialData && open) {\n                console.log('[DATETIME] Initializing form with data:', initialData);\n                const formattedData = {\n                    ...initialData,\n                    start_time: formatDateTimeForBrazilianInput(initialData.start_time),\n                    end_time: formatDateTimeForBrazilianInput(initialData.end_time),\n                    patient_id: initialData.patient_id || '',\n                    healthcare_professional_id: initialData.healthcare_professional_id || ''\n                };\n                console.log('[DATETIME] Formatted data for form:', formattedData);\n                reset(formattedData);\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        initialData,\n        open,\n        reset\n    ]);\n    // Auto-generate title when patient and type change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            if (watchedValues.patient_id && watchedValues.type) {\n                const patient = patients.find({\n                    \"AppointmentForm.useEffect.patient\": (p)=>p.id === watchedValues.patient_id\n                }[\"AppointmentForm.useEffect.patient\"]);\n                if (patient) {\n                    const typeLabel = watchedValues.type === 'consultation' ? 'Consulta' : 'Retorno';\n                    setValue('title', \"\".concat(typeLabel, \" - \").concat(patient.name));\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.patient_id,\n        watchedValues.type,\n        patients,\n        setValue\n    ]);\n    // Auto-calculate end time based on start time (Brazilian timezone)\n    // Only auto-calculate if end_time is not already set (e.g., from FullCalendar selection)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppointmentForm.useEffect\": ()=>{\n            console.log('[DATETIME] Auto-calculating end time, start_time:', watchedValues.start_time, 'end_time:', watchedValues.end_time);\n            if (watchedValues.start_time && !watchedValues.end_time) {\n                try {\n                    const startTime = parseBrazilianDateTime(watchedValues.start_time);\n                    console.log('[DATETIME] Parsed start time:', startTime, 'Valid:', startTime && !isNaN(startTime.getTime()));\n                    if (startTime && !isNaN(startTime.getTime())) {\n                        const totalDuration = selectedProcedures.reduce({\n                            \"AppointmentForm.useEffect.totalDuration\": (total, proc)=>{\n                                const procedure = procedures.find({\n                                    \"AppointmentForm.useEffect.totalDuration.procedure\": (p)=>p.id === proc.procedure_id\n                                }[\"AppointmentForm.useEffect.totalDuration.procedure\"]);\n                                return total + ((procedure === null || procedure === void 0 ? void 0 : procedure.duration_minutes) || 30) * proc.quantity;\n                            }\n                        }[\"AppointmentForm.useEffect.totalDuration\"], 30); // Default 30 minutes if no procedures\n                        console.log('[DATETIME] Total duration:', totalDuration, 'minutes');\n                        const endTime = (0,_barrel_optimize_names_addMinutes_date_fns__WEBPACK_IMPORTED_MODULE_19__.addMinutes)(startTime, totalDuration);\n                        const formattedEndTime = formatDateTimeForBrazilianInput(endTime);\n                        console.log('[DATETIME] Calculated end time:', endTime, 'Formatted:', formattedEndTime);\n                        setValue('end_time', formattedEndTime);\n                    }\n                } catch (error) {\n                    console.error('[DATETIME] Error calculating end time:', error);\n                }\n            }\n        }\n    }[\"AppointmentForm.useEffect\"], [\n        watchedValues.start_time,\n        watchedValues.end_time,\n        selectedProcedures,\n        procedures,\n        setValue\n    ]);\n    const filteredProcedures = procedures.filter((procedure)=>procedure.name.toLowerCase().includes(searchProcedure.toLowerCase()) || procedure.description && procedure.description.toLowerCase().includes(searchProcedure.toLowerCase()));\n    const addProcedure = (procedure)=>{\n        const existingIndex = selectedProcedures.findIndex((p)=>p.procedure_id === procedure.id);\n        if (existingIndex >= 0) {\n            // Increase quantity if already exists\n            const updated = [\n                ...selectedProcedures\n            ];\n            updated[existingIndex].quantity += 1;\n            updated[existingIndex].total_price = updated[existingIndex].quantity * updated[existingIndex].unit_price;\n            setSelectedProcedures(updated);\n        } else {\n            // Add new procedure\n            const newProcedure = {\n                procedure_id: procedure.id,\n                procedure_name: procedure.name,\n                quantity: 1,\n                unit_price: procedure.default_price || 0,\n                total_price: procedure.default_price || 0\n            };\n            setSelectedProcedures([\n                ...selectedProcedures,\n                newProcedure\n            ]);\n        }\n    };\n    const updateProcedure = (index, field, value)=>{\n        const updated = [\n            ...selectedProcedures\n        ];\n        updated[index][field] = value;\n        updated[index].total_price = updated[index].quantity * updated[index].unit_price;\n        setSelectedProcedures(updated);\n    };\n    const removeProcedure = (index)=>{\n        setSelectedProcedures(selectedProcedures.filter((_, i)=>i !== index));\n    };\n    const getTotalPrice = ()=>{\n        return selectedProcedures.reduce((total, proc)=>total + proc.total_price, 0);\n    };\n    const onFormSubmit = async (data)=>{\n        console.log('[DATETIME] Form submitted with data:', data);\n        try {\n            // Parse and validate dates in Brazilian timezone\n            const startTime = parseBrazilianDateTime(data.start_time);\n            const endTime = parseBrazilianDateTime(data.end_time);\n            if (!startTime || !endTime) {\n                toast({\n                    title: 'Erro',\n                    description: 'Datas inválidas. Por favor, verifique os horários.',\n                    variant: 'destructive'\n                });\n                return;\n            }\n            // Convert to ISO string with Brazilian timezone\n            const formattedData = {\n                ...data,\n                start_time: startTime.toISOString(),\n                end_time: endTime.toISOString()\n            };\n            console.log('[DATETIME] Formatted data for submission:', formattedData);\n            await submitForm(async ()=>{\n                const appointmentData = {\n                    ...formattedData,\n                    procedures: selectedProcedures,\n                    total_price: getTotalPrice(),\n                    status: formattedData.status || 'scheduled',\n                    ...isEditing && (initialData === null || initialData === void 0 ? void 0 : initialData.id) && {\n                        id: initialData.id\n                    }\n                };\n                console.log('[DATETIME] Final appointment data:', appointmentData);\n                await onSubmit(appointmentData);\n                // Reset form\n                reset();\n                setSelectedProcedures([]);\n                setActiveTab('scheduling');\n                onOpenChange(false);\n            });\n        } catch (error) {\n            console.error('[DATETIME] Error submitting form:', error);\n            toast({\n                title: 'Erro',\n                description: 'Erro ao processar os dados. Por favor, tente novamente.',\n                variant: 'destructive'\n            });\n        }\n    };\n    const toggleRecurrenceDay = (day)=>{\n        const currentDays = watchedValues.recurrence_days || [];\n        const newDays = currentDays.includes(day) ? currentDays.filter((d)=>d !== day) : [\n            ...currentDays,\n            day\n        ];\n        setValue('recurrence_days', newDays);\n    };\n    const weekDays = [\n        {\n            value: 1,\n            label: 'D'\n        },\n        {\n            value: 2,\n            label: 'S'\n        },\n        {\n            value: 3,\n            label: 'T'\n        },\n        {\n            value: 4,\n            label: 'Q'\n        },\n        {\n            value: 5,\n            label: 'Q'\n        },\n        {\n            value: 6,\n            label: 'S'\n        },\n        {\n            value: 7,\n            label: 'S'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-[800px] max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: isEditing ? 'Editar Consulta' : 'Agendar Nova Consulta'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogDescription, {\n                            children: isEditing ? 'Edite os dados da consulta' : 'Preencha os dados para agendar uma nova consulta'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit(onFormSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                    className: \"grid w-full grid-cols-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"scheduling\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Agendamento\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                            value: \"procedures\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Procedimentos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"scheduling\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"patient\",\n                                                            children: \"Paciente *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"patient_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o paciente\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 383,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 382,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: patients.map((patient)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: patient.id,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                                className: \"h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                lineNumber: 389,\n                                                                                                columnNumber: 33\n                                                                                            }, void 0),\n                                                                                            patient.name\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 388,\n                                                                                        columnNumber: 31\n                                                                                    }, void 0)\n                                                                                }, patient.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 387,\n                                                                                    columnNumber: 29\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 385,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.patient_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.patient_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"professional\",\n                                                            children: \"Profissional\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"healthcare_professional_id\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                                placeholder: \"Selecione o profissional\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 413,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: professional.id,\n                                                                                    children: [\n                                                                                        professional.name,\n                                                                                        professional.specialty && \" - \".concat(professional.specialty)\n                                                                                    ]\n                                                                                }, professional.id, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 420,\n                                                                                    columnNumber: 31\n                                                                                }, void 0))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 416,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.healthcare_professional_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.healthcare_professional_id.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"type\",\n                                                            children: \"Tipo de Consulta *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"type\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                    value: field.value,\n                                                                    onValueChange: field.onChange,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 27\n                                                                            }, void 0)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 25\n                                                                        }, void 0),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"consultation\",\n                                                                                    children: \"Consulta\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 27\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                    value: \"follow_up\",\n                                                                                    children: \"Retorno\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 27\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 25\n                                                                        }, void 0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.type.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 439,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"title\",\n                                                            children: \"T\\xedtulo *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"title\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"title\",\n                                                                    ...field\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 478,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.title.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"start_time\",\n                                                            children: \"Data/Hora In\\xedcio *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"start_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"start_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 492,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.start_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.start_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"end_time\",\n                                                            children: \"Data/Hora Fim *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                            name: \"end_time\",\n                                                            control: control,\n                                                            render: (param)=>{\n                                                                let { field } = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_datetime_input__WEBPACK_IMPORTED_MODULE_14__.DateTimeInput, {\n                                                                    id: \"end_time\",\n                                                                    value: field.value,\n                                                                    onChange: field.onChange,\n                                                                    placeholder: \"DD/MM/AAAA HH:mm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 23\n                                                                }, void 0);\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.end_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-destructive flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.end_time.message\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"description\",\n                                                    children: \"Descri\\xe7\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                    name: \"description\",\n                                                    control: control,\n                                                    render: (param)=>{\n                                                        let { field } = param;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                            id: \"description\",\n                                                            placeholder: \"Descri\\xe7\\xe3o da consulta...\",\n                                                            ...field\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 21\n                                                        }, void 0);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-destructive flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        errors.description.message\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                name: \"has_recurrence\",\n                                                                control: control,\n                                                                render: (param)=>{\n                                                                    let { field } = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_12__.Checkbox, {\n                                                                        id: \"has_recurrence\",\n                                                                        checked: field.value,\n                                                                        onCheckedChange: field.onChange\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 560,\n                                                                        columnNumber: 25\n                                                                    }, void 0);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 556,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"has_recurrence\",\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Recorr\\xeancia Personalizada\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                watchedValues.has_recurrence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir a cada:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_interval\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                            type: \"number\",\n                                                                                            min: \"1\",\n                                                                                            ...field,\n                                                                                            onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                            className: \"w-20\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 583,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 579,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                    name: \"recurrence_type\",\n                                                                                    control: control,\n                                                                                    render: (param)=>{\n                                                                                        let { field } = param;\n                                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                                                            value: field.value,\n                                                                                            onValueChange: field.onChange,\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {}, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                        lineNumber: 598,\n                                                                                                        columnNumber: 35\n                                                                                                    }, void 0)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 597,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"daily\",\n                                                                                                            children: \"dia(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 601,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"weekly\",\n                                                                                                            children: \"semana(s)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 602,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                                                            value: \"monthly\",\n                                                                                                            children: \"m\\xeas(es)\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                            lineNumber: 603,\n                                                                                                            columnNumber: 35\n                                                                                                        }, void 0)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 600,\n                                                                                                    columnNumber: 33\n                                                                                                }, void 0)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 596,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 592,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                watchedValues.recurrence_type === 'weekly' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Repetir:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 613,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex gap-1\",\n                                                                            children: weekDays.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    type: \"button\",\n                                                                                    variant: (watchedValues.recurrence_days || []).includes(day.value) ? 'default' : 'outline',\n                                                                                    size: \"sm\",\n                                                                                    className: \"w-8 h-8 p-0\",\n                                                                                    onClick: ()=>toggleRecurrenceDay(day.value),\n                                                                                    children: day.label\n                                                                                }, day.value, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 616,\n                                                                                    columnNumber: 31\n                                                                                }, undefined))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 614,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 612,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Termina em:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 633,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                    name: \"recurrence_end_type\",\n                                                                    control: control,\n                                                                    render: (param)=>{\n                                                                        let { field } = param;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroup, {\n                                                                            value: field.value,\n                                                                            onValueChange: field.onChange,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"never\",\n                                                                                            id: \"never\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 641,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"never\",\n                                                                                            children: \"Nunca\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 642,\n                                                                                            columnNumber: 27\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 640,\n                                                                                    columnNumber: 25\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"date\",\n                                                                                            id: \"end_date\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 646,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_date\",\n                                                                                            children: \"Em\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 647,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_end_date\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"date\",\n                                                                                                    ...field,\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'date',\n                                                                                                    className: \"w-40\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 652,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 648,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 645,\n                                                                                    columnNumber: 29\n                                                                                }, void 0),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_13__.RadioGroupItem, {\n                                                                                            value: \"count\",\n                                                                                            id: \"end_count\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 663,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            htmlFor: \"end_count\",\n                                                                                            children: \"Ap\\xf3s\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 664,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_18__.Controller, {\n                                                                                            name: \"recurrence_count\",\n                                                                                            control: control,\n                                                                                            render: (param)=>{\n                                                                                                let { field } = param;\n                                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                                    type: \"number\",\n                                                                                                    min: \"1\",\n                                                                                                    ...field,\n                                                                                                    onChange: (e)=>field.onChange(parseInt(e.target.value) || 1),\n                                                                                                    disabled: watchedValues.recurrence_end_type !== 'count',\n                                                                                                    className: \"w-20\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                                    lineNumber: 669,\n                                                                                                    columnNumber: 35\n                                                                                                }, void 0);\n                                                                                            }\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 665,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                            children: \"ocorr\\xeancias\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                            lineNumber: 679,\n                                                                                            columnNumber: 31\n                                                                                        }, void 0)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 662,\n                                                                                    columnNumber: 29\n                                                                                }, void 0)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 638,\n                                                                            columnNumber: 27\n                                                                        }, void 0);\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 634,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                    value: \"procedures\",\n                                    className: \"space-y-4 mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 693,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        placeholder: \"Buscar procedimentos...\",\n                                                        value: searchProcedure,\n                                                        onChange: (e)=>setSearchProcedure(e.target.value),\n                                                        className: \"flex-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto\",\n                                                children: filteredProcedures.map((procedure)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                        className: \"cursor-pointer hover:bg-accent/50\",\n                                                        onClick: ()=>addProcedure(procedure),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                            className: \"p-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-sm\",\n                                                                                children: procedure.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            procedure.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs text-muted-foreground mt-1\",\n                                                                                children: procedure.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 710,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-2 mt-2\",\n                                                                                children: [\n                                                                                    procedure.default_price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"secondary\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            \"R$ \",\n                                                                                            procedure.default_price.toFixed(2)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 714,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    procedure.duration_minutes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_11__.Badge, {\n                                                                                        variant: \"outline\",\n                                                                                        className: \"text-xs\",\n                                                                                        children: [\n                                                                                            procedure.duration_minutes,\n                                                                                            \"min\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                        lineNumber: 719,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 712,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        className: \"h-8 w-8 p-0\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 726,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, procedure.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            selectedProcedures.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardHeader, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardTitle, {\n                                                            className: \"text-lg\",\n                                                            children: \"Procedimentos Selecionados\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_10__.CardContent, {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            selectedProcedures.map((proc, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3 p-3 border rounded-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium\",\n                                                                                children: proc.procedure_name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 743,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Qtd:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 747,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    value: proc.quantity,\n                                                                                    onChange: (e)=>updateProcedure(index, 'quantity', parseInt(e.target.value) || 1),\n                                                                                    className: \"w-16 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 748,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                                    className: \"text-xs\",\n                                                                                    children: \"Pre\\xe7o:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 758,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                                    type: \"number\",\n                                                                                    min: \"0\",\n                                                                                    step: \"0.01\",\n                                                                                    value: proc.unit_price,\n                                                                                    onChange: (e)=>updateProcedure(index, 'unit_price', parseFloat(e.target.value) || 0),\n                                                                                    className: \"w-24 h-8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                    lineNumber: 759,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 757,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                proc.total_price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 769,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            type: \"button\",\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            onClick: ()=>removeProcedure(index),\n                                                                            className: \"h-8 w-8 p-0 text-destructive hover:text-destructive\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_Plus_Search_Stethoscope_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                                lineNumber: 780,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                            lineNumber: 773,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 25\n                                                                }, undefined)),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center pt-3 border-t\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Total:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 786,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold\",\n                                                                        children: [\n                                                                            \"R$ \",\n                                                                            getTotalPrice().toFixed(2)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                        lineNumber: 787,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                                lineNumber: 785,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: ()=>onOpenChange(false),\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    disabled: submitting || loading,\n                                    children: submitting ? isEditing ? 'Salvando...' : 'Agendando...' : isEditing ? 'Salvar Alterações' : 'Agendar Consulta'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                                    lineNumber: 800,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                            lineNumber: 796,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n            lineNumber: 352,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\AppointmentForm.tsx\",\n        lineNumber: 351,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AppointmentForm, \"0fK7CMPQBa8UlSZ3bOvox60mpoM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast,\n        _hooks_useAsyncOperation__WEBPACK_IMPORTED_MODULE_17__.useFormSubmission,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_18__.useForm\n    ];\n});\n_c = AppointmentForm;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppointmentForm);\nvar _c;\n$RefreshReg$(_c, \"AppointmentForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AppointmentForm.tsx\n"));

/***/ })

});